## 调用方式

```vue
<DictsResource
  :form="form"
  :dicts="dictsData"
  :props="[
    {name: '数据来源', key: 'dataSource', hide: false},
    {name: '板块', key: 'segment', hide: false},
    {name: '细分市场一', key: 'subMarket1'},
    {name: '细分市场二', key: 'subMarket2'}
  ]"
  :propsEngineFactory="{name: '发动机厂', key: 'engineFactory', show: false}"
  :propsFuelType="{name: '燃料', key: 'fuelType', show: true, type: 'B'}"
  :span="3"
  :xs="8"
  :sm="8"
  :md="3"
/>
```

# DictsResource 组件文档

## 属性 (Props)

| 属性名               | 说明                 | 类型    | 可选值 | 默认值                                                                 |
|----------------------|----------------------|---------|--------|------------------------------------------------------------------------|
| dicts                | 数据字典             | Array   | -      | `[]`                                                                  |
| props                | 表单key值配置        | Array   | -      | `[{name: '数据来源', key: 'dataSource', hide: false}, ...]`          |
| propsEngineFactory   | 发动机厂参数控制     | Object  | -      | `{name: '发动机厂', key: 'engineFactory', show: true}`               |
| propsManuFacturer    | 主机厂参数控制       | Object  | -      | `{name: '主机厂', key: 'manuFacturer', show: true}`                  |
| propsFuelType        | 燃料参数控制         | Object  | -      | `{name: '燃料', key: 'fuelType', show: true, type: 'B'}`             |
| propsBreed           | 品系参数控制         | Object  | -      | `{name: '品系', key: 'breed', show: false, disabled: false}`        |
| propsWeightMidLight  | 重中轻参数控制       | Object  | -      | `{name: '重中轻', key: 'vehicleType', show: false, disabled: false}` |
| form                 | 绑定的表单对象       | Object  | -      | `{}`                                                                 |
| span                 | 栅格占据的列数       | Number  | -      | `4`                                                                  |
| xs                   | <768px 响应式栅格    | Number  | -      | `8`                                                                  |
| sm                   | ≥768px 响应式栅格    | Number  | -      | `8`                                                                  |
| md                   | ≥992px 响应式栅格    | Number  | -      | `4`                                                                  |

## 事件 (Emits)

| 事件名             | 说明                     | 参数                      |
|--------------------|--------------------------|---------------------------|
| dictsManuFacturer  | 主机厂数据变化时触发     | `(value: Array)` - 主机厂数据 |

## 数据字典结构

组件期望的 `dicts` 数据结构：

```javascript
{
  level1: [],               // 第一级数据
  level2: [],               // 第二级数据 
  level3: [],               // 第三级数据
  level4: [],               // 第四级数据
  dictsEngineFactory: [],   // 发动机厂数据
  dictsManuFacturer: [],    // 主机厂数据
  dictsFuelType: [],       // 燃料类型数据
  dictsBreed: [],          // 品系数据
  dictsWeightMidLight: [], // 重中轻数据
  select1: [],             // 选择器1数据
  select2: [],             // 选择器2数据
  select3: [],             // 选择器3数据
  select4: []              // 选择器4数据
}