# Tooltip 组件

一个功能丰富的提示框组件，支持数据展示、排序、自定义布局等功能。

## 基本用法

Tooltip 组件可以用于展示图表数据的详细信息，支持多种数据格式和显示方式。

## 属性 (Props)

| 参数           | 说明                           | 类型    | 默认值                                                |
| -------------- | ------------------------------ | ------- | ----------------------------------------------------- |
| width          | 提示框的宽度                   | String  | '50vw'                                                |
| params         | 提示框所需的数据数组           | Array   | []                                                    |
| title          | 提示框的标题                   | String  | ''                                                    |
| showTotal      | 是否显示总数信息               | Boolean | false                                                 |
| mapping        | 数据映射对象，用于转换数据     | Object  | { sales: 'sales', proportion: 'slice', yoy: 'slice' } |
| shouldSort     | 是否需要对数据进行排序         | Boolean | true                                                  |
| sortField      | 指定排序字段                   | String  | 'proportion'                                          |
| isYoySortField | 是否置顶同比数据               | Boolean | false                                                 |
| singleColumn   | 是否使用单列布局               | Boolean | true                                                  |
| specialKeywords| 需要特殊处理并移到最后的关键词 | Array   | ['其他', '其它']                                      |
| showLineValues | 是否显示线条类型(line)的数值   | Boolean | true                                                  |

## 插槽 (Slots)

| 名称         | 说明                       | 参数                    |
| ------------ | -------------------------- | ----------------------- |
| default      | 触发Tooltip显示的内容      | -                       |
| hander-right | 标题右侧操作区             | { params }              |
| item         | 自定义每个数据项的显示内容 | { item }                |
| total        | 自定义总计信息的显示内容   | { params }              |
| total-num    | 自定义总计数值的显示内容   | { params }              |

## 事件 (Emits)

| 事件名 | 说明           | 参数 |
| ------ | -------------- | ---- |
| —      | 暂无自定义事件 | —    |

## 特性说明

### 数据排序
- 支持按指定字段排序（`sortField`）
- 可以将同比数据置顶显示（`isYoySortField`）
- 特殊关键词（如"其他"、"其它"）会自动移到最后

### 布局控制
- 支持单列和双列布局（`singleColumn`）
- 双列布局时数据会平均分配到左右两列
- 响应式设计，适配不同屏幕尺寸

### 数据类型处理
- 自动识别 `seriesType` 为 `line` 的数据项
- 线条类型数据显示百分号单位，其他类型显示"台"单位
- 可通过 `showLineValues` 控制是否显示线条类型数据

### 数据映射
- 支持通过 `mapping` 对象转换数据字段名
- 默认映射：`{ sales: 'sales', proportion: 'slice', yoy: 'slice' }`

## 实用案例

### 基础用法 (JSX)

```jsx
import { defineComponent } from 'vue'
import Tooltip from '@/views/components/tooltip/index.vue'

export default defineComponent({
  components: { Tooltip },
  setup() {
    const tooltipData = [
      {
        seriesName: '柴油机',
        value: 120,
        color: '#115e93',
        data: { sales: 120, proportion: 40, yoy: 8 }
      },
      {
        seriesName: '汽油机',
        value: 150,
        color: '#36b37e',
        data: { sales: 150, proportion: 50, yoy: 12 }
      },
      {
        seriesName: '其他',
        value: 30,
        color: '#ff8c00',
        data: { sales: 30, proportion: 10, yoy: 3 }
      }
    ]

    return () => (
      <Tooltip params={tooltipData} showTotal={true} singleColumn={false}>
        <div style={{ padding: '8px 16px', border: '1px solid #e8e8e8', borderRadius: '4px' }}>
          悬停查看动力类型销售数据
        </div>
      </Tooltip>
    )
  }
})
```

### 自定义数据项显示 (JSX)

```jsx
import { defineComponent } from 'vue'
import Tooltip from '@/views/components/tooltip/index.vue'

export default defineComponent({
  components: { Tooltip },
  setup() {
    const productData = [
      {
        seriesName: '重型车',
        value: 85,
        color: '#0065ff',
        data: { sales: 85, proportion: 35, yoy: 5 }
      },
      {
        seriesName: '轻型车',
        value: 120,
        color: '#722ed1',
        data: { sales: 120, proportion: 48, yoy: 15 }
      },
      {
        seriesName: '特种车',
        value: 40,
        color: '#f5222d',
        data: { sales: 40, proportion: 17, yoy: -2 }
      }
    ]

    return () => (
      <Tooltip
        params={productData}
        showTotal={true}
        shouldSort={true}
        sortField="sales"
        v-slots={{
          item: ({ item }) => (
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span
                  style={{
                    backgroundColor: item.color,
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    marginRight: '8px'
                  }}
                ></span>
                <span>{item.seriesName}</span>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div>销量: {item.data.sales}台</div>
                <div>
                  同比: {item.data.yoy > 0 ? '+' : ''}
                  {item.data.yoy}%
                </div>
              </div>
            </div>
          )
        }}
      >
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px'
          }}
        >
          查看车型销售详情
        </button>
      </Tooltip>
    )
  }
})
```

### 单列布局与总计自定义 (JSX)

```jsx
import { defineComponent } from 'vue';
import Tooltip from '@/views/components/tooltip/index.vue';

export default defineComponent({
  components: { Tooltip },
  setup() {
    const regionData = [
      { seriesName: '华东地区', value: 210, color: '#13c2c2', data: { sales: 210, proportion: 35, yoy: 10 } },
      { seriesName: '华南地区', value: 180, color: '#00b42a', data: { sales: 180, proportion: 30, yoy: 8 } },
      { seriesName: '华北地区', value: 150, color: '#ff7d00', data: { sales: 150, proportion: 25, yoy: 5 } },
      { seriesName: '西部地区', value: 60, color: '#f5222d', data: { sales: 60, proportion: 10, yoy: 3 } }
    ];

    return () => (
      <Tooltip
        params={regionData}
        showTotal={true}
        singleColumn={true}
        v-slots={{
          total: ({ params }) => (
            <div style={{ marginTop: '12px', paddingTop: '12px', borderTop: '1px dashed #e8e8e8', display: 'flex', justifyContent: 'space-between' }}
              <div style={{ fontWeight: 'bold' }}>全国销售总计</div>
              <div style={{ fontWeight: 'bold', color: '#f5222d' }}>
                {params.reduce((sum, item) => sum + item.data.sales, 0)}台
              </div>
            </div>
          ),
          'hander-right': () => (
            <button style={{ fontSize: '12px', padding: '2px 8px', border: '1px solid #e8e8e8', borderRadius: '4px' }}
              导出数据
            </button>
          )
        }}
      >
        <div style={{ cursor: 'pointer' }}>各区域销售分布</div>
      </Tooltip>
    );
  }
});
```

### 控制线条类型数据显示 (JSX)

```jsx
import { defineComponent } from 'vue'
import Tooltip from '@/views/components/tooltip/index.vue'

export default defineComponent({
  components: { Tooltip },
  setup() {
    const mixedData = [
      {
        seriesName: '销量',
        seriesType: 'bar',
        value: 120,
        color: '#115e93',
        data: { sales: 120, proportion: 40, yoy: 8 }
      },
      {
        seriesName: '增长率',
        seriesType: 'line',
        value: 15.5,
        color: '#00a9f4',
        data: { sales: 15.5, proportion: 15.5, yoy: 15.5 }
      },
      {
        seriesName: '市场占比',
        seriesType: 'line',
        value: 25.8,
        color: '#ff7d00',
        data: { sales: 25.8, proportion: 25.8, yoy: 25.8 }
      }
    ]

    return () => (
      <div style={{ display: 'flex', gap: '20px' }}>
        {/* 显示所有数据 */}
        <Tooltip params={mixedData} showLineValues={true} singleColumn={true}>
          <div style={{ padding: '8px 16px', border: '1px solid #e8e8e8', borderRadius: '4px' }}>
            显示所有数据（包括线条类型）
          </div>
        </Tooltip>

        {/* 隐藏线条类型数据 */}
        <Tooltip params={mixedData} showLineValues={false} singleColumn={true}>
          <div style={{ padding: '8px 16px', border: '1px solid #e8e8e8', borderRadius: '4px' }}>
            隐藏线条类型数据
          </div>
        </Tooltip>
      </div>
    )
  }
})
```

### 数据结构说明

组件支持的数据项结构：

```javascript
const dataItem = {
  seriesName: '数据系列名称',     // 必需：显示的名称
  seriesType: 'bar|line',       // 可选：数据类型，影响单位显示和是否受showLineValues控制
  value: 100,                   // 必需：主要数值
  color: '#115e93',             // 可选：显示颜色
  data: {                       // 可选：扩展数据对象
    sales: 100,                 // 销量数据
    proportion: 30,             // 占比数据
    yoy: 5                      // 同比数据
  }
}
```

tooltip中渲染

```js
   :tooltip="{
            formatter: params =>
                  TooltipFormatter(TooltipPercentageComponent, params, {
                    mapping: {
                      sales: 'value',
                      proportion: 'slice',
                      yoy: 'slice'
                    },
                    singleColumn: false,
                    sortField: 'value',
                  })
           }"

```
