 
import request from '@/utils/request'
const PREFIX = 'intelligence/report'

/**
 * @description 一句话-获取期数
 * @returns Object
 */
export const homePageMacroDataList = (params) => {
  return request({
    url: `${PREFIX}/list`,
    method: 'get',
    params
  })
}
 
/**
 * @description 一句话-获取期数右侧分类
 * @returns Object
 */
export const getPlateReportById = (params) => {
  return request({
    url: `${PREFIX}/getInfoBlongReportById/${params.reportId}`,
    method: 'get' 
  })
}

/**
 * @description 一句话-期数下面分类
 * @returns Object
 */
export const getInfoTypeById = (params) => {
  return request({
    url: `${PREFIX}/getInfoTypeById/${params.reportId}`,
    method: 'get'
  })
}

