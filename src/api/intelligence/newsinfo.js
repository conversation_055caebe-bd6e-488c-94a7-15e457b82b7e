 
import request from '@/utils/request'
const PREFIX = 'intelligence/newsinfo'

/**
 * @description 查看新闻详情
 * @returns Object
 */
export const viewNewsInfo = (params) => {
  return request({
    url: `${PREFIX}/viewNewsInfo?id=${params.id}&newsTagId=${params?.newsTagId}`,
    method: 'POST' 
  })
}

/**
 * @description 14.1	新闻列表查询
 * @returns Object
 */
export const newsList = (params) => {
  return request({
    url: `${PREFIX}/newsList`,
    method: 'get',
    params
  })
}
 
/**
 * @description 14.2	新增新闻
 * @returns Object
 */
export const addNews = (data) => {
  return request({
    url: `${PREFIX}/addNews`,
    method: 'post',
    data
  })
}
/**
 * @description 14.3	修改新闻
 * @returns Object
 */
export const updateNews = (data) => {
  return request({
    url: `${PREFIX}/updateNews`,
    method: 'post',
    data
  })
}
/**
 * @description 14.4	删除新闻
 * @returns Object
 */
export const delNews = (data) => {
  return request({
    url: `${PREFIX}/delNews`,
    method: 'delete',
    data
  })
}

/**
 * @description 14.5	根据ID查新闻
 * @returns Object
 */
export const getNewsById = (params) => {
  return request({
    url: `${PREFIX}/getNewsById`,
    method: 'get',
    params
  })
}

// 发布新闻
export const batchPublish = (data) => {
  return request({
    url: `${PREFIX}/batchPublish`,
    method: 'post',
    data
  })
}
// 批量取消
export const batchCancelPublish = (data) => {
  return request({
    url: `${PREFIX}/batchCancelPublish`,
    method: 'post',
    data
  })
}
/**
 * @description 批量分配栏目
 * @returns Object
 */
export const batchAssignKind = (data) => {
  return request({
    url: `${PREFIX}/batchAssignKind`,
    method: 'post',
    data
  })
}

export const deleteBatchApi = (data) => {
  return request({
    url: `${PREFIX}/deleteBatch`,
    method: 'post',
    data
  })
}
