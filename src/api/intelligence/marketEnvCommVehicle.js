 
import request from '@/utils/request'
const PREFIX = 'intelligence/marketEnvCommVehicle'

/**
 * @description 7.3	商用车总体市场销量趋势(共用占位图)
 * @returns Object
 */
export const commVehicleSalesYoyList = (data) => {
  return request({
    url: `${PREFIX}/commVehicleSalesYoyList`,
    method: 'post',
    data
  })
}

/**
 * @description 7.4	商用车**销量趋势(独立占位图:材料结构、马力段、排量段、驱动形式、气缸数)
 * @returns Object
 */
export const commVehicleSalesByQueryTypeList = (data) => {
  return request({
    url: `${PREFIX}/commVehicleSalesByQueryTypeList`,
    method: 'post',
    data
  })
}

/**
 * @description 7.5	商用车市场主机厂销量情况(独立占位图)
 * @returns Object
 */
export const commVehicleManuFacturerSalesList = (data) => {
  return request({
    url: `${PREFIX}/commVehicleManuFacturerSalesList`,
    method: 'post',
    data
  })
}

/**
 * @description 7.6	商用车市场发动机企业销量情况(独立占位图)
 * @returns Object
 */
export const commVehicleEngineSalesList = (data) => {
  return request({
    url: `${PREFIX}/commVehicleEngineSalesList`,
    method: 'post',
    data
  })
}

/**
 * @description 7.7	商用车市场区域销量情况(独立占位图)
 * @returns Object
 */
export const selectCommVehicleAreaSaleList = (data) => {
  return request({
    url: `${PREFIX}/selectCommVehicleAreaSaleList`,
    method: 'post',
    data
  })
}

 

 