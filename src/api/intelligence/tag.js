 
import request from '@/utils/request'
const PREFIX = 'intelligence/tag'

/**
 * @description 公开新闻-根据iis_sys_news_tag的主键id查询
 * @returns Object
 */
export const queryNewsTagById = (params) => {
  return request({
    url: `${PREFIX}/queryNewsTagById`,
    method: 'get',
    params
  })
}

/**
 * @description 获取特定信息种类的热点新闻的接口
 * @returns Object
 * /intelligence/tag/queryHotNews?hotTagName=经营动态&startDate=2023-09-28&endDate=2024-11-22&searchKey=拖拉机
 */
export const queryHotNews = (params) => {
  return request({
    url: `${PREFIX}/queryHotNews`,
    method: 'get',
    params
  })
}

/**
 * @description 全局搜索新闻的接口
 * @returns Object
 * /intelligence/tag/queryHotNews?hotTagName=经营动态&startDate=2023-09-28&endDate=2024-11-22&searchKey=拖拉机
 */
export const gQueryHotNews = (params) => {
  return request({
    url: `${PREFIX}/queryAllNews`,
    method: 'get',
    params
  })
}
// 获取新闻tab列表
export function tabList(params) {
  return request({
    url: `${PREFIX}/list`,
    method: 'get',
    params
  })
}

 