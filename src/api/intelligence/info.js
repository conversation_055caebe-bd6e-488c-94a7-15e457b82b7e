 
import request from '@/utils/request'
const PREFIX = 'intelligence/info'

/**
 * @description 获取信息快递一期所有信息的接口
 * @returns Object
 */
export const queryInfoByReportId = (params) => {
  return request({
    url: `${PREFIX}/queryInfoByReportId/${params.reportId}`,
    method: 'get'
  })
}

/**
 * @description 一句话信息-根据reportId和infoType信息类型查询
 * @returns Object
 */
export const queryInfoByReportIdAndType = (params) => {
  return request({
    url: `${PREFIX}/queryInfoByReportIdAndType`,
    method: 'get',
    params
  })
}

/**
 * @description 一句话信息-根据起始时间、截止时间、关键字查询信息（新增接口）
 * @returns Object
 */
export const queryByKeyWord = (params) => {
  return request({
    url: `${PREFIX}/queryByKeyWord`,
    method: 'get',
    params
  })
}

/**
 * @description 2.一句话新闻附件获取接口
 * @returns Object
 */
export const getAttachmenList = (params) => {
  return request({
    url: `${PREFIX}/getAttachmenList/${params.id}`,
    method: 'get',
    params
  })
}

export const getAttachmenDetail = (params) => {
  return request({
    url: `${PREFIX}/getAttachmenDetail/${params.id}`,
    method: 'get',
    params
  })
}