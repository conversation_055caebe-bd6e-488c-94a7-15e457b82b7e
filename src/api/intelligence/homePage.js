import request from '@/utils/request'
const PREFIX = 'intelligence/homePage'

/**
 * @description 首页-新能源市场
 * @returns Object
 */
export const homePagePermeability = (data, tableName) => {
  return request({
    url: `${PREFIX}/permeability/${tableName}`,
    method: 'POST',
    data
  })
}

/**
 * @description 首页-OEM排名
 * @returns Object
 */
export const homePageOem = (data, tableName) => {
  return request({
    url: `${PREFIX}/oem/${tableName}`,
    method: 'POST',
    data
  })
}

/**
 * @description 首页-出口市场
 * @returns Object
 */
export const exportMarket = (data, tableName) => {
  return request({
    url: `${PREFIX}/exportMarket/${tableName}`,
    method: 'POST',
    data
  })
}

/**
 * @description 首页-国内市场
 * @returns Object
 */
export const domesticMarket = (data, tableName) => {
  return request({
    url: `${PREFIX}/domesticMarket/${tableName}`,
    method: 'POST',
    data
  })
}

/**
 * @description 首页-出口分布时间
 * @returns Object
 */
export const exportMarketDateRange = (data = {}) => {
  return request({
    url: `${PREFIX}/exportMarket/dateRange`,
    method: 'POST',
    data
  })
}
 
/**
 * @description 首页-国内市场时间
 * @returns Object
 */
export const domesticMarketRange = (data = {}) => {
  return request({
    url: `${PREFIX}/domesticMarket/dateRange`,
    method: 'POST',
    data
  })
}