
import request from '@/utils/request'
const PREFIX = 'intelligence/macroData'

export function api(query,adata) {
  return request({
    url: `/intelligence/macroData/${adata}`,
    method: 'get',
    params: query
  })
}


export function apiPost(query,adata) {
  return request({
    url: `/intelligence/macroData/${adata}`,
    method: 'post',
    data: query
  })
}

/**
 * @description 数据库管理-宏观环境列表查询
 * @returns Object
 */
export const macroDateStatisticsList = (params) => {
  return request({
    url: `${PREFIX}/macroDateStatisticsList`,
    method: 'get',
    params
  })
} 

/**
 * @description 数据库管理-宏观环境列表按不同宏观数据分类要渲染的表头查询
 * @returns Object
 */
export const getSubTypeBymacroType = (data) => {
  return request({
    url: `${PREFIX}/getSubTypeBymacroType`,
    method: 'post',
    data
  })
} 

