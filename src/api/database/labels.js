import request from '@/utils/request'

// 查询列表
export function list(query) {
  return request({
    url: '/intelligence/tag/list',
    method: 'get',
    params: query
  })
}
// 详情
export function detail(query) {
    return request({
      url: `/intelligence/tag/detail/${query}`,
      method: 'get'
    })
  }


  export function apiPost(query,adata) {
    return request({
      url: `/intelligence/tag/${adata}`,
      method: 'post',
      data: query
    })
  }


  // 删除
export function del(query) {
  return request({
    url: `/intelligence/tag/del/${query}`,
    method: 'delete'
  })
}

