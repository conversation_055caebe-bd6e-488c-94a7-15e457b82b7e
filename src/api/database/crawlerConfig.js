//爬虫网站配置
import request from '@/utils/request'

// 查询列表
export function list(query) {
  return request({
    url: '/intelligence/iisCrawlerConfig/list',
    method: 'post',
    data: query,
    params:query
  })
}
// 详情
export function detail(query) {
    return request({
      url: `/intelligence/iisCrawlerConfig/detail/${query}`,
      method: 'get'
    })
  }


  export function apiPost(query,adata) {
    return request({
      url: `/intelligence/iisCrawlerConfig/${adata}`,
      method: 'post',
      data: query
    })
  }


  // 删除
export function del(query) {
  return request({
    url: `/intelligence/iisCrawlerConfig/del/${query}`,
    method: 'delete'
  })
}

