//爬虫过滤配置
import request from '@/utils/request'

// 查询列表
export function list(query) {
  return request({
    url: '/intelligence/iisCrawlerFilter/list',
    method: 'post',
    data: query
  })
}
// 详情
export function detail(query) {
    return request({
      url: `/intelligence/iisCrawlerFilter/detail/${query}`,
      method: 'get'
    })
  }


  export function apiPost(query,adata) {
    return request({
      url: `/intelligence/iisCrawlerFilter/${adata}`,
      method: 'post',
      data: query
    })
  }


  // 删除
export function del(query) {
  return request({
    url: `/intelligence/iisCrawlerFilter/del/${query}`,
    method: 'delete'
  })
}

