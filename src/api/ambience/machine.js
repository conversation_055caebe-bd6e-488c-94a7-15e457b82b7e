import request from '@/utils/request'
import { getYAxisName, xAxisMonth } from '@/views/ambience/components/commonConfigData'
import { ElMessage } from 'element-plus'
import { splitDate } from './macro'
import { changeYearMonth, JI, sliceync, sortMonth } from './market'
import { dataConvertForPercentTopN, getOtherNumber } from '../../utils/dataconvert.js'

const changeData = dataRes => {
  const { seriesList, yms } = sliceync(dataRes)
  // let saleslistTotal = []
  // let slicelistTotal = [];// name,value形式
  const _yms = changeYearMonth([...yms])
  const newReas = seriesList?.map(_item => {
    const { name, saleslistTotal: sales } = _item

    return {
      // 每条线的名称
      name: name,
      // 每条线的数据
      data: sales || [],
      // 展示成什么图形
      type: 'line',
      symbolSize: 1
    }
  })

  const chart1 = {
    title: {
      text: '销量走势'
    },
    xAxis: {
      data: _yms
    },
    yAxis: {
      type: 'value',
      name: getYAxisName('台')
    },
    series: newReas
  }

  const newReas1 = seriesList?.map(_item => {
    const { name, slicelistTotal: slice = [] } = _item

    return {
      type: 'bar',
      data: slice, //[30, 50, 44, 25, 30, 20, 10, 30, 20, 20, 10, 25],
      name: name,
      stack: 'total',
      barWidth: '40%',
      barMaxWidth: 30 // 最大宽度为30像素
    }
  })

  const chart2 = {
    title: {
      text: '销量占比走势'
    },
    xAxis: {
      data: _yms
    },
    yAxis: {
      type: 'value',
      max: 100,
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: newReas1
  }

  return {
    chart1,
    chart2
  }
}

// 8.1	细分销量/占比
export const getGenMachineSalesSliceList = async query => {
  const { date = undefined, ...otherQuery } = query
  const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  const params = {
    ...newDate,
    'pointerType': 0,
    dataSource: '2',
    ...otherQuery
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/genMachineSalesSliceList',
      method: 'post',
      data: params
    })
    const { code = '', data = [], msg = '' } = res
    // console.log(data,'data')
    if (code === 200) {
      const _res = changeData(data)
      // console.log(_res, 'data')
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

const changeTrendList = (resData = []) => {
  let serData = {}
  const XName = resData?.map(item => {
    const { name, data = [] } = item

    data.map(ch => {
      const { engineManufacturer = '' } = ch
      if (
        serData?.[engineManufacturer]?.engineManufacturer &&
        serData?.[engineManufacturer]?.engineManufacturer === engineManufacturer
      ) {
        const facturerData = serData?.[engineManufacturer]?.data
        const _data = serData?.[engineManufacturer]
        serData = {
          ...serData,
          [engineManufacturer]: {
            ..._data,
            data: [
              ...facturerData,
              {
                ...ch
              }
            ]
          }
        }
      } else {
        serData = {
          ...serData,
          [engineManufacturer]: {
            engineManufacturer,
            data: [
              {
                ...ch
              }
            ]
          }
        }
      }
    })
    return name
  })
  const newYearMonth = sortMonth(XName)

  let newReas = Object.values(serData)

  const _XName = changeYearMonth(newYearMonth)

  newReas = newReas?.map(item => {
    const { engineManufacturer, data = [] } = item
    let saleslistTotal = []
    const saleslist = newYearMonth?.map((y, index) => {
      const list = data?.filter(({ yearMonth: m = '' }) => m === y)
      let _slice = 0
      if (list && list.length > 0) {
        let _S = list[0]?.slice || list[0]?.lsice || ''
        _slice = _S && _S?.indexOf('%') > -1 ? _S?.replace(new RegExp('%', 'g'), '') : _S
      }

      saleslistTotal.push({
        name: _XName[index],
        value: _slice
      })

      return _slice
    })

    return {
      name: engineManufacturer,
      stack: 'total',
      barWidth: '40%',
      type: 'bar',
      data: saleslistTotal, //saleslist
      barMaxWidth: 30 // 最大宽度为30像素
    }
  })

  const obj = {
    title: {
      text: '动力厂家占比'
    },
    xAxis: {
      data: changeYearMonth(XName)
    },
    yAxis: {
      type: 'value',
      max: 100,
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: newReas
  }

  return obj
}

// 8.1	趋势
export const genMachineCptTrendList = async query => {
  const { date = undefined, ...otherQuery } = query
  const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  const params = {
    ...newDate,
    'pointerType': 0,
    dataSource: '6',
    ...otherQuery
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/genMachineCptTrendList',
      method: 'post',
      data: params
    })
    const { code = '', data = [], msg = '' } = res
    // console.log(data,'data')
    if (code === 200) {
      const _res = changeTrendList(data)
      // console.log(_res, 'data')
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

/**
 * step1.获取所有列表的name
 */
const getAllName = (list, key) => {
  const response = []
  if (list && list.length > 0) {
    for (let i = 0; i < list.length; i++) {
      let engineList = list[i] && list[i][key.listKey] ? list[i][key.listKey] : []
      if (!key.listKey) engineList = list[i] ?? []
      for (let j = 0; j < engineList.length; j++) {
        response.push(engineList[j][key.nameKey])
      }
    }
  }
  return Array.from(new Set(response))
}

/**
 * step2.每个组别渲染包含所有x轴数据
 {
      listKey: 'engineList',
      nameKey: 'engineFactory',
      dataNameKey: 'manuFacturer',
      dataValueKey: 'engineSales'
    }
 */
const flatAllName = (list, key, allNameArr) => {
  if (list && list.length === 0) return []
  const response = JSON.parse(JSON.stringify(list))
  if (list && list.length > 0) {
    list.forEach((el, elIndex) => {
      let children = []
      let responseChildren = []
      if (!key.listKey) {
        children = JSON.parse(JSON.stringify(el))
        responseChildren = response[elIndex]
      } else {
        if (!el) return
        children = JSON.parse(JSON.stringify(el[key.listKey]))
        if (children.length === 0) return
        responseChildren = response[elIndex][key.listKey]
      }

      const childrenNameArray = children.map(v => v[key.nameKey])
      allNameArr.forEach((i, iIndex) => {
        if (childrenNameArray.indexOf(i) === -1) {
          responseChildren.push({
            ...key.dataObj,
            [[key.nameKey]]: i,
            [key.dataNameKey]: children[0][key.dataNameKey],
            [key.dataValueKey]: 0
          })
        }
      })
    })
    return response
  }
}

/**
 * @description 组装charts第一层数据集
 * @param list 接口返回数据
 * @param key name的key值
 */
function firstChartDataFormatter(list, key) {
  const response = []
  if (list && list.length > 0) {
    for (let i = 0; i < list.length; i++) {
      const engineList = list[i][key.listKey]
      for (let j = 0; j < engineList.length; j++) {
        response.push({ name: engineList[j][key.nameKey], data: [] })
      }
      break
    }
  }
  return response
}
/**
 * @description step3.组装charts完整数据
 * @param list 接口返回数据
 * @param key name的key值
 */
function chartDataFormatter(list, key, firstLevel) {
  const response = firstLevel ? firstLevel : firstChartDataFormatter(list, key)
  if (list && list.length > 0) {
    for (let i = 0; i < list.length; i++) {
      let engineList = []
      if (!key.listKey) {
        engineList = list[i] ?? []
      } else {
        engineList = list[i] ? list[i][key.listKey] : []
      }

      for (let j = 0; j < engineList.length; j++) {
        const hasNameIndex = response.findIndex(e => (e || {}).name === engineList[j][key.nameKey])
        if (hasNameIndex !== -1) {
          // 本页面用的表2
          const latterName =
            engineList[j][key.dataNameKey]?.length === 4 && key.dataNameLastFormatter
              ? key.dataNameLastFormatter
              : key.dataNameFormatter
                ? key.dataNameFormatter
                : ''
          const name = engineList[j][key.dataNameKey] + latterName

          response[hasNameIndex].data.push({
            name,
            value: engineList[j][key.dataValueKey],
            ...engineList[j]
          })
        }
      }
    }
  }
  return response
}

function fillXAxisList(list, defaultList) {
  const response = JSON.parse(JSON.stringify(list))
  if (response.length === 0) {
    return [{ name: '', data: defaultList }]
  }
  response.forEach(element => {
    const ElementList = element.data
    defaultList.forEach((el, ind) => {
      if (el.name !== ElementList[ind].name) {
        ElementList.splice(ind, 0, { ...defaultList[ind] })
      }
    })
  })
  return response
}

// 柱状图数据构建1
const getSeriesDataByBar = (
  params = {},
  patternRsList = [],
  xCode = 'month',
  yCode = 'type',
  valueCode = 'proportion',
  isTop = true
) => {
  // 根据查询条件计算仅三年
  const year = Number(params.year)
  const showYears = []
  for (let i = 0; i < 3; i++) {
    showYears.push({ name: year - i + '年', value: 0 })
  }
  showYears.reverse()
  // 获取所有列表的分类
  let allNameD = []
  if (patternRsList && patternRsList.length > 0) {
    // for (var i in patternRsList) {
    //   patternRsList[i] = getOtherNumber(patternRsList[i], yCode, valueCode)
    // }
    allNameD = getAllName(patternRsList, { nameKey: yCode })
  }
  // 组装一级结构
  const levelDataD = []
  allNameD.forEach(el => {
    levelDataD.push({ name: el, data: [] })
  })
  const dataObj = {}
  dataObj[yCode] = ''
  dataObj[valueCode] = ''
  dataObj[xCode] = ''
  // x轴分类保持每个分类都一样有数据
  const levelSecondDataD = flatAllName(
    patternRsList,
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataValueKey: valueCode,
      dataObj: dataObj
    },
    allNameD
  )
  // 生成charts数据
  let dataD = chartDataFormatter(
    JSON.parse(JSON.stringify(levelSecondDataD)),
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataNameFormatter: '年',
      dataValueKey: valueCode
    },
    JSON.parse(JSON.stringify(levelDataD))
  )

  dataD = fillXAxisList(dataD, showYears)

  // const series = isTop ? dataConvertForPercentTopN(dataD, dataD.length, 1) : dataD
  const series = dataD

  // 去重
  series.forEach(dt => {
    const names = []
    dt.data?.forEach(ndt => {
      const inum = names.findIndex(s => s.name === ndt.name)
      if (inum > -1) {
        names[inum] = ndt.value > 0 ? ndt : names[inum]
      } else {
        names.push(ndt)
      }
    })
    dt.data = names
  })
  return series
}

// 柱状图数据构建2
const getSeriesDataByBar2 = (
  patternRsList = [],
  xCode = 'month',
  yCode = 'type',
  valueCode = 'proportion',
  isTop = true,
  isFormat = false
) => {
  if (isFormat) {
    const quarterArray = {
      Q1: '第一季度',
      Q2: '第二季度',
      Q3: '第三季度',
      Q4: '第四季度'
    }
    console.log(patternRsList, 'patternRsList')
    patternRsList.forEach(arr => {
      arr.forEach(el => {
        el[xCode] = quarterArray[el[xCode]]
      })
    })
  }
  const showYears = []
  // 遍历数据构建X坐标、
  patternRsList.forEach(arr => {
    if (arr.length) {
      showYears.push({ name: arr[0].month + (isFormat ? '' : '月'), value: 0 })
    }
  })
  showYears.sort((a, b) => a.name - b.name) // 升序排序
  // 获取所有列表的分类
  let allNameD = []
  if (patternRsList && patternRsList.length > 0) {
    // for (var i in patternRsList) {
    //   patternRsList[i] = getOtherNumber(patternRsList[i], yCode, valueCode)
    // }
    allNameD = getAllName(patternRsList, { nameKey: yCode })
  }
  // 组装一级结构
  const levelDataD = []
  allNameD.forEach(el => {
    levelDataD.push({ name: el, data: [] })
  })
  const dataObj = {}
  dataObj[yCode] = ''
  dataObj[valueCode] = ''
  dataObj[xCode] = ''
  // x轴分类保持每个分类都一样有数据
  const levelSecondDataD = flatAllName(
    patternRsList,
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataValueKey: valueCode,
      dataObj: dataObj
    },
    allNameD
  )
  // 生成charts数据
  let dataD = chartDataFormatter(
    JSON.parse(JSON.stringify(levelSecondDataD)),
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataNameFormatter: isFormat ? '' : '月',
      dataValueKey: valueCode
    },
    JSON.parse(JSON.stringify(levelDataD))
  )
  dataD = fillXAxisList(dataD, showYears)
  const series = isTop ? dataConvertForPercentTopN(dataD, dataD.length, 1) : dataD
  // 去重
  series.forEach(dt => {
    const names = []
    dt.data?.forEach(ndt => {
      const inum = names.findIndex(s => s.name === ndt.name)
      if (inum > -1) {
        names[inum] = ndt.value > 0 ? ndt : names[inum]
      } else {
        names.push(ndt)
      }
    })
    dt.data = names
  })
  // console.log(series, 7)
  return series
}

const formateChartData = (data, structure) => {
  const response = [...structure]
  data.forEach(el => {
    response.forEach(item => {
      item.data.push({ name: el[item.dataNameKey], value: el[item.dataValueKey] })
    })
  })
  return response
}

// 柱状转化
const getSeriesDataByType = arr => {
  // 取第一项构建xy坐标轴
  const xArr = arr[0].map(m => m.month.toString())?.sort((a, b) => a - b)

  // const yArr = arr.map(m => m.name)
  // console.log(yArr, 'yArr');
  const barList = []
  xArr.forEach(x => {
    const yData = []
    arr.forEach(children => {
      children.forEach(third => {
        if (third.month.toString() === x.toString()) {
          yData.push({
            name: third.type,
            value: third.installationcount
          })
        }
      })
    })
    barList.push({
      name: x + '年',
      type: 'bar',
      barMaxWidth: '30', // 最大宽度为30像素
      barWidth: '20%',
      
      data: yData,
    })
  })
console.log(barList, 'barList')
  return barList
}
// left1
export const generalMachineYearSale = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  try {
    const res = await request({
      url: '/intelligence/marketEnvGeneralMachine/generalMachineYearSale',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const series = data.patternRsList.length
        ? getSeriesDataByBar(query, data.patternRsList, 'month', 'type', 'installationcount', true)
        : []

      return series
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

//  right1
export const generalMachineMonthSale = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')
  const quarterArray = {
    Q1: '第一季度',
    Q2: '第二季度',
    Q3: '第三季度',
    Q4: '第四季度'
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnvGeneralMachine/generalMachineMonthSale',
      method: 'post',
      data: query
    })
    const { code = '', data = [], msg = '' } = res
    if (code === 200) {
      const totalArr = data.yoyRsList.total.sort((a, b) => a.month - b.month) // 升序排序
      totalArr.forEach(el => {
        el.month = query.pointerType === '1' ? quarterArray[el.month] : `${el.month}月`
      })
      const trendList = formateChartData(totalArr, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'month',
          dataValueKey: 'growth_rate',
          data: [],
          disTotal: true
        }
      ])
      const barList = getSeriesDataByBar2(
        data.patternRsList,
        'month',
        'type',
        'installationcount',
        true,
        query.pointerType === '1'
      )
      const returnData = []
      barList.forEach(bar => {
        returnData.push({
          name: bar.name,
          type: 'bar',
          stack: 'total',
          data: bar.data
        })
      })
      return [...returnData, ...trendList]
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

// line2
export const generalMachineYearSaleForMain = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  try {
    const res = await request({
      url: '/intelligence/marketEnvGeneralMachine/generalMachineYearSaleForMain',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      console.log(data.leftPatternRsList, 'data')
      const left = data.leftPatternRsList.length
        ? getSeriesDataByBar(
            query,
            data.leftPatternRsList,
            'month',
            'type',
            'installationcount',
            true
          )
        : []
      const right = data.patternRsList.length ? getSeriesDataByType(data.patternRsList) : []
      const trendList = formateChartData(data.yoyRsList, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'type',
          dataValueKey: 'yoy',
          data: [],
          disTotal: true
        }
      ])
      return {
        left: left,
        right: [...right, ...trendList]
      }
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

//  line3
export const generalMachineYearSaleForEngine = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  try {
    const res = await request({
      url: '/intelligence/marketEnvGeneralMachine/generalMachineYearSaleForEngine',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const left = data.leftPatternRsList.length
        ? getSeriesDataByBar(
            query,
            data.leftPatternRsList,
            'month',
            'type',
            'installationcount',
            true
          )
        : []
      const right = getSeriesDataByType(data.patternRsList)
      const trendList = formateChartData(data.yoyRsList, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'type',
          dataValueKey: 'yoy',
          data: [],
          disTotal: true
        }
      ])
      // .filter(item => item.name !== '其他')
      return {
        left: left && left.length ? left : [],
        right: [...right, ...trendList]
      }
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

//   第四个图表
export const generalMachineYearYoy = async query => {
  try {
    const res = await request({
      url: '/intelligence/marketEnvGeneralMachine/generalMachineYearYoy',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const right = data.patternRsList.length ? getSeriesDataByType(data.patternRsList) : []
      const trendList = formateChartData(data.yoyRsList, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'type',
          dataValueKey: 'yoy',
          data: [],
          disTotal: true
        }
      ])
      return {
        right: [...right, ...trendList]
      }
    } else {
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}
//   第六个图表
export const generalMachineYearSaleYoy = async query => {
  try {
    const res = await request({
      url: '/intelligence/marketEnvGeneralMachine/generalMachineYearSaleYoy',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const right = data.patternRsList.length ? getSeriesDataByType(data.patternRsList) : []
      const trendList = formateChartData(data.yoyRsList, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'type',
          dataValueKey: 'yoy',
          data: [],
          disTotal: true
        }
      ])
      return {
        right: [...right, ...trendList]
      }
    } else {
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}
