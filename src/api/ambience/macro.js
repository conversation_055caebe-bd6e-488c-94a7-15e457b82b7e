import * as echarts from 'echarts'
import request from '@/utils/request'
import { getYAxisName, xAxisMonth } from '@/views/ambience/components/commonConfigData'
import { ElMessage } from 'element-plus'
const monthNumber = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
import { getYearMonthBu, sortMonth } from './market'
const JI={
  'Q1':'Q1',
  'Q2':'Q2',
  'Q3':'Q3',
  'Q4':'Q4',
  '01':'Q1',
  '02':'Q2',
  '03':'Q3',
  '04':'Q4',
}
import store from '../../store'
export const splitDate = (date, keyNames = ['start', 'end']) => {
  if (!date)
    return {
      'startYear': '',
      'endYear': '',
      'endMonth': '',
      'endMonth': ''
    }
  if (Array.isArray(date)) {
    let obj = {}
    date.forEach((item, index) => {
      const keyValue = item.split('-')
      const key1 = `${keyNames[index]}Year`
      const key2 = `${keyNames[index]}Month`
      let initMonth = index === 0 ? 1 : 12
      obj = {
        ...obj,
        [key1]: keyValue[0],
        [key2]: keyValue[1] ? `${Number(keyValue[1])}` : initMonth
      }
    })
    return obj
  }
}

const KEYTYPEPRESET = {
  excavator: {
    yAxisName: '小时'
  },
  road: {
    yAxisName: '亿吨'
  }
}

// 解析接口返回的数据
export const formatData = (
  chartData = {},
  { initXAxisName = '', KEYVALUE = ['年', '月'] } = {}
) => { 
  if (!chartData) return chartData
  const values = Object.entries(chartData)

  const newValues = values?.map(item => {
    const [key, _values] = item
    let xAxisData = []

    let yms = new Set()
    let valuesObj = {}
    _values.forEach(({ data = [], macroType = '' }) => {
      data.forEach(({ number, yearMonths }) => {
        yms.add(yearMonths)
        let obj = valuesObj[macroType]
        valuesObj = {
          ...valuesObj,
          [macroType]: {
            ...obj,
            number,
            value: number,
            yearMonths
          }
        }
      })
    })
    if (key === 'gdp') {
      KEYVALUE = ['年', '季']
    } else {
      // KEYVALUE = ['年', '月']
      KEYVALUE = ['年', '']
    }
    const _yms = [...yms]
    const _yms1 = sortMonth(getYearMonthBu(_yms, "'"))
    const data1 = changeYearMonth(_yms1, KEYVALUE)

    let list = _values.map(({ macroType = '', data = [] }) => {
      let seriesData = []
      // if (key === 'consumerConf') {
      //   seriesData = monthNumber.map(y => {
      //     let list = data.filter(({ yearMonths: m = '' }) => {
      //       const [key, key2] = m.split("'")
      //       return key2 === y
      //     })
      //     let value = ''
      //     if (list && list.length) {
      //       value = list[0].number
      //     }
      //     return {
      //       value,
      //       // name: `${y * 1}月`,
      //       name: `${y * 1}`,
      //       yearMonths: y
      //     }
      //   })
      // } else {
      //   seriesData = _yms1.map((y, index) => {
      //     let list = data.filter(({ yearMonths: m = '' }) => m === y)
      //     let value = ''
      //     if (list && list.length) {
      //       value = list[0].number
      //     }

      //     return {
      //       value,
      //       name: data1[index],
      //       yearMonths: y
      //     }
      //   })
      // }

      seriesData = _yms1.map((y, index) => {
        let list = data.filter(({ yearMonths: m = '' }) => m === y)
        let value = ''
        if (list && list.length) {
          value = list[0].number
        }

        return {
          value,
          name: data1[index],
          yearMonths: y
        }
      })
      return {
        // 每条线的名称
        name: macroType,
        // 每条线的数据
        data: seriesData || [],
        // 展示成什么图形
        type: 'line',
        symbolSize: 4,
        symbol: 'circle',
        itemStyle:{
          borderColor:'rgba(255, 255, 255, 1)'
        },
        smooth:true
      }
    })

    //说明：只有一个维度的折线图，期望加入渐变色块
    if (list.length == 1) {
      list[0].areaStyle = {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(0, 169, 244, 1)'
          },
          {
            offset: 1,
            color: 'rgba(0, 169, 244, 0)'
          }
        ])
      }
    }

    let name = _values[0]?.name || ''
    const yAxisName = KEYTYPEPRESET[key]?.yAxisName || '%'
    const xAxisName = initXAxisName || KEYTYPEPRESET[key]?.xAxisName || ''

    if (key !== 'impExpMarket') {
      name = `国内${name}`
    }

    // 整理成echarts数据
    return {
      height: key === 'gdp' ? '20%' : '40%',
      key,
      name: name,
      // 表名
      title: {
        text: name,
        textAlign: 'center'
      },
      xAxis: {
        // X坐标轴数据
        // data:xAxisData,
        // x轴名称
        axisLine: {
          lineStyle: {
            color: '#C4C6CB'
          }
        },
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          color: 'rgba(5, 28, 44, 0.8)'
        },
        name: xAxisName ? getYAxisName(xAxisName) : ''
      },
      // 显示的图表数据
      series: list,
      yAxis: {
        // y轴单位名称
        name: getYAxisName(yAxisName),
        splitLine: {
          show: true // 显示x轴网格线
        }
      },
    }
  })

  return newValues
}

// 获取经济环境图表数据
export const getMacroEnvEcoDataList = async query => {
  const { date = undefined } = query

  const newDate = splitDate(date)

  const params = {
    ...newDate
  }

  try {
    const res = await request({
      url: '/intelligence/macroData/macroEnvEcoDataList',
      method: 'post',
      data: params
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const _res = formatData(data)
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    // ElMessage({
    //   showClose: true,
    //   message: error,
    //   type: 'error',
    // })
    return []
  }
}

// 对象转换成数组
const OBJECCHANGEARR = obj => {
  if (Object.prototype.toString.call(obj) === '[object Object]') {
    const entriesValues = Object.entries(obj)
    const data = entriesValues.map(item => {
      const [key, value] = item
      return {
        key,
        data: value
      }
    })
    return data
  }
  return obj
}

// 年、月分转换
const changeYearMonth = (data, KEYVALUE = ['年', '月']) => {
  let mark = ''
  // X坐标轴数据
  const xAxisData = data?.map(yearMonths => {
    let [year = '', month = ''] = yearMonths.split("'")
    const [value1, value2] = KEYVALUE

    let _month = month?.replace(/\b(0+)/gi, '')
    _month = _month ? `${_month}${value2}` : ''
    let xAxisName = ''

    if (mark !== year) {
      mark = year
      xAxisName = `${year}${value1}`
    }

    if (value2 === '季') {
      xAxisName = year
      _month = JI[month]
    }

    xAxisName = `${xAxisName}${_month}`

    return xAxisName
  })

  return xAxisData
}

// 匹配月份数据
const changeMacrdMonthData = data => {
  const yData = monthNumber.map((item, index) => {
    const _data = data?.filter(({ yearMonths }) => {
      let [_, month = ''] = yearMonths?.split("'") || []
      return month === item
    })
    let value = ''
    if (_data && _data.length) {
      value = _data[0]?.number
    }

    return {
      value: value,
      name: xAxisMonth[index]
    }
  })

  return yData

  // console.log(yData,'y')
}

//产业环境环境data转换成echart格式
const changeMacrdData = (chartData = dataADDD, { initXAxisName = '' } = {}) => {
  if (!chartData) return chartData
  const _chartData = OBJECCHANGEARR(chartData)

  let names = [
    '公路物流运价指数走势',
    '物流景气指数走势',
    '小松挖掘机开工小时数',
    '公路货运量月度走势(亿吨)',
    '冷鲜肉月度同比产量增速',
    '快递量月度同比增速',
    '原煤月度产量同比增速',
    '钢材月度产量同比增速',
    '水泥月度产量同比增速'
  ]

  const newData = _chartData.map(item => {
    const { data = [], key } = item
    let obj = {
      key
    }
    data?.forEach(item2 => {
      const { data: _data = {} } = item2
      obj = {
        ...obj,
        ...item2,
        data: OBJECCHANGEARR(_data)
      }
    })
    return obj
  })

  let newValues = newData?.map(({ name, data = [], key = '' }) => {
    const _values2 = data?.map(_item => {
      const { key: key2 = '', data = [] } = _item
      let seriesData = []
      if (data && data.length) {
        seriesData = changeMacrdMonthData(data)
      }
      return {
        // 每条线的名称
        name: key2,
        // 每条线的数据
        data: seriesData || [],
        // 展示成什么图形
        type: KEYTYPEPRESET[key]?.type || 'line',
        symbolSize: 1
      }
    })

    const yAxisName = KEYTYPEPRESET[key]?.yAxisName || '%'
    const xAxisName = initXAxisName || KEYTYPEPRESET[key]?.xAxisName || ''
    return {
      index: names.findIndex(i => i === name),
      height: '60%',
      key,
      name: name,
      // 表名
      title: {
        text: name
      },
      xAxis: {
        // X坐标轴数据
        data: xAxisMonth,
        // x轴名称
        name: xAxisName ? getYAxisName(xAxisName) : ''
      },
      // 显示的图表数据
      series: _values2,
      yAxis: {
        // y轴单位名称
        name: getYAxisName(yAxisName)
      }
    }
  })

  newValues = newValues?.sort((a, b) => a?.index - b.index)
  return newValues
}

//产业环境数据
export const getMacroEnvIndDataList = async query => {
  const { date = undefined } = query
  const newDate = splitDate(date)
  const params = {
    ...newDate,
    commandKey: store.state.app.commandKey
  }
  const res = await request({
    url: '/intelligence/macroData/macroEnvIndDataList',
    method: 'post',
    data: params
  })
  const { code = '', data = {} } = res
  if (code === 200) {
    const _res = changeMacrdData(data)
    return _res
  } else {
    return []
  }
}

// 新闻区/intelligence/tag/queryNewsTagById

// const PARAMSKEYVUEDOC = {
//   //宏观环境
//   macro:'53',
//   //经济环境
//   economy:'56',
//   //政策环境
//   policy:'44',
//   //市场环境
//   estate:'99',
//   //商用车
//   vehicle:'10',
//   //通机
//   marketmachine:'36',
//   //船电
//   electricity:'38',
//   //新能源
//   marketenergy:'39',
//   //细分动力
//   marketpower:'56',
// }

const PARAMSKEYVUEDOC = {
  // s商用车
  vehicle: {
    '宏观∙行业趋势∙政策法规': 35,
    '经营动态': 36,
    '高管动态': 37,
    '人事变动': 38,
    '市场活动与订单': 39,
    '供货价格与商务政策': 40,
    '终端返利与服务政策': 41,
    '产品与技术': 42,
    '产品市场表现': 43,
    '战略合作': 44,
    '四化动态': 45
  },
  // 新能源
  marketenergy: {
    '宏观∙行业趋势∙政策法规': 90,
    '经营动态': 91,
    '高管动态': 92,
    '人事变动': 93,
    '市场活动与订单': 94,
    '供货价格与商务政策': 95,
    '终端返利与服务政策': 96,
    '产品与技术': 97,
    '产品市场表现': 98,
    '战略合作': 99,
    '四化动态': 100
  },
  // 经济环境
  economy: {
    '新闻区': '101'
  },
  // 政策环境
  policy: {
    '新闻区': '103'
  },
  // 产业环境
  estate: {
    '新闻区': '102'
  },
  // 通机
  marketmachine: {
    '宏观∙行业趋势∙政策法规': '126',
    '经营动态': '127',
    '高管动态': '128',
    '人事变动': '129',
    '市场活动与订单': '130',
    '供货价格与商务政策': '131',
    '终端返利与服务政策': '132',
    '产品与技术': '133',
    '产品市场表现': '134',
    '战略合作': '135',
    '四化动态': '136'
  },
  //船电
  electricity: {
    '宏观∙行业趋势∙政策法规': '137',
    '经营动态': '138',
    '高管动态': '139',
    '人事变动': '140',
    '市场活动与订单': '141',
    '供货价格与商务政策': '142',
    '终端返利与服务政策': '143',
    '产品与技术': '144',
    '产品市场表现': '145',
    '战略合作': '146',
    '四化动态': '147'
  },
  // 细分动力
  marketpower: {
    '宏观∙行业趋势∙政策法规': '115',
    '经营动态': '116',
    '高管动态': '117',
    '人事变动': '118',
    '市场活动与订单': '119',
    '供货价格与商务政策': '120',
    '终端返利与服务政策': '121',
    '产品与技术': '122',
    '产品市场表现': '123',
    '战略合作': '124',
    '四化动态': '125'
  }
}

export const getQueryNewsTagById = async query => {
  const { year, ...otherQuey } = query
  // startDate=2023-09-28&endDate
  let startDate = ''
  let endDate = ''
  if (year) {
    startDate = year[0]
    endDate = year[1]
  }
  // let { hash:pathname = '',pathname:p } = location
  // pathname = pathname.indexOf('ambience') > -1 ? pathname : p;

  // const values = pathname.split("/")

  // const [ _,path1,path2,path3 ] = values
  // let _newsTagId = ''
  // try{
  //   _newsTagId = PARAMSKEYVUEDOC[path3][newsTagId] || ''
  // }catch{
  //   _newsTagId = ''
  // }
  const res = await request({
    url: '/intelligence/tag/queryNewsTagById',
    method: 'get',
    params: {
      // newsTagId:_newsTagId || '55',
      ...otherQuey,
      startDate,
      endDate
    }
  })
  const { code = '' } = res
  if (code === 200) {
    return res
  } else {
    return []
  }
}

// 报告区

const PARAMSKEYVUE = {
  ambience: '竞争环境',
  macro: '宏观环境',
  economy: '经济环境',
  policy: '政策环境',
  estate: '产业环境',
  market: '市场环境',
  vehicle: '商用车',
  marketmachine: '通机',
  electricity: '船电',
  marketenergy: '新能源',
  marketpower: '细分动力'
}

export const getQueryFileList = async query => {
  const { date = '' } = query
  let { hash: pathname = '', pathname: p } = location
  // console.log(pathname,'pathname')
  pathname = pathname.indexOf('ambience') > -1 ? pathname : p

  const values = pathname.split('/')
  //  console.log(values,'00000000')
  const [_, path1, path2, path3] = values

  let startDate = ''
  let endDate = ''
  if (date) {
    startDate = date[0]
    endDate = date[1]
  }

  const res = await request({
    url: '/intelligence/file/list',
    method: 'get',
    params: {
      dictModuleId: PARAMSKEYVUE[path1] || '', //"竞争环境",
      dictLeftMenuId: PARAMSKEYVUE[path2] || '', // "宏观环境",
      dictNewsId: PARAMSKEYVUE[path3] || '', // "经济环境",
      fileType: '02',
      's_date': startDate,
      'e_date': endDate,
      ...query
    }
  })
  const { code = '' } = res
  if (code === 200) {
    return res
  } else {
    return []
  }
}

export const getQueryFileLisdetail = async query => {
  const res = await request({
    url: '/intelligence/file/detail',
    method: 'get',
    params: {
      ...query
    }
  })
  const { code = '' } = res
  if (code === 200) {
    return res
  } else {
    return []
  }
}

export const getViewNewsInfo = async ({ id, newsTagId }) => {
  const res = await request({
    url: `/intelligence/newsinfo/viewNewsInfo?id=${id}&newsTagId=${newsTagId}`,
    method: 'post'
    // data: {
    //   id,
    //   newsTagId:`${newsTagId}`
    // }
  })
  const { code = '' } = res
  if (code === 200) {
    return res
  } else {
    return []
  }
}
