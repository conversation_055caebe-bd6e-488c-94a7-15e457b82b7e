import request from '@/utils/request'
import { getYAxisName, xAxisMonth } from '@/views/ambience/components/commonConfigData'
import { ElMessage } from 'element-plus'
import { splitDate } from './macro'
import { changeYearMonth, sortMonth, sliceync } from './market'
import { dataConvertForPercentTopN } from '../../utils/dataconvert.js'

const changeData = dataRes => {
  const { seriesList, yms } = sliceync(dataRes)

  const _yms = changeYearMonth([...yms])

  const newReas = seriesList?.map(_item => {
    const { name, saleslistTotal: sales } = _item

    return {
      // 每条线的名称
      name: name,
      // 每条线的数据
      data: sales || [],
      // 展示成什么图形
      type: 'line',
      symbolSize: 1
    }
  })

  const chart1 = {
    title: {
      text: '销量走势'
    },
    xAxis: {
      data: _yms
    },
    yAxis: {
      type: 'value',
      name: getYAxisName('台')
    },
    series: newReas
  }

  const newReas1 = seriesList?.map(_item => {
    const { name, slicelistTotal: slice = [] } = _item

    return {
      data: slice, //[30, 50, 44, 25, 30, 20, 10, 30, 20, 20, 10, 25],
      name: name,
      type: 'bar',
      stack: 'total',
      barWidth: '40%'
    }
  })

  const chart2 = {
    title: {
      text: '占比走势'
    },
    xAxis: {
      data: _yms
    },
    yAxis: {
      type: 'value',
      max: 100,
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: newReas1
  }

  return {
    chart1,
    chart2
  }
}

// 8.1	细分销量/占比
export const getShipElecSaleSliceListt = async query => {
  const { date = undefined, ...otherQuery } = query
  const newDate = splitDate(date)

  const params = {
    ...newDate,
    'pointerType': 0,
    dataSource: '6',
    ...otherQuery
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/shipElecSaleSliceList',
      method: 'post',
      data: params
    })
    const { code = '', data = [], msg = '' } = res
    if (code === 200) {
      const _res = changeData(data)
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

const changeTrendList = (resData = []) => {
  let serData = {}
  const XName = resData?.map(item => {
    const { name, data = [] } = item

    data.map(ch => {
      const { engineManufacturer = '' } = ch
      if (
        serData?.[engineManufacturer]?.engineManufacturer &&
        serData?.[engineManufacturer]?.engineManufacturer === engineManufacturer
      ) {
        const facturerData = serData?.[engineManufacturer]?.data
        const _data = serData?.[engineManufacturer]
        serData = {
          ...serData,
          [engineManufacturer]: {
            ..._data,
            data: [
              ...facturerData,
              {
                ...ch
              }
            ]
          }
        }
      } else {
        serData = {
          ...serData,
          [engineManufacturer]: {
            engineManufacturer,
            data: [
              {
                ...ch
              }
            ]
          }
        }
      }
    })
    return name
  })

  let newReas = Object.values(serData)
  const newYearMonth = sortMonth(XName)
  const _XName = changeYearMonth(XName)
  newReas = newReas?.map(item => {
    const { engineManufacturer, data = [] } = item
    let saleslistTotal = []
    const saleslist = newYearMonth?.map((y, index) => {
      let _slice = ''
      const list = data?.filter(({ yearMonth: m = '' }) => m === y)
      if (list && list.length > 0) {
        let _S = list[0]?.slice || list[0]?.lsice || ''
        _slice = _S && _S?.indexOf('%') > -1 ? _S?.replace(new RegExp('%', 'g'), '') : _S
      }

      saleslistTotal.push({
        name: _XName[index],
        value: _slice
      })

      return _slice
    })

    return {
      name: engineManufacturer,
      stack: 'total',
      barWidth: '40%',
      type: 'bar',
      data: saleslistTotal //saleslist
    }
  })

  const obj = {
    title: {
      text: '动力厂家占比'
    },
    xAxis: {
      data: changeYearMonth(XName)
    },
    yAxis: {
      type: 'value',
      max: 100,
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: newReas
  }

  return obj
}

// 8.1	趋势
export const getShipElecFacturerSliceList = async query => {
  const { date = undefined, ...otherQuery } = query
  const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  const params = {
    ...newDate,
    'pointerType': 0,
    dataSource: '6',
    ...otherQuery
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/shipElecFacturerSliceList',
      method: 'post',
      data: params
    })
    const { code = '', data = [], msg = '' } = res
    // console.log(data,'data')
    if (code === 200) {
      const _res = changeTrendList(data)
      // console.log(_res, 'data')
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

/**
 * step1.获取所有列表的name
 */
const getAllName = (list, key) => {
  const response = []
  if (list && list.length > 0) {
    for (let i = 0; i < list.length; i++) {
      let engineList = list[i] && list[i][key.listKey] ? list[i][key.listKey] : []
      if (!key.listKey) engineList = list[i] ?? []
      for (let j = 0; j < engineList.length; j++) {
        response.push(engineList[j][key.nameKey])
      }
    }
  }
  return Array.from(new Set(response))
}

/**
 * step2.每个组别渲染包含所有x轴数据
 {
      listKey: 'engineList',
      nameKey: 'engineFactory',
      dataNameKey: 'manuFacturer',
      dataValueKey: 'engineSales'
    }
 */
const flatAllName = (list, key, allNameArr) => {
  if (list && list.length === 0) return []
  const response = JSON.parse(JSON.stringify(list))
  if (list && list.length > 0) {
    list.forEach((el, elIndex) => {
      let children = []
      let responseChildren = []
      if (!key.listKey) {
        children = JSON.parse(JSON.stringify(el))
        responseChildren = response[elIndex]
      } else {
        if (!el) return
        children = JSON.parse(JSON.stringify(el[key.listKey]))
        if (children.length === 0) return
        responseChildren = response[elIndex][key.listKey]
      }

      const childrenNameArray = children.map(v => v[key.nameKey])
      allNameArr.forEach((i, iIndex) => {
        if (childrenNameArray.indexOf(i) === -1) {
          responseChildren.push({
            ...key.dataObj,
            [[key.nameKey]]: i,
            [key.dataNameKey]: children[0][key.dataNameKey],
            [key.dataValueKey]: 0
          })
        }
      })
    })
    return response
  }
}

/**
 * @description 组装charts第一层数据集
 * @param list 接口返回数据
 * @param key name的key值
 */
function firstChartDataFormatter(list, key) {
  const response = []
  if (list && list.length > 0) {
    for (let i = 0; i < list.length; i++) {
      const engineList = list[i][key.listKey]
      for (let j = 0; j < engineList.length; j++) {
        response.push({ name: engineList[j][key.nameKey], data: [] })
      }
      break
    }
  }
  return response
}
/**
 * @description step3.组装charts完整数据
 * @param list 接口返回数据
 * @param key name的key值
 */
function chartDataFormatter(list, key, firstLevel) {
  const response = firstLevel ? firstLevel : firstChartDataFormatter(list, key)
  if (list && list.length > 0) {
    for (let i = 0; i < list.length; i++) {
      let engineList = []
      if (!key.listKey) {
        engineList = list[i] ?? []
      } else {
        engineList = list[i] ? list[i][key.listKey] : []
      }

      for (let j = 0; j < engineList.length; j++) {
        const hasNameIndex = response.findIndex(e => (e || {}).name === engineList[j][key.nameKey])
        if (hasNameIndex !== -1) {
          // 本页面用的表2
          const latterName =
            engineList[j][key.dataNameKey]?.length === 4 && key.dataNameLastFormatter
              ? key.dataNameLastFormatter
              : key.dataNameFormatter
                ? key.dataNameFormatter
                : ''
          const name = engineList[j][key.dataNameKey] + latterName
          response[hasNameIndex].data.push({
            name,
            value: engineList[j][key.dataValueKey],
            ...engineList[j]
          })
        }
      }
    }
  }
  return response
}

function fillXAxisList(list, defaultList) {
  const response = JSON.parse(JSON.stringify(list))
  if (response.length === 0) {
    return [{ name: '', data: defaultList }]
  }
  response.forEach(element => {
    const ElementList = element.data
    defaultList.forEach((el, ind) => {
      if (el.name !== ElementList[ind]?.name) {
        ElementList.splice(ind, 0, { ...defaultList[ind] })
      }
    })
  })
  return response
}

// 柱状图数据构建1
const getSeriesDataByBar = (
  params = {},
  patternRsList = [],
  xCode = 'month',
  yCode = 'type',
  valueCode = 'proportion',
  isTop = true
) => {
  // 根据查询条件计算仅三年
  const year =new Date().getFullYear()
    const currentYear = new Date().getFullYear();
  const showYears = []

  for (let i = 0; i < 3; i++) {
    showYears.push({ name: year - i + '年', value: 0 })
  }
  showYears.reverse()

  // const showYears = data
  // .flat() // 将二维数组扁平化为一维数组
  // .map(item => item.year) // 提取 year 字段
  // .filter(year => year !== undefined && year.trim() !== '') // 过滤掉 undefined 和空值
  // .filter((value, index, self) => self.indexOf(value) === index) // 去重
  // .sort((a, b) => a - b); // 排序
  // 获取所有列表的分类
  let allNameD = []
  if (patternRsList && patternRsList.length > 0) {
    // for (var i in patternRsList) {
    //   patternRsList[i] = getOtherNumber(patternRsList[i], yCode, valueCode)
    // }
    allNameD = getAllName(patternRsList, { nameKey: yCode })
  }
  // 组装一级结构
  const levelDataD = []
  allNameD.forEach(el => {
    levelDataD.push({ name: el, data: [] })
  })
  const dataObj = {}
  dataObj[yCode] = ''
  dataObj[valueCode] = ''
  dataObj[xCode] = ''
  // x轴分类保持每个分类都一样有数据
  const levelSecondDataD = flatAllName(
    patternRsList,
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataValueKey: valueCode,
      dataObj: dataObj
    },
    allNameD
  )
  // 生成charts数据
  let dataD = chartDataFormatter(
    JSON.parse(JSON.stringify(levelSecondDataD)),
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataNameFormatter: '年',
      dataValueKey: valueCode
    },
    JSON.parse(JSON.stringify(levelDataD))
  )
  console.log(dataD)
  dataD = fillXAxisList(dataD, showYears)
  const series = isTop ? dataConvertForPercentTopN(dataD, dataD.length, 1) : dataD
  // 去重
  series.forEach(dt => {
    const names = []
    dt.data?.forEach(ndt => {
      const inum = names.findIndex(s => s.name === ndt.name)
      if (inum > -1) {
        names[inum] = ndt.value > 0 ? ndt : names[inum]
      } else {
        names.push(ndt)
      }
    })
    dt.data = names
  })
  // 提取所有年份并去重
let yearsSet = new Set();
series.forEach(item => {
    item.data.forEach(yearData => {
        yearsSet.add(yearData.name);
    });
});

// 将年份转换为数组并排序
let sortedYears = Array.from(yearsSet).sort((a, b) => a.localeCompare(b));

// 根据排序后的年份重组数据
let sortedData = series.map(item => ({
    name: item.name,
    data: sortedYears.map(year => {
        let yearData = item.data.find(dataItem => dataItem.name === year);
        return yearData || { name: year, value: 0 };
    })
}));

  console.log(sortedData)

const recentData = sortedData.map(category => ({
  ...category,
  data: category.data.filter(yearData => parseInt(yearData.name) >= currentYear - 2)
}));
  return recentData
}

// 柱状图数据构建2
const getSeriesDataByBar2 = (
  patternRsList = [],
  xCode = 'month',
  yCode = 'type',
  valueCode = 'proportion',
  isTop = true,
  isFormat = false
) => {
  if (isFormat) {
    const quarterArray = {
      Q1: '第一季度',
      Q2: '第二季度',
      Q3: '第三季度',
      Q4: '第四季度'
    }
    patternRsList.forEach(arr => {
      arr.forEach(el => {
        el[xCode] = quarterArray[el[xCode]]
      })
    })
  }
  const showYears = []
  // 遍历数据构建X坐标、
  patternRsList.forEach(arr => {
    if (arr.length) {
      showYears.push({ name: arr[0].month + (isFormat ? '' : '月'), value: 0 })
    }
  })
  showYears.sort((a, b) => a.name - b.name) // 升序排序
  // 获取所有列表的分类
  let allNameD = []
  if (patternRsList && patternRsList.length > 0) {
    // for (var i in patternRsList) {
    //   patternRsList[i] = getOtherNumber(patternRsList[i], yCode, valueCode)
    // }
    allNameD = getAllName(patternRsList, { nameKey: yCode })
  }
  // 组装一级结构
  const levelDataD = []
  allNameD.forEach(el => {
    levelDataD.push({ name: el, data: [] })
  })
  const dataObj = {}
  dataObj[yCode] = ''
  dataObj[valueCode] = ''
  dataObj[xCode] = ''
  // x轴分类保持每个分类都一样有数据
  const levelSecondDataD = flatAllName(
    patternRsList,
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataValueKey: valueCode,
      dataObj: dataObj
    },
    allNameD
  )
  // 生成charts数据
  let dataD = chartDataFormatter(
    JSON.parse(JSON.stringify(levelSecondDataD)),
    {
      nameKey: yCode,
      dataNameKey: xCode,
      dataNameFormatter: isFormat ? '' : '月',
      dataValueKey: valueCode
    },
    JSON.parse(JSON.stringify(levelDataD))
  )
  dataD = fillXAxisList(dataD, showYears)
  const series = isTop ? dataConvertForPercentTopN(dataD, dataD.length, 1) : dataD
  // 去重
  series.forEach(dt => {
    const names = []
    dt.data?.forEach(ndt => {
      const inum = names.findIndex(s => s.name === ndt.name)
      if (inum > -1) {
        names[inum] = ndt.value > 0 ? ndt : names[inum]
      } else {
        names.push(ndt)
      }
    })
    dt.data = names
  })
  return series
}

// 柱状图数据构建3 新左边
const getSeriesDataByBar3 = (
  params = {},
  patternRsList = [],
  xCode = 'month',
  yCode = 'type',
  valueCode = 'proportion',
  isTop = true
) => {
  console.log('params', patternRsList)
  var yearsSet = new Set()
  var marketsSet = new Set()
  patternRsList.forEach(yearData => {
    yearData.forEach(item => {
      yearsSet.add(item.year)
      marketsSet.add(item.segmented_market)
    })
  })
  // 构建 series 数据
  var seriesData = markets.map(market => ({
    name: market,
    type: 'bar',
    stack: 'total',
    data: years.map(year => {
      var item = patternRsList
        .find(yd => yd.some(i => i.year === year))
        .find(i => i.segmented_market === market)
      return item ? item.sales : 0
    })
  }))
  console.log('seriesData', seriesData)
  return seriesData
}
const formateChartData = (data, structure) => {
  const response = [...structure]
  data.forEach(el => {
    response.forEach(item => {
      item.data.push({ name: el[item.dataNameKey], value: el[item.dataValueKey] })
    })
  })
  return response
}

// 柱状转化
const getSeriesDataByType = arr => {
  // 取第一项构建xy坐标轴
  const xArr = arr[0].map(m => m.year.toString())?.sort((a, b) => a - b)

  // const yArr = arr.map(m => m.name)
  // console.log(yArr, 'yArr');
  const barList = []
  xArr.forEach(x => {
    const yData = []
    arr.forEach(children => {
      children.forEach(third => {
        if (third.year.toString() === x.toString()) {
          yData.push({
            name: third.manufacturer,
            value: third.sales
          })
        }
      })
    })
    barList.push({
      name: x + '年',
      type: 'bar',
      barWidth: '20%',
      data: yData
    })
  })
  return barList
}
// left1
export const shipYearSaleTrend = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  try {
    const res = await request({
      url: '/intelligence/marketEnv/shipYearSaleTrend',
      method: 'post',
      data: query
    })
    const { code = '', data = [], msg = '' } = res
    if (code === 200) {
      const series = data.length
        ? getSeriesDataByBar(query, data, 'year', 'segmented_market', 'sales', true)
        : []
      return series
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

//  right1
export const shipMonthSaleTrend = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')
  const quarterArray = {
    Q1: '第一季度',
    Q2: '第二季度',
    Q3: '第三季度',
    Q4: '第四季度'
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/shipMonthSaleTrend',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const totalArr = data.yoyRsList.sort((a, b) => a.month - b.month) // 升序排序
      totalArr.forEach(el => {
        el.month = query.pointerType === '1' ? quarterArray[el.month] : `${el.month}月`
      })

      const trendList = formateChartData(totalArr, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'month',
          dataValueKey: 'rate',
          data: [],
          disTotal: true
        }
      ])
      const barList = getSeriesDataByBar2(
        data.patternRsList,
        'month',
        'segmented_market',
        'sales',
        true,
        query.pointerType === '1'
      )
      const returnData = []
      barList.forEach(bar => {
        bar.data.sort(
          (a, b) => parseInt(a.name.replace(/\D/g, '')) - parseInt(b.name.replace(/\D/g, ''))
        ) // 升序排序
        returnData.push({
          name: bar.name,
          type: 'bar',
          stack: 'total',
          data: bar.data
        })
      })
      return [...returnData, ...trendList]
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

// left2
export const shipManufacturerYearSaleTrend = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  try {
    const res = await request({
      url: '/intelligence/marketEnv/shipManufacturerYearSaleTrend',
      method: 'post',
      data: query
    })
    const { code = '', data = [], msg = '' } = res
    if (code === 200) {
      const series = data.length
        ? getSeriesDataByBar(query, data, 'year', 'manufacturer', 'sales', true)
        : []
      return series
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

// right2
export const shipManufacturerSlice = async query => {
  // const {
  //   date = undefined,
  //     ...otherQuery
  // } = query
  // const newDate = splitDate(date)
  // console.log(newDate,'newDate')

  try {
    const res = await request({
      url: '/intelligence/marketEnv/shipManufacturerSlice',
      method: 'post',
      data: query
    })
    const { code = '', data = {}, msg = '' } = res
    if (code === 200) {
      const arr = data.patternRsList.length ? getSeriesDataByType(data.patternRsList) : []
      const trendList = formateChartData(data.yoyRsList, [
        {
          name: '同比',
          type: 'line',
          dataNameKey: 'manufacturer',
          dataValueKey: 'rate',
          data: [],
          disTotal: true
        }
      ])
      return [...arr, ...trendList]
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}
