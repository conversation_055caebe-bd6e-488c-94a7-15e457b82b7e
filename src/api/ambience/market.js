import request from '@/utils/request'
import {
  getYAxisName,
  changeDataType
} from '@/views/ambience/components/commonConfigData'
import {
  ElMessage
} from 'element-plus'
import { splitDate  } from "./macro";
const monthNumber = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']

export const JI={
  'Q1':'第一季度',
  'Q2':'第二季度',
  'Q3':'第三季度',
  'Q4':'第四季度',
  '01':'第一季度',
  '02':'第二季度',
  '03':'第三季度',
  '04':'第四季度',
}

export const  getYearMonthBu = (yearMonths,pef = '-')=>{
 let mons =  yearMonths?.map((item)=>{
    let [key,key2] = item.split(pef)
    let mon = key2 && key2*1 <10 ? `${key}${pef}0${key2 *1 }`:`${key}${pef}${key2}`
    return mon
  })
  // console.log(mons,'mons')
  return mons
}

export const  getYearMonthBu2 = (yearMonth,pef = '-')=>{
     let [key,key2] = yearMonth.split(pef)
     let mon =  key2 && key2*1 < 10 ? `${key}${pef}0${key2 *1 }`:`${key}${pef}${key2}`
   return mon
 }

export const sortMonth = (yearMonths)=>{

  const _yms = yearMonths.sort((a,b)=>{
    if(a?.indexOf('Q') > -1){
      let _a = a?.replace(new RegExp("'Q", 'g'), '') || ''
      let _b = b?.replace(new RegExp("'Q", 'g'), '') || ''
       return Number(_a) - Number(_b)
    }
    let _a = a?.replace(new RegExp("'", 'g'), '') || ''
    let _b = b?.replace(new RegExp("'", 'g'), '') || ''
    return Number(_a) - Number(_b)
  })

  return _yms
}



export const changeYearMonth = (yearMonths,KEYVALUE=['年','月'])=>{
  const _yms = yearMonths.sort((a,b)=>{
    if(a?.indexOf('Q') > -1){
      let _a = a?.replace(new RegExp("'Q", 'g'), '') || ''
      let _b = b?.replace(new RegExp("'Q", 'g'), '') || ''
       return Number(_a) - Number(_b)
    }


    if(a?.indexOf("-") > -1){
      let _a = a?.replace(new RegExp("-", 'g'), '') || '0'
      let _b = b?.replace(new RegExp("-", 'g'), '') || '0'
       return Number(_a) - Number(_b)
    }


    let _a = a?.replace(new RegExp("'", 'g'), '') || ''
    let _b = b?.replace(new RegExp("'", 'g'), '') || ''
    return Number(_a) - Number(_b)
  })

  let mark = ''
  const new_yms =  _yms.map((item)=>{
    let pre = "'"
    if(item.indexOf('-')>-1){
      pre = '-'
    }
    let [ year = '',month = '' ,date = '']  = item?.split(pre)
    const Q = month
    const [ value1,value2 ] = KEYVALUE 
    month = month?.replace(/\b(0+)/gi,"")
    month = month ? `${month}${value2}` : ''
    let ym = ''
   
    if(mark !== year){
      mark = year
      ym = `${year}${value1}`
    }
    if(item?.indexOf('Q') > -1){
      month = JI[Q]
    }
    ym = `${ym}${month}`

    if(!ym){
      ym = `${year}${value1}`
    }
    if(date){
      ym = `${ym}${date}日`
    }
    
    return ym
  })

  return new_yms

}

export const changeYearMonth1 = changeYearMonth


export const sliceync = (chartData)=>{
  let yms = new Set()
  chartData?.map((item)=>{
    const { data = [] } = item
    // console.log(item,'item')
    data?.map(( {yearMonth = '',})=>{
      yms.add(yearMonth)
    })
  })


  const _yms2 =  sortMonth([...yms])


  const seriesList = chartData.map((item)=>{
    const { name,data = [] } = item
      const saleslist =  _yms2.map((y)=>{
        const list = data?.filter(({ yearMonth:m = ''})=>(m === y))
        if(list && list.length>0){
          return `${list[0]?.sales}` || 0
        }
        return ''

      })
      let saleslistTotal = []
      let slicelistTotal = [];// name,value形式
      let _yms1 = changeYearMonth([...yms])
     

      const slicelist = _yms2.map((y,index)=>{
        const list = data?.filter(({ yearMonth:m = ''})=>(m === y))
        let value = ''
        let value2 = ''
        let _sales = ''
        let _slice = ''
        if(list && list.length>0){

          let _S = `${list[0]?.slice}` || 0
           _sales = `${list[0]?.sales}` || 0

            _slice = _S && _S?.indexOf('%') > -1 ? _S?.replace(new RegExp('%', 'g'), '') :_S
            
            value=_slice
            value2 = _sales

        }
         
        slicelistTotal.push({
           name:_yms1[index],
           value
        })
        saleslistTotal.push({
          name:_yms1[index],
          value:value2
        })
        return _slice
      })


      return {
        name,
        sersice:slicelist,
        saleslistTotal,

        sersiceTotal:saleslist,
        slicelistTotal,
        
      }

  })

  return {seriesList,yms}

}


const changeData = (chartData = [])=>{

  const {seriesList,yms} =  sliceync(chartData)

  // 百分比
  const sliceChart = {
    height:'16%',
    title:{
      text:'细分占比趋势'
    },
    xAxis:{
      data:changeYearMonth([...yms])
    },
    yAxis:{
      type: "value",
      name:getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series:seriesList?.map(( {slicelistTotal:sersice = [],name = '' } )=>{
      return {
        name,
        data:sersice || [],
        type: "bar",
        stack: "total",
        barWidth: "40%",
        
      }

    })
  }

  // 总销量

  const totlaChart = {
    height:'16%',
    title:{
      text:'细分销量趋势'
    },
    xAxis:{
      data:changeYearMonth([...yms])
    },
    yAxis:{
      type: "value",
      name:getYAxisName('台')
    },
    series:seriesList?.map(( {saleslistTotal:sersiceTotal = [],type = '',name = '' } )=>{
      return {
        type: "line",
        name,
        symbolSize:1,
        data:sersiceTotal || []
      }

    })
  }


  return {
    sliceChart,
    totlaChart
  }


}


//  获取商用车图表数据
export const getCommVehicleSalesSliceList = async (query) => {

  const {
    date = undefined,
    dataType='',
      ...otherQuery
  } = query
  const newDate = splitDate(date)
  const  _dataType= changeDataType(dataType)
  const params = {
    ...newDate,
    dataType:_dataType,
    ...otherQuery,

  }


  try {
    const res = await request({
      url: '/intelligence/marketEnv/commVehicleSalesSliceList',
      method: 'post',
      data: params
    })
    const {
      code = '',
        data = [],
        msg = ''
    } = res
    if (code === 200) {
      const _res =  changeData(data)
      console.log('_res: ', _res);

    // console.log(_res,'resres')
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error',
      })
      return []
    }
  } catch (error) {
    console.log(error,'error')
    return []
  }


}

const changeShipData = (resData = [],pointerType = '')=>{
  let serData = {}
 const XName =  resData?.map((item)=>{
    const {name, data=[] } = item

   data.map((ch)=>{
      const { engineManufacturer = '' } = ch
      if(serData?.[engineManufacturer]?.engineManufacturer && serData?.[engineManufacturer]?.engineManufacturer === engineManufacturer){
        const facturerData = serData?.[engineManufacturer]?.data;
        const _data = serData?.[engineManufacturer]
        serData = {
          ...serData,
          [engineManufacturer]:{
            ..._data,
            data:[
              ...facturerData,
              {
                ...ch
              }
            ]
          }
        }
      }else{
        serData = {
          ...serData,
          [engineManufacturer]:{
            engineManufacturer,
            data:[{
              ...ch,
            }]
          }
        }
      }
    })

    return name

  })

  const newYearMonth = sortMonth(XName)

  let newReas = Object.values(serData)
  const XNameName = changeYearMonth(XName)
  newReas = newReas?.map((item)=>{
    const { engineManufacturer ,data = [] } = item
    let _saleslist = []
    const saleslist =  newYearMonth?.map((y,index)=>{
        const list = data?.filter(({ yearMonth:m = ''})=>(m === y))
        let _slice = ''
        if(list && list.length>0){
          let _S = list[0]?.slice || list[0]?.lsice || ''
           _slice = _S && _S?.indexOf('%') > -1 ? _S?.replace(new RegExp('%', 'g'), '') :_S
        }
        _saleslist.push({
          name:XNameName[index],
          value:_slice
        })

        return _slice

    })

    return {
      name:engineManufacturer,
      stack: "total",
      barWidth: "40%",
      type: "bar",
      data:_saleslist,//saleslist
    }

  })

  
   const obj = {
    height:'16%',
    title:{
      text:'市场发动机竞争趋势'
    },
    xAxis:{
      data:changeYearMonth(XName),
    },
    yAxis:{
      type: "value",
      max: 100,
      name:getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series:newReas
  }

  return obj;


}



// 季度
export const getCommVehicleEngineCptTrendList = async (query) => {
  const {
    date = undefined,
    dataType='',
      ...otherQuery
  } = query
  const newDate = splitDate(date)
  const  _dataType= changeDataType(dataType)

  const params = {
    ...newDate,
    dataType:_dataType,
    ...otherQuery,
    // pointerType:'1',
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/commVehicleEngineCptTrendList',
      method: 'post',
      data: params
    })
    const {
      code = '',
        data = [],
        msg = ''
    } = res
    if (code === 200) {
      const _res =  changeShipData(data,query?.pointerType)
      // console.log(_res, 'data11111')
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error',
      })
      return {}
    }
  } catch (error) {
    console.log(error,'error')
    return {}
  }


}





