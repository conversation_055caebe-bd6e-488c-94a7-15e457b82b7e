import request from '@/utils/request'
import biRoutes from "../../mock/menu"

// 获取路由
export const getRouters = () => {
  return request({
    url: '/system/menu/getRouters',
    method: 'get'
  })
}

export const getBiRouters = () => {
  // return request({
  //   url: '/system/menu/getBiRouters',
  //   method: 'get'
  // })
  return new Promise((resolve,reject)=>{
    resolve(biRoutes[0].response)})
}