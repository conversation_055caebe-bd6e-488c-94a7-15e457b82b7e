import request from '@/utils/request'

// 新闻查询列表
export function newsList(query) {
  return request({
    url: '/rival/yuchai/news/list',
    method: 'get',
    params: query
  })
}
// 报告区列表
export function getReportList(query) {
  return request({
    url: '/rival/yuchai/report/list',
    method: 'get',
    params: query
  })
}
// 玉柴图表列表
export function getEchart(query) {
  return request({
    url: '/rival/yuchai/echart',
    method: 'get',
    params: query
  })
}
// 潍柴图表列表
export function getEchartWeichai(query) {
  return request({
    url: '/rival/weichai/echart',
    method: 'get',
    params: query
  })
}
// 康明斯图表列表
export function getEchartKangmingsi(query) {
  return request({
    url: '/rival/kangmingsi/echart',
    method: 'get',
    params: query
  })
}
// 云内图表列表
export function getEchartYunnei(query) {
  return request({
    url: '/rival/yunnei/echart',
    method: 'get',
    params: query
  })
}
// 全柴图表列表
export function getEchartQuanchai(query) {
  return request({
    url: '/rival/quanchai/echart',
    method: 'get',
    params: query
  })
}
// 新柴图表列表
export function getEchartXinchai(query) {
  return request({
    url: '/rival/xinchai/echart',
    method: 'get',
    params: query
  })
}
// 久保田图表列表
export function getEchartJiubaotian(query) {
  return request({
    url: '/rival/jiubaotian/echart',
    method: 'get',
    params: query
  })
}
// 洋马图表列表
export function getEchartYangma(query) {
  return request({
    url: '/rival/yangma/echart',
    method: 'get',
    params: query
  })
}
// 铂金斯图表列表
export function getEchartBojinsi(query) {
  return request({
    url: '/rival/bojinsi/echart',
    method: 'get',
    params: query
  })
}