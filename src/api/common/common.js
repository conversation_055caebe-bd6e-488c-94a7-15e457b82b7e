// 公共组件所需接口
import request from '@/utils/request'

// 获取新闻列表
export function getNewsList(query) {
  return request({
    url: '/intelligence/tag/queryNewsTagById',
    method: 'get',
    params: query
  })
}

// 获取新闻tab列表
export function getTabList(query) {
  return request({
    url: '/intelligence/tag/list',
    method: 'get',
    params: query
  })
}

// 报告区列表
export function getReportList(query) {
  return request({
    url: '/intelligence/file/list',
    method: 'get',
    params: query
  })
}
// 报告区列表
export function getReportListByAuth(query) {
  return request({
    url: '/intelligence/file/listByAuth',
    method: 'get',
    params: query
  })
}

// 竞争对手数据区
// 商用车/通机
export function getCompetitorList(data) {
  return request({
    url: '/intelligence/cmperData/competitorList',
    method: 'post',
    data: data
  })
}
// 新能源
export function getCompetitorNewEnergyList(data) {
  return request({
    url: '/intelligence/cmperData/competitorNewEnergyList',
    method: 'post',
    data: data
  })
}
