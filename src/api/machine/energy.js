import request from '@/utils/request'
////////////////////////////////////////1
/**
 * @description 主机厂客户-新能源：{xxx}细分⻋型销量趋势left
 * @returns Object
 */
export const hostNewEnergyWeightYearDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyWeightYearData`,
    method: 'POST',
    data
  })
}
/**
 * @description 主机厂客户-新能源：{xxx}细分⻋型销量趋势right
 * @returns Object
 */
export const hostNewEnergyWeightMonthDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyWeightMonthData`,
    method: 'POST',
    data
  })
}
////////////////////////////////////////2
/**
 * @description 主机厂客户-新能源：{xxx}细分市场销量趋势left
 * @returns Object
 */
export const hostNewEnergyBreedYearDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyBreedYearData`,
    method: 'POST',
    data
  })
}
/**
 * @description 主机厂客户-新能源：{xxx}细分市场销量趋势right
 * @returns Object
 */
export const hostNewEnergyBreedMonthDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyBreedMonthData`,
    method: 'POST',
    data
  })
}
////////////////////////////////////////3
/**
 * @description 主机厂客户-新能源：{xxx}燃料结构趋势left
 * @returns Object
 */
export const hostNewEnergyDieselYearDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyDieselYearData`,
    method: 'POST',
    data
  })
}
/**
 * @description 主机厂客户-新能源：{xxx}燃料结构趋势right
 * @returns Object
 */
export const hostNewEnergyDieselMonthDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyDieselMonthData`,
    method: 'POST',
    data
  })
}
////////////////////////////////////////4
/**
 * @description 主机厂客户-新能源：{xxx}动力配套结构趋势left
 * @returns Object
 */
export const hostNewEnergyEngineYearDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyEngineYearData`,
    method: 'POST',
    data
  })
}
/**
 * @description 主机厂客户-新能源：{xxx}动力配套结构趋势right
 * @returns Object
 */
export const hostNewEnergyEngineMonthDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyEngineMonthData`,
    method: 'POST',
    data
  })
}
////////////////////////////////////////5
/**
 * @description 主机厂客户-新能源：{xxx}省份区域销量
 * @returns Object
 */
export const hostNewEnergyProvinceDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyProvinceData`,
    method: 'POST',
    data
  })
}
////////////////////////////////////////6
/**
 * @description 主机厂客户-新能源：{xxx}top10城市销量
 * @returns Object
 */
export const hostNewEnergyCityDataApi = data => {
  return request({
    url: `intelligence/newEnergyMarket/hostNewEnergyCityData`,
    method: 'POST',
    data
  })
}

/**
 * @description 主机厂客户-新能源：获取热门top 10厂商
 * @param {*} data 
 * @returns 
 */
export const getManufacturersByRank = async data => {
  return await request({
    url: `intelligence/newEnergyMarket/manufacturersByRank`,
    method: 'POST',
    data
  })
}