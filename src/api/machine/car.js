import request from '@/utils/request'

/**
 * @description 主机厂客户-商用车：XX细分市场销量趋势
 * @returns Object
 */
export const commVehicleSalesTrendApi = (data) => {
  return request({
    url: `intelligence/hostCustomer/commVehicle/salesTrend`,
    method: 'POST',
    data
  })
}
/**
 * @description 主机厂客户-商用车：XX燃料结构趋势
 * @returns Object
 */
export const commVehicleFuelTrendApi = (data) => {
  return request({
    url: `intelligence/hostCustomer/commVehicle/fuelTrend`,
    method: 'POST',
    data
  })
}
/**
 * @description 主机厂客户-商用车：XX动力配套结构趋势
 * @returns Object
 */
export const commVehicleMatchTrendApi = (data) => {
  return request({
    url: `intelligence/hostCustomer/commVehicle/matchTrend`,
    method: 'POST',
    data
  })
}

/**
 * @description 主机厂客户-商用车：XX动力配套结构趋势
 * @returns Object
 */
export const commVehicleAreaSales = (data) => {
  return request({
    url: `intelligence/hostCustomer/commVehicle/areaSales`,
    method: 'POST',
    data
  })
}