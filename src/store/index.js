import { createStore } from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import biapp from './modules/biapp'
import bisettings from './modules/bisettings'
import macro from './modules/macro'
import power from './modules/power'
import dicts from './modules/dicts'
const store = createStore({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    biapp,
    bisettings,
    macro,
    power,
    dicts
  },
  getters
});


export default store