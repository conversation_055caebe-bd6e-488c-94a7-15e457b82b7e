
import { getMacroEnvEcoDataList,getMacroEnvIndDataList } from '@/api/ambience/macro'

export const getInitDate = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldYear = year-1;
   let newmonth = month-1
  let oldMonth = month >=10 ? month :`0${month}`;

  if((month - 12)===0){
    oldMonth = `12`;
    oldYear = year
  }
  if(newmonth===0){
    newmonth = '12';
    year = year-1
  }

 return [ `${oldYear}-${oldMonth}`,`${year}-${newmonth}` ]
}

export const getInitDate2 = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldMonth = month >=10 ? month :`0${month}`

  year = month > 3 ? year : year - 1
  let oldYear = year-3;
  oldMonth = `01`;
  let newmonth = '12';

 return [ `${oldYear}-${oldMonth}`,`${year}-${newmonth}` ]
}



export const getInitYear = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldYear = year-1;
  let oldMonth = month >=10 ? month :`0${month}`

  let newmonth = month-1

  if((month - 12)===0){
    oldMonth = `01`;
    oldYear = year
  }
  if(newmonth===0){
    newmonth = '12';
    year = year-1
  }


 return [ `${oldYear}`,`${year}` ]
}


export const getInitDate3 = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldMonth = month >=10 ? month :`0${month}`
  oldMonth = `01`;

  year = month > 3 ? year : year - 1
  month = month > 3 ? month : '12'
  let oldYear = year-3;

 return [ `${oldYear}-${oldMonth}`,`${year}-${month}` ]
}


export const getInityearNomonth = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;

  year = month > 3 ? year : year - 1
  let oldYear = year-3;

 return [ `${oldYear}`,`${year}` ]
}


const state = {
    // 经济环境图表数据
    economyChartList:[],
    // 经济环境入参
    economyParams:{
      date:getInitDate3()
    },
    // 加载图标
    economyLoading:false,
     // 加载图标
    estateLoading:false,
    // 产业环境数据
    estateChartList:[],
    // 产业环境入参
    estateParams:{
      date:getInitDate3()
    },
    

}
const mutations = {
  CHANGE_CHARTlIST: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
}

const actions = {
  // 获取经济环境
 async getChartList({commit,state}) {
    try {
      if(state.economyLoading)return
      state.economyLoading = true;
      commit('CHANGE_CHARTlIST', {
        key:'economyLoading',
        value:true
      })
      const params = state.economyParams
      const data = await getMacroEnvEcoDataList(params)

      // 打印实际的 key 值用于调试
      console.log('经济数据图表 keys:', data.map(item => ({ key: item.key, name: item.name })))

      // 按照指定顺序重新排序图表数据 - 基于图表名称匹配
      const chartNameOrder = [
        'GDP累计增速走势',                    // 1. 国内GDP累计增速走势
        '固定资产分行业投资累计增速',          // 2. 国内固定资产分行业投资累计增速
        '消费累计月度同比增速走势',            // 3. 国内消费累计月度同比增速走势
        '进出口市场累计增速',                 // 4. 进出口市场累计增速
        'CPI与PPI月度增速走势',               // 5. 国内CPI与PPI月度增速走势
        '制造业采购经理人指数月度走势',        // 6. 国内制造业采购经理人指数月度走势
        '消费者信心指数月度走势'              // 7. 国内消费者信心指数月度走势
      ]

      // 根据指定顺序重新排列数据
      const sortedData = []
      chartNameOrder.forEach(targetName => {
        const chart = data.find(item => {
          // 匹配图表名称，支持带"国内"前缀的情况
          const name = item.name || ''
          return name.includes(targetName) || name.replace('国内', '') === targetName
        })
        if (chart) {
          sortedData.push(chart)
        }
      })

      // 添加任何未在排序列表中的图表（保持原有数据完整性）
      data.forEach(item => {
        const isAlreadyAdded = sortedData.some(sortedItem => sortedItem.key === item.key)
        if (!isAlreadyAdded) {
          sortedData.push(item)
        }
      })

      commit('CHANGE_CHARTlIST', {
        key:'economyChartList',
        value:sortedData
      })
      commit('CHANGE_CHARTlIST', {
        key:'economyLoading',
        value:false
      })
    } catch (error) {
      commit('CHANGE_CHARTlIST', {
        key:'economyLoading',
        value:false
      })
      console.log(error,'error')
    }

  },
  // 产业环境
  async getMacroEnvIndData({commit,state}) {
    try {
      if(state.estateLoading)return
      await commit('CHANGE_CHARTlIST', {
        key:'estateLoading',
        value:true
      })
      const params = state.estateParams
      const data = await getMacroEnvIndDataList(params)
      commit('CHANGE_CHARTlIST', {
        key:'estateChartList',
        value:data
      })
      commit('CHANGE_CHARTlIST', {
        key:'estateLoading',
        value:false
      })
    } catch (error) {
      console.log(error,'error')
      commit('CHANGE_CHARTlIST', {
        key:'estateLoading',
        value:false
      })
    }
   
  },
  
 
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

