
import { getMacroEnvEcoDataList,getMacroEnvIndDataList } from '@/api/ambience/macro'

export const getInitDate = ()=>{
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldYear = year-1;
  let oldMonth = month >=10 ? month :`0${month}`
  if((month - 12)===0){
    oldMonth = `01`;
    oldYear = year
  }
 return [ `${oldYear}-${oldMonth}`,`${year}-${month}` ]
}

const state = {
    // 行业
    hangye:[],
    // 周
    week:[]
   
    

}
const mutations = {
  CHANGE_HANGYE: (state, hangye) => {
    // console.log(hangye,'---hangyehangyehangyehangye')
   state.hangye = hangye
  },
  CHANGE_WEEL: (state, week) => {
    state.week = week
   },
}

const actions = {
  // 行业
  getChartHangye({commit},hangye) {
    commit('CHANGE_HANGYE',hangye )
  },
  //  周
  getChartListWeek({commit,week}) {
    commit('CHANGE_WEEL',week )
  },


 
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

