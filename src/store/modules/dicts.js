import { selectOptionByDataSource } from '@/api/intelligence/dimDataSource.js'
import { getDicts } from '@/api/system/dict/data.js'
import cityData from '@/utils/area/city.js'
const DEFAULT_FLUE_A = [
  {
    value: '燃料电池',
    label: '燃料电池'
  },
  {
    value: '纯电动',
    label: '纯电动'
  },
  {
    value: '混合动力',
    label: '混合动力'
  }
]
const DEFAULT_FLUE_B = [
  {
    value: '柴油',
    label: '柴油'
  },
  {
    value: '气体',
    label: '气体'
  },
  {
    value: '其他',
    label: '其他'
  }
]
const DEFAULT_FLUE_C = [
  {
    value: '柴油',
    label: '柴油'
  },
  {
    value: '气体',
    label: '气体'
  },
  {
    value: '新能源（不含柴混、气混）',
    label: '新能源（不含柴混、气混）'
  }
]
// // 每个数组里面都又label value

// dataSource 数据来源
// const dataSource = [{
//    // 燃料
//   dieselGas: [],
//    // 燃料
//   fuelTypeYuchai: [],
//    // 板块
//   sector: [{
//     // 主机厂
//     chassisActualProducer2: [],
//     // 发动机厂
//     engineManufacturer: [],
//     // 细分市场一
//     segmentedMarket1: [{
//       breed: [], // 品系
//       // 细分市场二
//       segmentedMarket2: [{
//         label: '',
//         value: '',
//       }]
//     }]
//   }],
//   label: '',
//   value: ''
// }]

// 前端参数key值
const TRANSLATE_KEY = {
  dataSource: 'dataSource', // 数据来源
  segment: 'sector', // 板块
  subMarket1: 'segmentedMarket1', // 细分市场一
  subMarket2: 'segmentedMarket2', // 细分市场二
  engineFactory: 'engineManufacturer', // 发动机厂
  manuFacturer: 'chassisActualProducer2', // 主机厂
  fuelTypeA: 'fuelTypeYuchai', // 燃料类型1
  fuelTypeB: 'dieselGas' // 燃料类型2
}

// 接口返回的含有发动机厂/主机厂(engineManufacturer/chassisActualProducer2)的字段的key
const HAS_ENGINE_FACTORY_KEY = 'segmentedMarket1'
// 接口返回的含有燃料(fuelTypeYuchai/dieselGas)类型的父级key
const HAS_FUEL_TYPE_KEY = 'sector'
// 接口返回的含有品系/重中轻(breed/weightMidLight)类型的父级key
const HAS_BREED_KEY = 'segmentedMarket2'

const state = {
  cityData,
  dicts: {},  // sys_cylinder_count 缸数  sys_drive_form 驱动形式 sys_province 省份
  dictsDataSource: [] // 四级联动选择（数据来源 -> 板块 -> 细分市场一 -> 细分市场二 ）
}
const mutations = {
  SET_DICTS: (state, dicts) => {
    state.dictsDataSource = dicts
  },
  SET_SYSTEM_DICTS: (state, value) => {
    state.dicts = value
  },
  SET_SYSTEM_DICTS_ITEM: (state, { key, value }) => {
    state.dicts[key] = value
  }
}
const actions = {
  /**
   * 获取所有数据直接存储
   */
  getAllDictsData({ commit, dispatch }) {
    return new Promise(async resolve => {
      dispatch('getSystemDicts')
      let array = []
      // 有缓存值就不需要再调用接口
      if (sessionStorage.getItem('dataSource')) {
        commit('SET_DICTS', JSON.parse(sessionStorage.getItem('dataSource')))
        array = JSON.parse(sessionStorage.getItem('dataSource'))
        resolve(array)
      } else {
        // 数据源数组
        const dataSourceDataList = [
          '装机数',
          '友商数',
          '船电数',
          '海关数据',
          '中内协',
          '上险数',
          '货运新增数',
          '流向数'
        ]

        for (let index = 0; index < dataSourceDataList.length; index++) {
          const dataSource = dataSourceDataList[index]
          let { data } = await selectOptionByDataSource({ dataSource })
          // 去除fuelTypeYuchai的null数据
          if (data?.fuelTypeYuchai.length > 0) {
            data.fuelTypeYuchai = data.fuelTypeYuchai.filter(v => v.value !== null)
          }
          array.push(data)
        }
        commit('SET_DICTS', array)
        sessionStorage.setItem('dataSource', JSON.stringify(array))
        resolve(array)
      }
    })
  },
  /**
   * @description 组成符合多级联动的数据
   * @param {*} keyArray ['dataSource', 'segment', 'subMarket1', 'subMarket2'] 每一级的数值,必须有第一季
   * @returns 符合联动的多层嵌套数组
   */
  getLinkageData({ commit }, { keyArray, page }) {
    // console.log('state.dictsDataSource', JSON.parse(JSON.stringify(state.dictsDataSource)))
    const list = JSON.parse(JSON.stringify(state.dictsDataSource))
    const dictsKey = [] // 字典实际保存的key值(实际传递给后端的参数)
    keyArray.forEach(el => {
      dictsKey.push(TRANSLATE_KEY[el])
    })
    dictsKey.shift()
    const response = getDataSourceDicts(list, dictsKey, page)
    return response
  },
  /**
   * @description 获取指定数据来源的级联数据
   * @param {*} param {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['货运新增数', '装机数', '上险数']
    }
   * @returns 
   */
  async getDictsData({ dispatch }, params) {
    const response = []
    const { keyArray, dataSource, page } = params
    const list = await dispatch('getLinkageData', { keyArray, page })
    if (list && list.length > 0) {
      dataSource.forEach(element => {
        list.forEach(el => {
          if (el.label === element) {
            response.push(el)
          }
        })
      })
    }
    return response
  },
  // 获取系统设置的字典
  getSystemDicts({ commit, state }) {
    let sessionDicts = {}
    if (sessionStorage.getItem('systemDicts')) {
      sessionDicts = JSON.parse(sessionStorage.getItem('systemDicts'))
      commit('SET_SYSTEM_DICTS', sessionDicts)
      return
    }
    // sys_cylinder_count 缸数  sys_drive_form 驱动形式 sys_weight_mid_light 重中轻 sys_province 省份 sys_manufacturer_insure 主机厂_上险数 发动机型号 sys_engine_model
    const typeArray = ['sys_cylinder_count', 'sys_drive_form', 'sys_weight_mid_light', 'sys_province', 'sys_manufacturer_insure','sys_weight_mid_light_bus','sys_manufacturer_weight_mid_light','sys_manufacturer_install','sys_engine_model']
    for (let i = 0; i < typeArray.length; i++) {
      const param = typeArray[i]
      if ((sessionDicts[param] && sessionDicts[param].length > 0) || (state.dicts[param] && state.dicts[param].length > 0)) continue
      getDicts(param).then(res => {
        if (res.code === 200) {
          const data = res.data
          const arr = data.map(v => ({ label: v.dictLabel, value: v.dictValue }))
          commit('SET_SYSTEM_DICTS_ITEM', { key: param, value: arr })
          sessionStorage.setItem('systemDicts', JSON.stringify(state.dicts))
        }
      })
    }
  }
}

const getDataSourceDicts = (list, childrenKey, page, itemLabel, dataSource = {}) => {
  const res = []
  const childrenKeyCopy = JSON.parse(JSON.stringify(childrenKey))
  const currentKey = childrenKeyCopy.shift()
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    // 溯源找到数据来源是哪个
    if (currentKey === 'sector') {
      dataSource = JSON.parse(JSON.stringify(item))
    }
    // 过滤掉细分市场二的其他选项
    if (
      (currentKey === undefined && item.value === '其他') ||
      !item.value ||
      item.value === 'NULL' ||
      item.value === 'null'
    ) {
      continue
    }

    // 过滤1.中内协
    if (
      currentKey === 'segmentedMarket1' &&
      dataSource.label === '中内协' &&
      item.value === '乘用车'
    ) {
      continue
    }

    if (
      currentKey === 'segmentedMarket2' &&
      dataSource.label === '中内协' &&
      item.value === '其他'
    ) { 
      continue
    }

    // 过滤2.友商数
    if (
      currentKey === 'segmentedMarket1' &&
      dataSource.label === '友商数' &&
      (item.value === '基础机' || item.value === '机体缸盖总成')
    ) {
      continue
    }

    // 过滤3.货运新增数
    if (
      currentKey === 'segmentedMarket2' &&
      dataSource.label === '货运新增数' &&
      item.value === '客车'
    ) {
      continue
    }
    if (currentKey === undefined && dataSource.label === '货运新增数' &&
      item.value === '皮卡') {
      continue
    }

    // 过滤4.海关数据; 
    if (
      currentKey === 'segmentedMarket2' &&
      dataSource.label === '海关数据' &&
      (item.value === '乘用车' || item.value === '纯电卡车' || item.value === '皮卡')
    ) {
      continue
    }
    //  隐藏 板块里面的 皮卡，乘用车 
    if ( 
      currentKey === 'segmentedMarket1' && 
      dataSource.label === '海关数据' && (item.value === '皮卡' || item.value === '乘用车') 
    ) { 
      continue
    }
    // 细分市场1 里面的 其他 
    if ( 
      currentKey === 'segmentedMarket2' && 
      dataSource.label === '海关数据' && item.value === '其他' ) {
      continue
    }


    // 过滤5.装机数
    if (
      currentKey === undefined &&
      dataSource.label === '装机数' &&
      (item.value === '工程机(其它)' || item.value === '农机(其它)')
    ) {
      continue
    }

    // 过滤6.上险数
    // 过滤7.船电数

    const json = { label: item.label, value: item.value, children: [] }

    // console.log('item',item, currentKey)
    // const HAS_ENGINE_FACTORY_KEY = 'segmentedMarket1'
    // const HAS_FUEL_TYPE_KEY = 'sector'
    // const HAS_BREED_KEY = 'segmentedMarket2'

    // 处理添加 发动机厂/主机厂
    if (currentKey === HAS_ENGINE_FACTORY_KEY) {
      const engineFactory = item?.engineManufacturer ?? []
      json.engineFactory = engineFactory.filter(
        v =>
          v.value &&
          v.value !== 'null' &&
          v.value !== 'NULL' &&
          v.value !== 'MTU' &&
          v.value !== '/' &&
          v.value.indexOf('、云内') === -1
      )

      const manuFacturer = item?.chassisActualProducer2 ?? []
      json.manuFacturer = manuFacturer.filter(
        v =>
          v.value &&
          v.value !== 'null' &&
          v.value !== 'NULL' &&
          v.value !== '/' &&
          v.value.indexOf('、云内') === -1
      )
    }
    // 处理首页oem选中上险数主机厂选择字段不一样
    if (page === 'oem' && (itemLabel === '上险数' || itemLabel === '货运新增数')) {
      const automobileGroup = item?.automobileGroup ?? []
      json.manuFacturer = automobileGroup.filter(
        v => v.value && v.value !== 'null' && v.value !== 'NULL'
      )
    }
    // 处理添加 燃料类型
    // fuelTypeA: 'fuelTypeYuchai', // 燃料类型1
    // fuelTypeB: 'dieselGas' // 燃料类型2
    // 燃料类型（A-新能源 B-汽油）
    // 燃料类型只需固定值
    // 装机数无品系/燃料
    if (currentKey === HAS_FUEL_TYPE_KEY && dataSource.label !== '装机数') {
      // json.fuelTypeA = item?.fuelTypeYuchai ?? []
      // json.fuelTypeB = item?.dieselGas ?? []
      json.fuelTypeA = DEFAULT_FLUE_A
      json.fuelTypeB = DEFAULT_FLUE_B
      json.fuelTypeC = DEFAULT_FLUE_C
    }
    // weightMidLight 处理添加重中轻
    if (currentKey === HAS_BREED_KEY) {
      const weightMidLight = item?.weightMidLight ?? []
      json.weightMidLight = weightMidLight.filter(
        v =>
          v.value &&
          v.value !== 'null' &&
          v.value !== 'NULL' &&
          v.value !== '其他' &&
          v.value !== '皮卡或微卡'
      )
    }
    // breed 处理添加品系
    // 装机数无品系/燃料
    if (currentKey === HAS_BREED_KEY && dataSource.label !== '装机数') {
      const breed = item?.breed ?? []
      json.breed = breed.filter(
        v =>
          v.value &&
          v.value !== 'null' &&
          v.value !== 'NULL' &&
          v.value !== '其他' &&
          v.value !== '皮卡或微卡'
      )
    }

    if (currentKey && item[currentKey] && item[currentKey].length > 0) {
      json.children = getDataSourceDicts(item[currentKey], childrenKeyCopy, page, list[i].label, dataSource)
    }

    res.push(json)
  }
  return res
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
