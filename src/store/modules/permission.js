import { constantRoutes } from "@/router";
import { getRouters, getBiRouters } from "@/api/menu";
import Layout from "@/layout/index";
import BiLayout from '@/bilayout/index'
import ParentView from "@/components/ParentView";
import InnerLink from "@/layout/components/InnerLink";
// TODO: 菜单管理好了删除
import { routerBi, routerLayout } from '@/utils/common/routeMock.js'

// 匹配views里面所有的.vue文件
const modules = import.meta.glob("./../../views/**/*.vue");

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
    // Bi
    biAddRoutes: [],
    biDefaultRoutes: [],
    biTopbarRouters: [],
    biSidebarRouters: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes);
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      // 顶部导航菜单默认添加统计报表栏指向首页
      const index = [
        {
          path: "index",
          meta: { title: "统计报表", icon: "dashboard" },
        },
      ];
      state.topbarRouters = routes.concat(index);
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes;
    },
    // Bi
    SET_BI_ROUTES: (state, routes) => {
      state.biAddRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_BI_SIDEBAR_ROUTERS: (state, routes) => {
      state.biSidebarRouters = routes;
    },
    SET_BI_DEFAULT_ROUTES: (state, routes) => {
      state.biDefaultRoutes = constantRoutes.concat(routes);
    },
    SET_BI_TOPBAR_ROUTES: (state, routes) => {
      // 顶部导航菜单默认添加统计报表栏指向首页
      const index = [];
      state.biTopbarRouters = routes.concat(index);
    },

  },
  actions: {
    async GenerateRoutes({ commit }) {
      // const routers = await getRouters().catch((err) => err);
      const routers = { data: routerLayout }
      const sdata = JSON.parse(JSON.stringify(routers.data));
      const rdata = JSON.parse(JSON.stringify(routers.data));
      const defaultData = JSON.parse(JSON.stringify(routers.data));
      const sidebarRoutes = filterAsyncRouter(sdata);
      const rewriteRoutes = filterAsyncRouter(rdata, false, true);
      const defaultRoutes = filterAsyncRouter(defaultData);
      commit("SET_ROUTES", rewriteRoutes);
      commit("SET_SIDEBAR_ROUTERS", constantRoutes.concat(sidebarRoutes));
      commit("SET_DEFAULT_ROUTES", sidebarRoutes);
      commit("SET_TOPBAR_ROUTES", defaultRoutes);

      // BI
      // const biRouters = await getBiRouters().catch((err) => err);
      // const  biRouters = {data: routerBi}
      const menuRouters = await getRouters().catch((err) => err);
      let arr = menuRouters.data;
      // console.log('arr', arr)
      arr.forEach((item) => {//一级菜单重定向处理  
        const redirect = getFirstTagPath(item) 
        item.redirect = redirect
      })
      const biRouters = { data: [] }
      biRouters.data = arr;
      // console.log('菜单：',biRouters.data)
      const sdataBi = JSON.parse(JSON.stringify(biRouters.data));
      const rdataBi = JSON.parse(JSON.stringify(biRouters.data));
      const defaultDataBi = JSON.parse(JSON.stringify(biRouters.data));
      const sidebarRoutesBi = filterAsyncRouter(sdataBi);
      const rewriteRoutesBi = filterAsyncRouter(rdataBi, false, true);
      const defaultRoutesBi = filterAsyncRouter(defaultDataBi);
      commit("SET_BI_ROUTES", rewriteRoutesBi);
      // commit("SET_BI_SIDEBAR_ROUTERS", constantRoutes.concat(sidebarRoutesBi));
      commit("SET_BI_DEFAULT_ROUTES", sidebarRoutesBi);
      commit("SET_BI_TOPBAR_ROUTES", defaultRoutes);

      return [...rewriteRoutes, ...rewriteRoutesBi]
    },
    setBiSidebarRouters({ commit }, value) {
      commit("SET_BI_SIDEBAR_ROUTERS", value);
    },
    // 获取选择新闻的三级联动数据
    getNewsLinkageData({ rootState }, responseItem = ['国内竞争环境', '国际竞争环境', '竞争对手', '主机厂客户',  '终端市场']) {
      const res = dealLinkageData(rootState.permission.biDefaultRoutes, responseItem, 'first')
      return res
    }
    // 生成路由
    // GenerateRoutes({ commit }) {
    //   return new Promise(resolve => {
    //     // 向后端请求路由数据
    //     getRouters().then(res => {
    //       const sdata = JSON.parse(JSON.stringify(res.data))
    //       const rdata = JSON.parse(JSON.stringify(res.data))
    //       const defaultData = JSON.parse(JSON.stringify(res.data))
    //       const sidebarRoutes = filterAsyncRouter(sdata)
    //       const rewriteRoutes = filterAsyncRouter(rdata, false, true)
    //       const defaultRoutes = filterAsyncRouter(defaultData)
    //       commit('SET_ROUTES', rewriteRoutes)
    //       commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))
    //       commit('SET_DEFAULT_ROUTES', sidebarRoutes)
    //       commit('SET_TOPBAR_ROUTES', defaultRoutes)
    //       resolve(rewriteRoutes)
    //     })
    //   })
    // }
  }
};

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "BiLayout") {
        route.component = BiLayout;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else if (route.component === "InnerLink") {
        route.component = InnerLink;
      } else {
        route.component = loadView(route.component);
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = [];
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === "ParentView" && !lastRouter) {
        el.children.forEach((c) => {
          c.path = el.path + "/" + c.path;
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c));
            return;
          }
          children.push(c);
        });
        return;
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + "/" + el.path;
    }
    children = children.concat(el);
  });
  return children;
}

export const loadView = (view) => {
  let res;
  for (const path in modules) {
    const dir = path.split("views/")[1].split(".vue")[0];
    if (dir === view) {
      res = () => modules[path]();
    }
  }
  return res;
};

const dealLinkageData = (list, hasLinkageData, level) => {
  const response = []
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    if (
      item.hidden || !item.meta ||
      (level === 'first' && item.meta.title && hasLinkageData.indexOf(item.meta.title) === -1)
    )
      continue
    const json = {
      label: item.meta.title,
      value: item.meta.menuId + '',
      children: []
    }
    if (item?.children && item.children.length > 0) {
      json.children = dealLinkageData(item.children)
    }
    response.push(json)
  }
  return response
}
// 获取跳转的第一层路径
function getFirstTagPath(route, path = '') { 
  path += (route.path[0] === "/" ? '' : '/') + route.path
  if (route.children && route.children.length > 0) {
    return getFirstTagPath(route.children[0], path)
  } else {
    return path
  }
}
export default permission;
