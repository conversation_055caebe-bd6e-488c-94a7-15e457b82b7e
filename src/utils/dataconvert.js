import { numberFormat } from './format'

// 结构转换
var listToTable = list => {
  var tableInfo = []
  for (var i in list) {
    var datas = list[i].data
    var dataObj = {
      name: list[i].name
    }
    for (var j in datas) {
      dataObj[datas[j].name] = datas[j].value
    }
    tableInfo.push(dataObj)
  }
  return tableInfo
}

// 获取排序后的x轴，y轴顺序
var getSortedItems = list => {
  // 数据统计
  var yNumbers = {}
  var xNumbers = {}
  for (var i in list) {
    var datas = list[i].data
    var total = 0
    for (var j in datas) {
      total += datas[j].value
      xNumbers[datas[j].name] = xNumbers[datas[j].name] || 0
      xNumbers[datas[j].name] += datas[j].value
    }
    yNumbers[list[i].name] = total
  }
  // 排序
  var sortedYs = []
  for (var y in yNumbers) {
    sortedYs.push({
      name: y,
      value: yNumbers[y]
    })
  }
  sortedYs.sort((a, b) => {
    return b.value - a.value
  })
  var sortedXs = []
  for (var x in xNumbers) {
    sortedXs.push({
      name: x,
      value: xNumbers[x]
    })
  }
  sortedXs.sort((a, b) => {
    return b.value - a.value
  })
  return {
    sortedYs,
    sortedXs
  }
}

/**
 * 自定义需要展示的项，未定义的归为其他
 * @param {'[{name:"xxx",data:[{name:"x",value:1000}]}]'} list 需要处理的数据， 格式为[{name:"xxx",data:[{name:"x",value:1000}]}]
 * @param {'[string]'} yNames y轴项需要展示的项,为[]则展示所有
 * @param {'[string]'} xNames x轴项需要展示的项,为[]则展示所有
 * @returns  处理后的数据
 */
export const dataConvert = (list, yNames = [], xNames = []) => {
  // 结构转换
  var tableInfo = listToTable(list)

  // 数据过滤统计
  var newDataInfo = {}
  for (var m in tableInfo) {
    var cols = tableInfo[m]
    var name = cols.name
    for (var k in cols) {
      if (k === 'name') {
        continue
      }
      if (yNames.length == 0 || yNames.indexOf(name) > -1) {
        newDataInfo[name] = newDataInfo[name] || {}
        if (xNames.length == 0 || xNames.indexOf(k) > -1) {
          // newDataInfo[name][k] = cols[k]
          newDataInfo[name][k] = (Number(newDataInfo[name][k] || 0)) + (Number(cols[k]) || 0);
        } else {
          newDataInfo[name]['其他'] = newDataInfo[name]['其他'] || 0
          // newDataInfo[name]['其他'] += cols[k]
          newDataInfo[name]['其他'] = (Number(newDataInfo[name]['其他'])|| 0) + (Number(cols[k]) || 0);
        }
      } else {
        newDataInfo['其他'] = newDataInfo['其他'] || {}
        if (xNames.length == 0 || xNames.indexOf(k) > -1) {
          newDataInfo['其他'][k] = newDataInfo['其他'][k] || 0
          // newDataInfo['其他'][k] += cols[k]
          newDataInfo['其他'][k] = (Number(newDataInfo['其他'][k])|| 0) + (Number(cols[k]) || 0);
        } else {
          newDataInfo['其他']['其他'] = newDataInfo['其他']['其他'] || 0
          // newDataInfo['其他']['其他'] += cols[k]
          newDataInfo['其他']['其他'] = (Number(newDataInfo['其他']['其他'])|| 0) + (Number(cols[k]) || 0);
        }
      }
    }
  }

  // 结构转换
  var newList = []
  for (var l1 in newDataInfo) {
    var dataList = []
    var total = 0
    for (var l2 in newDataInfo[l1]) {
      total += newDataInfo[l1][l2]
      dataList.push({
        name: l2,
        value: parseFloat(newDataInfo[l1][l2]) 
      })
    }
    newList.push({
      name: l1,
      data: dataList
    })
  }

  var { sortedXs } = getSortedItems(newList)

  // x轴由大到小，其他放最后;获取数值排序顺序
  var sortXKey = []
  var hasXOther = false
  for (var j in sortedXs) {
    if (sortedXs[j].name == '其他') {
      hasXOther = true
    } else {
      sortXKey.push(sortedXs[j].name)
    }
  }
  if (hasXOther) {
    sortXKey.push('其他')
  }

  // y轴由小到大，其他放最后;获取数值排序顺序
  // y轴数据的排序由x轴第一项的排序结果决定
  var yFirstName = sortXKey[0]
  var sortedForY = []
  var hasYOther = false
  for (var i in newDataInfo) {
    if (i == '其他') {
      hasYOther = true
    } else {
      sortedForY.push({
        name: i,
        sortVal: newDataInfo[i][yFirstName]
      })
    }
  }
  sortedForY.sort((a, b) => {
    return b.sortVal - a.sortVal
  })
  var sortYKey = sortedForY.map(e => e.name)
  if (hasYOther) {
    sortYKey.push('其他')
  }

  // 结构转换
  var tempTable = listToTable(newList)
  var finalList = []
  for (var y in sortYKey) {
    var dataTemp = {}
    for (var i in tempTable) {
      if (tempTable[i].name == sortYKey[y]) {
        dataTemp = tempTable[i]
        break
      }
    }
    var tempList = {
      'name': sortYKey[y],
      data: []
    }
    for (var j in sortXKey) {
      tempList.data.push({
        name: sortXKey[j],
        value: dataTemp[sortXKey[j]]
      })
    }
    finalList.push(tempList)
  }

  return finalList
}

/**
 * 对数据按topN进行排序处理，不在topN的归为其他，且其他按要求展示
 * @param {'[{name:"xxx",data:[{name:"x",value:1000}]}]'} list 需要处理的数据， 格式为[{name:"xxx",data:[{name:"x",value:1000}]}]
 * @param {number} yTop y轴项需要展示的topN，其他展示在最上,为0则展示所有
 * @param {number} xTop x轴项需要展示的topN，其他暂时在最后,为0则展示所有
 * @returns 排序分组后的数据，topN之外的数据归为其他
 */
export const dataConvertForTopN = (list, yTop, xTop) => {
  // 排序
  var { sortedYs, sortedXs } = getSortedItems(list)
  yTop = yTop || sortedYs.length + 1
  xTop = xTop || sortedXs.length + 1
  // 取数
  var topYNames = []
  for (var y = 0; y < Math.min(yTop - 1, sortedYs.length); y++) {
    topYNames.push(sortedYs[y].name)
  }

  var topXNames = []
  for (var x = 0; x < Math.min(xTop - 1, sortedXs.length); x++) {
    topXNames.push(sortedXs[x].name)
  }
  return dataConvert(list, topYNames, topXNames)
}

/**
 * 展示项排序
 * @param {'[{name:"xxx",data:[{name:"x",value:1000}]}]'} list 需要处理的数据， 格式为[{name:"xxx",data:[{name:"x",value:1000}]}]
 * @param {number} sortIndex 按首还是尾为排序依据 0:首 1:尾。按时间排的需要按尾项进行排序
 * @returns  处理后的数据
 */
export const dataConvertForLine = (list, sortIndex) => {
  sortIndex = sortIndex || 0
  if (sortIndex != 0) {
    sortIndex = list[0].data.length - 1
  }
  list.sort((a, b) => {
    return parseFloat(b.data[sortIndex]?.value || 0) - parseFloat(a.data[sortIndex]?.value || 0)
  })

  var finalList = []
  var otherInfo = {}
  for (var i in list) {
    if (list[i].name == '其他') {
      otherInfo = list[i]
    } else {
      finalList.push(list[i])
    }
  }
  if (JSON.stringify(otherInfo) != '{}') {
    finalList.push(otherInfo)
  }
  
  return finalList
}
// 排序后数据函数
export const getSortData = (data, sortControl) => {
  // 检查是否需要排序：sortControl 存在且数据类型为 bar 或者未指定类型
  if (sortControl && (data[0]?.type === 'bar' || !data[0]?.type)) {
    // 计算各个数据总量
    let totalMap = {};
    // 遍历每个产品
    data.forEach(product => {
      // 遍历每个产品的数据项
      product.data.forEach(item => {
        // 如果总量映射中不存在该数据项的名称，则初始化为 0
        if (!totalMap[item.name]) {
          totalMap[item.name] = 0;
        }
        // 累加该数据项的值到总量映射中
        totalMap[item.name] += Number(item.value);
      });
    });
    // console.log('totalMap', totalMap);
    // 根据总量排序，将 '其他' 项放在最后
    var sortedCompanies = Object.keys(totalMap).sort((a, b) => {
      // 如果 a 是 '其他'，则将其排在 b 后面
      if (a === '其他') {
        return 1;
      }
      // 如果 b 是 '其他'，则将其排在 a 后面
      if (b === '其他') {
        return -1;
      }
      // 根据 sortControl 控制排序方向
      return sortControl * (totalMap[b] - totalMap[a]);
    });
    console.log('sortedCompanies', sortedCompanies);
    // 遍历每个产品
    data.forEach((el) => {
      // 根据排序后的公司名称重新排列每个产品的数据项
      let newSeriesData = sortedCompanies.map(company => {
        // 查找当前公司名称对应的数据项
        var found = el.data.find(item => item.name === company);
        // 如果找到则返回该数据项，否则返回默认值
        return found || { name: company, value: 0 };
      });
      // 更新当前产品的数据项
      el.data = newSeriesData;
    });
  }
  // 返回排序处理后的数据数组
  return data;
};
/**
 * 堆叠图自定义需要展示的项，未定义的归为其他
 * @param {'[{name:"xxx",data:[{name:"x",value:1000}]}]'} list 需要处理的数据， 格式为[{name:"xxx",data:[{name:"x",value:1000}]}]
 * @param {'[string]'} yNames y轴项需要展示的项,为[]则展示所有
 * @param {number} sortIndex 按首还是尾为排序依据 0:首 1:尾
 * @param {number} sortControl 排序控制 0:不排序 1:升序 -1:降序
 * @returns  处理后的数据
 */
export const dataConvertForPercent = (list, yNames = [], sortIndex, sortControl=0) => {
  if (yNames.length > 0 && list.length > 0) {
    var newList = []
    // 复制原来的数据的结构
    var otherInfo = JSON.parse(JSON.stringify(list[0]))
    otherInfo.name = '其他'
    otherInfo.data = []

    var ohterObj = {}
    for (var i in list) {
      if (yNames.indexOf(list[i].name) > -1) {
        newList.push(list[i])
      } else {
        var dataList = list[i].data
        for (var m in dataList) {
          ohterObj[m] = ohterObj[m] || { name: dataList[m].name, value: 0 }
          ohterObj[m].value += parseFloat(dataList[m].value || 0)
        }
      }
    }
    var otherData = []
    for (var m in ohterObj) {
      otherData.push({
        name: ohterObj[m].name,
        value: parseFloat(ohterObj[m].value)
      })
    }
    if (otherData.length) {
      otherInfo.data = otherData
      newList.push(otherInfo)
    }
    list = newList
  }
  return getSortData(dataConvertForLine(list, sortIndex), sortControl)
}

/**
 * 堆叠图TOPN
 * @param {'[{name:"xxx",data:[{name:"x",value:1000}]}]'} list 需要处理的数据， 格式为[{name:"xxx",data:[{name:"x",value:1000}]}]
 * @param {*} yNames
 * @param {*} sortIndex
 * @returns
 */
export const dataConvertForPercentTopN = (list, topN, sortIndex) => {
  var ynames = []
  if (topN > 0 && list.length > 0) {
    for (var i = 0; i < Math.min(topN, list.length); i++) {
      ynames.push(list[i].name)
    }
  }
  var responseDataB = dataConvertForPercent(list, topN == 0 ? [] : ynames, sortIndex)
  if (ynames.length > 0) {
    // 取topN后重新拉齐100%
    var temObj = {}
    for (var b in responseDataB) {
      var level1 = responseDataB[b]
      for (var m in responseDataB[b].data) {
        var level2 = responseDataB[b].data[m]
        temObj[level2.name] = temObj[level2.name] || 0
        if (level1.name != '其他') {
          temObj[level2.name] += parseFloat(numberFormat(parseFloat(level2.value || 0)))
        }
      }
    }
    // 计算各个月其他
    var ohter = JSON.parse(JSON.stringify(list[0]))
    ohter.name = '其他'
    ohter.data = []
    for (var month in temObj) {
      var value = temObj[month] == 0 ? 0 : 100 - temObj[month]
      value = numberFormat(value)
      ohter.data.push({ name: month, value: parseFloat(value) })
    }
    // 还原到其他
    for (var i in responseDataB) {
      if (responseDataB[i].name == '其他') {
        responseDataB[i] = ohter
      }
    }
  }
  return responseDataB
}

/**
 * 合并列表中‘其他’及对应项名称为空的数据
 * numberkeys 需要初始化的key，第一个为需要合并的值
 */
export const combineOther = (list, nameKey, valueKeys) => {
  var newList = []

  var otherInfo = JSON.parse(JSON.stringify(list[0]))
  otherInfo[nameKey] = '其他'
  if (typeof valueKeys === 'string') {
    valueKeys = [valueKeys]
  }
  for (var i in valueKeys) {
    otherInfo[valueKeys[i]] = 0
  }

  for (var m in list) {
    var itemInfo = list[m]
    if (
      !itemInfo[nameKey] ||
      itemInfo[nameKey].trim() == '' ||
      itemInfo[nameKey] == '其他' ||
      itemInfo[nameKey].toLocaleLowerCase() == 'null'
    ) {
      for (var i in valueKeys) {
        otherInfo[valueKeys[i]] += parseFloat(itemInfo[valueKeys[i]])
      }
    } else {
      newList.push(itemInfo)
    }
  }

  return newList
}

// 计算处理“其他”项数据
// 处理百分比对齐问题，也可以计算剩余的数量。
// initTotal默认是100，用于处理百分比问题。数量问题可以看total数据
// numberkeys 需要初始化的key，第一个为需要合并的值
export const getOtherNumber = (list, nameKey, numberKeys, initTotal) => {
  var newList = []
  var otherInfo = JSON.parse(JSON.stringify(list[0]))
  otherInfo[nameKey] = '其他'
  if (typeof numberKeys === 'string') {
    numberKeys = [numberKeys]
  }
  for (var i in numberKeys) {
    otherInfo[numberKeys[i]] = 0
  }
  initTotal = initTotal || 100
  var leftNumber = initTotal
  for (var m in list) {
    var itemInfo = list[m]
    if (
      itemInfo[nameKey] &&
      itemInfo[nameKey].trim() !== '' &&
      itemInfo[nameKey] !== '其他' &&
      itemInfo[nameKey].toLocaleLowerCase() !== 'null'
    ) {
      newList.push(itemInfo)
      leftNumber -= parseFloat(itemInfo[numberKeys[0]])
    }
  }
  otherInfo[numberKeys[0]] = leftNumber
  newList.push(otherInfo)
  return newList
}
