/**
 * 对数字进行格式化处理。
 * 
 * @param {number|string} num - 需要格式化的数字或包含数字的字符串。如果是字符串且包含 '%'，会先提取数字进行格式化，最后再加上 '%'。
 * @param {number} [precision=1] - 保留的小数位数。如果为 0，则不保留小数；如果未提供，则默认保留 1 位小数。
 * @returns {string|number} - 格式化后的结果。如果输入为 0，则直接返回 0；如果输入为空值，则返回空字符串。
 */
export const numberFormat = (num, precision) => {
  if (num == 0) {
    return num
  }
  if (!num) {
    return ''
  }
  if (precision == 0) {
    precision = 0
  } else {
    precision = precision || 1
  }
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })
  if (typeof num == 'string') {
    if (num.indexOf('%') > -1) {
      num = parseFloat(num)
      return formatter.format(num) + '%'
    }
  }
  return formatter.format(num)
}
