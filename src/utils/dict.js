import { getDicts } from '@/api/system/dict/data'

/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({});
  return (() => {
    args.forEach((d, index) => {
      res.value[d] = [];
      getDicts(d).then(resp => {
        // 有些接口字典数据返回的数组是rows，有些返回的是data，后端开发人员没有做到数据统一
        if(resp.rows){
          res.value[d] = resp.rows.map(p => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass }))
        }else{
          res.value[d] = resp.data.map(p => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass }))
        }
      })
    })
    return toRefs(res.value);
  })()
}