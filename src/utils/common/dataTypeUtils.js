/**
 * 数据类型转换工具函数
 * 统一处理 dataType 的单选/多选转换逻辑
 * 便于以后在单选和多选之间切换
 */

// 配置项：控制 dataType 是否为多选模式
// 修改这个值可以全局切换单选/多选行为
export const DATA_TYPE_MULTIPLE_MODE = false // true: 多选模式, false: 单选模式

/**
 * 将 dataType 转换为字符串格式（用于API调用）
 * 兼容单选和多选模式
 * @param {string|array} dataType - 数据类型值（可能是字符串或数组）
 * @returns {string} - 转换后的字符串
 */
export const formatDataTypeForAPI = (dataType) => {
  if (!dataType) {
    return ''
  }

  // 如果是数组，使用 join 方法
  if (Array.isArray(dataType)) {
    return dataType.length > 0 ? dataType.join(',') : ''
  }

  // 如果是单个值，直接转换为字符串
  if (typeof dataType === 'string' || typeof dataType === 'number') {
    return dataType.toString()
  }

  return ''
}

/**
 * 简化版本：直接处理参数中的 dataType
 * 这是一个便捷函数，可以直接替换原来的 params.dataType.join() 调用
 * @param {string|array} dataType - 数据类型值
 * @returns {string} - 处理后的字符串
 */
export const processDataType = (dataType) => {
  return Array.isArray(dataType) ? dataType.join(',') : (dataType || '')
}

/**
 * 标准化 dataType 值（确保格式正确）
 * @param {string|array} dataType - 数据类型值
 * @returns {string|array} - 标准化后的值
 */
export const normalizeDataType = (dataType) => {
  if (!dataType) {
    return DATA_TYPE_MULTIPLE_MODE ? [] : ''
  }
  
  if (DATA_TYPE_MULTIPLE_MODE) {
    // 多选模式：确保返回数组
    return Array.isArray(dataType) ? dataType : [dataType]
  } else {
    // 单选模式：确保返回字符串
    return Array.isArray(dataType) ? (dataType[0] || '') : dataType.toString()
  }
}

/**
 * 初始化 dataType 默认值
 * @returns {string|array} - 默认值
 */
export const getDefaultDataType = () => {
  return DATA_TYPE_MULTIPLE_MODE ? [] : ''
}

/**
 * 检查 dataType 是否有值
 * @param {string|array} dataType - 数据类型值
 * @returns {boolean} - 是否有值
 */
export const hasDataTypeValue = (dataType) => {
  if (!dataType) {
    return false
  }
  
  if (Array.isArray(dataType)) {
    return dataType.length > 0
  }
  
  return dataType.toString().trim() !== ''
}

/**
 * 处理参数对象中的 dataType（用于API调用前的参数处理）
 * @param {object} params - 参数对象
 * @returns {object} - 处理后的参数对象
 */
export const processParamsDataType = (params) => {
  if (!params || typeof params !== 'object') {
    return params
  }
  
  const processedParams = { ...params }
  
  if ('dataType' in processedParams) {
    processedParams.dataType = formatDataTypeForAPI(processedParams.dataType)
  }
  
  return processedParams
}

// 兼容性函数：保持与原有 changeDataType 函数的兼容性
export const changeDataType = formatDataTypeForAPI
