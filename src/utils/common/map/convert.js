// 省份简称->全称映射表
var provicefull2jian = require('./provicefull2jian.json')
var fs = require('fs')
var path = require('path')

var provicejian2full = {}
for (var i in provicefull2jian) {
  provicejian2full[provicefull2jian[i]] = i
}
console.log(provicejian2full)

// 将中国地图中的全称名称换成简称
var china = require('../china.json')
for (var i in china.features) {
  var fullname = china.features[i].properties.name
  china.features[i].properties.name = provicefull2jian[fullname] || fullname
}
fs.writeFileSync(path.join(__dirname, '../chinajian.json'), JSON.stringify(china))

var cityfull2jian = require('./cityFull2Jian.json')
var cityjian2full = {}
for (var i in cityfull2jian) {
  cityjian2full[cityfull2jian[i]] = i
}
fs.writeFileSync(path.join(__dirname, './cityJian2Full.json'), JSON.stringify(cityjian2full))

// 各省地图转换简称
var jsonList = fs.readdirSync(path.join(__dirname, '../province'))
for (var i in jsonList) {
  if (jsonList[i].indexOf('.json') > -1) {
    var data = fs.readFileSync(path.join(__dirname, '../province/' + jsonList[i]))
    var jsonData = JSON.parse(data)
    for (var j in jsonData.features) {
      var fullname = jsonData.features[j].properties.name
      // console.log(fullname, cityfull2jian[fullname] || '--')
      jsonData.features[j].properties.name = cityfull2jian[fullname] || fullname

      fs.writeFileSync(
        path.join(__dirname, '../provincejian/' + jsonList[i]),
        JSON.stringify(jsonData)
      )
    }
  }
}
