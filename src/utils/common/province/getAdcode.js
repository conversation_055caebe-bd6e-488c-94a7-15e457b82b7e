/**该文件用于动态生成省份编码映射及数据引入文件 */
var maps = require('../china.json')
var fs = require('fs')
var path = require('path')

var adcodes = {}
var names = []
var imports = ''
for (var i in maps.features) {
  if (maps.features[i].properties.name.indexOf('其他') < 0) {
    imports += `import ${maps.features[i].properties.name} from './${maps.features[i].properties.adcode}.json'\n`
    names.push(`  '${maps.features[i].properties.name}': ${maps.features[i].properties.name}`)
    adcodes[maps.features[i].properties.name] = maps.features[i].properties.adcode
    names[maps.features[i].properties.name] = maps.features[i].properties.name
  }
}

console.log('adcodes', adcodes)
fs.writeFileSync(path.join(__dirname, './adcodes.json'), JSON.stringify(adcodes, null, 2))
console.log(imports + '\nvar names = {\n' + names.join(',\n') + '\n}')
fs.writeFileSync(
  path.join(__dirname, './getGeoJson.js'),
  imports +
    '\nvar names = {\n' +
    names.join(',\n') +
    '\n}\n\n' +
    `export const getGeoJson = async name => {
  return names[name]
}\n`
)
