import 北京市 from './110000.json'
import 天津市 from './120000.json'
import 河北省 from './130000.json'
import 山西省 from './140000.json'
import 内蒙古自治区 from './150000.json'
import 辽宁省 from './210000.json'
import 吉林省 from './220000.json'
import 黑龙江省 from './230000.json'
import 上海市 from './310000.json'
import 江苏省 from './320000.json'
import 浙江省 from './330000.json'
import 安徽省 from './340000.json'
import 福建省 from './350000.json'
import 江西省 from './360000.json'
import 山东省 from './370000.json'
import 河南省 from './410000.json'
import 湖北省 from './420000.json'
import 湖南省 from './430000.json'
import 广东省 from './440000.json'
import 广西壮族自治区 from './450000.json'
import 重庆市 from './500000.json'
import 四川省 from './510000.json'
import 贵州省 from './520000.json'
import 云南省 from './530000.json'
import 西藏自治区 from './540000.json'
import 陕西省 from './610000.json'
import 甘肃省 from './620000.json'
import 青海省 from './630000.json'
import 宁夏回族自治区 from './640000.json'
import 新疆维吾尔自治区 from './650000.json'
import 台湾省 from './710000.json'
import 香港 from './810000.json'
import 澳门 from './820000.json'
import 海南省 from './460000.json'

var names = {
  '北京市': 北京市,
  '天津市': 天津市,
  '河北省': 河北省,
  '山西省': 山西省,
  '内蒙古自治区': 内蒙古自治区,
  '辽宁省': 辽宁省,
  '吉林省': 吉林省,
  '黑龙江省': 黑龙江省,
  '上海市': 上海市,
  '江苏省': 江苏省,
  '浙江省': 浙江省,
  '安徽省': 安徽省,
  '福建省': 福建省,
  '江西省': 江西省,
  '山东省': 山东省,
  '河南省': 河南省,
  '湖北省': 湖北省,
  '湖南省': 湖南省,
  '广东省': 广东省,
  '广西壮族自治区': 广西壮族自治区,
  '重庆市': 重庆市,
  '四川省': 四川省,
  '贵州省': 贵州省,
  '云南省': 云南省,
  '西藏自治区': 西藏自治区,
  '陕西省': 陕西省,
  '甘肃省': 甘肃省,
  '青海省': 青海省,
  '宁夏回族自治区': 宁夏回族自治区,
  '新疆维吾尔自治区': 新疆维吾尔自治区,
  '台湾省': 台湾省,
  '香港': 香港,
  '澳门': 澳门,
  '海南省': 海南省
}

export const getGeoJson = async name => {
  return names[name]
}
