import 北京 from './110000.json'
import 天津 from './120000.json'
import 河北 from './130000.json'
import 山西 from './140000.json'
import 内蒙 from './150000.json'
import 辽宁 from './210000.json'
import 吉林 from './220000.json'
import 黑龙江 from './230000.json'
import 上海 from './310000.json'
import 江苏 from './320000.json'
import 浙江 from './330000.json'
import 安徽 from './340000.json'
import 福建 from './350000.json'
import 江西 from './360000.json'
import 山东 from './370000.json'
import 河南 from './410000.json'
import 湖北 from './420000.json'
import 湖南 from './430000.json'
import 广东 from './440000.json'
import 广西 from './450000.json'
import 重庆 from './500000.json'
import 四川 from './510000.json'
import 贵州 from './520000.json'
import 云南 from './530000.json'
import 西藏 from './540000.json'
import 陕西 from './610000.json'
import 甘肃 from './620000.json'
import 青海 from './630000.json'
import 宁夏 from './640000.json'
import 新疆 from './650000.json'
import 台湾 from './710000.json'
import 香港 from './810000.json'
import 澳门 from './820000.json'
import 海南 from './460000.json'

var names = {
  '北京': 北京,
  '天津': 天津,
  '河北': 河北,
  '山西': 山西,
  '内蒙': 内蒙,
  '辽宁': 辽宁,
  '吉林': 吉林,
  '黑龙江': 黑龙江,
  '上海': 上海,
  '江苏': 江苏,
  '浙江': 浙江,
  '安徽': 安徽,
  '福建': 福建,
  '江西': 江西,
  '山东': 山东,
  '河南': 河南,
  '湖北': 湖北,
  '湖南': 湖南,
  '广东': 广东,
  '广西': 广西,
  '重庆': 重庆,
  '四川': 四川,
  '贵州': 贵州,
  '云南': 云南,
  '西藏': 西藏,
  '陕西': 陕西,
  '甘肃': 甘肃,
  '青海': 青海,
  '宁夏': 宁夏,
  '新疆': 新疆,
  '台湾': 台湾,
  '香港': 香港,
  '澳门': 澳门,
  '海南': 海南
}

export const getGeoJson = async name => {
  return names[name]
}
