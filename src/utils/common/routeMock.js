export const routerBi = [
  {
    name: 'Home',
    path: '/home',
    hidden: false,
    redirect: '/home/<USER>',
    component: 'BiLayout',
    meta: {
      title: '首页',
      icon: 'system',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'homeNews',
        path: 'news',
        hidden: false,
        component: 'home/news/index',
        meta: {
          title: '热点新闻',
          icon: 'bi-home-news',
          noCache: true,
          link: null
        }
      },
      {
        name: 'homeNewsList', // 首页一句话信息列表
        path: 'newslist',
        hidden: true,
        component: 'home/newslist/index',
        meta: {
          title: '信息快递',
          icon: '',
          noCache: true,
          link: null,
          activeMenu: '/home/<USER>'
        }
      },
      {
        name: 'homeNewsInfo', // 首页信息详情
        path: 'newsinfo',
        hidden: true,
        component: 'home/newsinfo/index',
        meta: {
          title: '信息详情',
          icon: '',
          noCache: true,
          link: null,
          activeMenu: '/home/<USER>'
        }
      },
      {
        name: 'HomeVibe',
        path: 'vibe',
        hidden: false,
        component: 'home/vibe/index',
        meta: {
          title: '宏观环境',
          icon: 'bi-home-macro',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeEngine',
        path: 'engine',
        hidden: false,
        component: 'home/engine/index',
        meta: {
          title: '内燃机排名',
          icon: 'bi-home-engine',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeInland',
        path: 'inland',
        hidden: false,
        component: 'home/inland/index',
        meta: {
          title: '国内市场',
          icon: 'bi-home-inland',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeExit',
        path: 'exit',
        hidden: false,
        component: 'home/exit/index',
        meta: {
          title: '出口分布',
          icon: 'bi-home-exit',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeEnergy',
        path: 'energy',
        hidden: false,
        component: 'home/energy/index',
        meta: {
          title: '新能源市场',
          icon: 'bi-home-energy',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeOem',
        path: 'oem',
        hidden: false,
        component: 'home/oem/index',
        meta: {
          title: 'OEM排名',
          icon: 'bi-home-oem',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeMarketing',
        path: 'marketing',
        hidden: false,
        component: 'home/marketing/index',
        meta: {
          title: '营销线索',
          icon: 'bi-home-marketing',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeScan',
        path: 'scan',
        hidden: false,
        component: 'home/scan/index',
        meta: {
          title: '异动扫描',
          icon: 'bi-home-scan',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeSubscription',
        path: 'subscription',
        hidden: false,
        component: 'home/subscription/index',
        meta: {
          title: '我的订阅',
          icon: 'bi-home-subscription',
          noCache: true,
          link: null
        }
      },
      {
        name: 'HomeMessage',
        path: 'message',
        hidden: false,
        component: 'home/message/index',
        meta: {
          title: '信息需求',
          icon: 'bi-home-message',
          noCache: true,
          link: null
        }
      }
    ]
  },
  {
    name: 'Ambience',
    path: '/ambience',
    hidden: false,
    redirect: '/ambience/macro/economy',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '竞争环境',
      icon: 'system',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'AmbienceMacro',
        path: 'macro',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '宏观环境',
          icon: 'bi-ambience-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'AmbienceMacroEconomy',
            path: 'economy',
            hidden: false,
            component: 'ambience/macro/economy/index',
            meta: {
              title: '经济环境',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AmbienceMacroPolicy',
            path: 'policy',
            hidden: false,
            component: 'ambience/macro/policy/index',
            meta: {
              title: '政策环境',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AmbienceMacroEstate',
            path: 'estate',
            hidden: false,
            component: 'ambience/macro/estate/index',
            meta: {
              title: '产业环境',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AmbienceMacroInfo',
            path: 'info',
            hidden: true,
            component: 'ambience/macro/info/index',
            meta: {
              title: '详情',
              icon: '',
              noCache: false,
              link: null,
              activeMenu: '/ambience/macro/estate'
            }
          }
        ]
      },
      {
        name: 'AmbienceMarket',
        path: 'market',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '市场环境',
          icon: 'bi-ambience-market',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'CommercialVehicle',
            path: 'vehicle',
            hidden: false,
            component: 'ambience/market/vehicle/index',
            meta: {
              title: '商用车',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'GeneralMachine',
            path: 'machine',
            hidden: false,
            component: 'ambience/market/machine/index',
            meta: {
              title: '通机',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'ShipElectricity',
            path: 'electricity',
            hidden: false,
            component: 'ambience/market/electricity/index',
            meta: {
              title: '船电',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'NewEnergy',
            path: 'energy',
            hidden: false,
            component: 'ambience/market/energy/index',
            meta: {
              title: '新能源',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'SegmentedPower',
            path: 'power',
            hidden: false,
            component: 'ambience/market/power/index',
            meta: {
              title: '细分动力',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    name: 'Rival',
    path: '/rival',
    hidden: false,
    redirect: '/rival/shangyongche/yuchai',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '竞争对手',
      icon: 'bi-home-marketing',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'SYCHome',
        path: 'shangyongche',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '商用车',
          icon: 'bi-home-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'RivalHomeYuchaiSyc',
            path: 'yuchai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '玉柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeWeichaiSyc',
            path: 'weichai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '潍柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeKMS',
            path: 'kangmingsi',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '康明斯',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeYunNei',
            path: 'yunnei',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '云内',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: 'TJHome',
        path: 'tongji',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '通机',
          icon: 'job',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'RivalHomeYuchaiTj',
            path: 'yuchai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '玉柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeWeichaiTj',
            path: 'weichai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '潍柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeWeichaiTj2',
            path: 'kangmingsi',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '康明斯',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: 'ShipHome',
        path: 'chuandian',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '船电',
          icon: 'bi-home-marketing',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'RivalHomeYuchaiTj2',
            path: 'yuchai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '玉柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeYuchaiShiphc',
            path: 'weichai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '潍柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeWeichaiShipkms',
            path: 'kangmingsi',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '康明斯',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: 'newpowerHome',
        path: 'newPower',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '新能源',
          icon: 'bi-home-energy',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'RivalHomeYuchaiTj3',
            path: 'yuchai3',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '玉柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeYuchainewpowerwc',
            path: 'yuchai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '潍柴',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeWeichainewpowerksm',
            path: 'weichai',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '康明斯',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'RivalHomeWeichainewpowermn',
            path: 'yunnei',
            hidden: false,
            component: 'rival/home/<USER>/index',
            meta: {
              title: '云内',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
    // children: [
    //   {
    //     name: "RivalHome",
    //     path: "home",
    //     hidden: false,
    //     component: "ParentView",
    //     redirect: 'noRedirect',
    //     alwaysShow: true,
    //     meta: {
    //       title: "竞争对手",
    //       icon: "bi-home-macro",
    //       noCache: true,
    //       link: null,
    //     },

    // children: [
    //   {
    //     name: "RivalHomeYuchai",
    //     path: "yuchai",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "玉柴",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeWeichai",
    //     path: "weichai",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "潍柴",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeKMS",
    //     path: "kangmingsi",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "康明斯",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeYunNei",
    //     path: "yunnei",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "云内",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeQuanchai",
    //     path: "quanchai",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "全柴",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeXinchai",
    //     path: "xinchai",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "新柴",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeJiubaotian",
    //     path: "jiubaotian",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "久保田",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeYangma",
    //     path: "yangma",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "洋马",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   },
    //   {
    //     name: "RivalHomeBojinsi",
    //     path: "bojinsi",
    //     hidden: false,
    //     component: "rival/home/<USER>/index",
    //     meta: {
    //       title: "铂金斯",
    //       icon: "",
    //       noCache: false,
    //       link: null,
    //     },
    //   }
    // ],
    // 	}
    // ],
  },
  {
    name: 'Machine',
    path: '/machine',
    hidden: false,
    redirect: '/machine/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '主机厂客户',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'MachineIntel',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '客户情报',
          icon: 'bi-home-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'MachineIntelCar',
            path: 'car',
            hidden: false,
            component: 'machine/intel/car/index',
            meta: {
              title: '商用车',
              icon: '',
              noCache: false,
              link: null,
              activeMenu: ''
            }
          },

          // {
          //   name: "MachineIntelCar9",
          //   path: "morenews",
          //   hidden: false,
          //   component: "machine/intel/car/morenews",
          //   meta: {
          //     title: "详情",
          //     icon: "",
          //     noCache: false,
          //     link: null,
          //     activeMenu: 'machine/intel/car/morenews'
          //   },
          // },

          {
            name: 'MachineIntelMechanical',
            path: 'mechanical',
            hidden: false,
            component: 'machine/intel/mechanical/index',
            meta: {
              title: '工程机械',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'MachineIntelAgro',
            path: 'agro',
            hidden: false,
            component: 'machine/intel/agro/index',
            meta: {
              title: '农业装备',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'MachineIntelShip',
            path: 'ship',
            hidden: false,
            component: 'machine/intel/ship/index',
            meta: {
              title: '船舶',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'MachineIntelPower',
            path: 'power',
            hidden: false,
            component: 'machine/intel/power/index',
            meta: {
              title: '发电动力',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'MachineIntelEnrgy',
            path: 'energy',
            hidden: false,
            component: 'machine/intel/energy/index',
            meta: {
              title: '新能源',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },

  {
    name: 'Enduser', // 开发订正自己的路由
    path: '/enduser',
    hidden: false,
    redirect: '/enduser/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '终端客户',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'EnduserIntel',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '客户情报',
          icon: 'bi-home-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'EnduserIntelCar',
            path: 'car',
            hidden: false,
            component: 'enduser/intel/car/index',
            meta: {
              title: '商用车',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'EnduserIntelMechanical',
            path: 'mechanical',
            hidden: false,
            component: 'enduser/intel/mechanical/index',
            meta: {
              title: '工程机械',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'EnduserIntelAgro',
            path: 'agro',
            hidden: false,
            component: 'enduser/intel/agro/index',
            meta: {
              title: '农业装备',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'EnduserIntelShip',
            path: 'ship',
            hidden: false,
            component: 'enduser/intel/ship/index',
            meta: {
              title: '船舶',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'EnduserIntelPower',
            path: 'power',
            hidden: false,
            component: 'enduser/intel/power/index',
            meta: {
              title: '发电动力',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'EnduserIntelEnergy',
            path: 'energy',
            hidden: false,
            component: 'enduser/intel/energy/index',
            meta: {
              title: '新能源',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },

  {
    name: 'Abroad', // 开发订正自己的路由
    path: '/abroad',
    hidden: false,
    redirect: '/abroad/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '海外客户',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'AbroadIntel2',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '客户情报',
          icon: 'bi-home-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'AbroadIntelCar2',
            path: 'car',
            hidden: false,
            component: 'abroad/intel/car/index',
            meta: {
              title: '商用车',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AbroadIntelmechanical',
            path: 'mechanical',
            hidden: false,
            component: 'abroad/intel/mechanical/index',
            meta: {
              title: '工程机械',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AbroadIntelAgro2',
            path: 'agro',
            hidden: false,
            component: 'abroad/intel/agro/index',
            meta: {
              title: '农业装备',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AbroadIntelShip',
            path: 'ship',
            hidden: false,
            component: 'abroad/intel/ship/index',
            meta: {
              title: '船舶',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AbroadIntelPower',
            path: 'power',
            hidden: false,
            component: 'abroad/intel/power/index',
            meta: {
              title: '发电动力',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'AbroadIntelEnergy',
            path: 'energy',
            hidden: false,
            component: 'abroad/intel/energy/index',
            meta: {
              title: '新能源',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    name: 'Machine3', // 开发订正自己的路由
    path: '/machine',
    hidden: false,
    redirect: '/machine/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '技术情报',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'MachineIntel3',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '新能源',
          icon: '',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'MachineIntelCar3',
            path: 'car',
            hidden: false,
            component: 'machine/intel/car/index',
            meta: {
              title: '商用车',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    name: 'Machine4', // 开发订正自己的路由
    path: '/machine',
    hidden: false,
    redirect: '/machine/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '展会情报',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'MachineIntel4',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '展会情报',
          icon: '',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'MachineIntelCar4',
            path: 'car',
            hidden: false,
            component: 'machine/intel/car/index',
            meta: {
              title: '商务年会',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    name: 'Machine5', // 开发订正自己的路由
    path: '/machine',
    hidden: false,
    redirect: '/machine/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '情报应用',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'MachineIntel5',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '报告生成',
          icon: '',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'MachineIntelCar5',
            path: 'car',
            hidden: false,
            component: 'machine/intel/car/index',
            meta: {
              title: '情报季报',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    name: 'Batabase', // 开发订正自己的路由
    path: '/database',
    hidden: false,
    redirect: '/database/cooperate/business',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '数据管理',
      icon: '',
      noCache: true,
      link: null
    },

    children: [
      {
        name: 'BatabaseCooperate',
        path: 'cooperate',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '数据库管理',
          icon: 'bi-ambience-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'BatabaseCooperateMacro',
            path: 'macro',
            hidden: false,
            component: 'database/cooperate/macro/index',
            meta: {
              title: '宏观环境',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'BatabaseCooperateIndustry',
            path: 'industry',
            hidden: false,
            component: 'database/cooperate/industry/index',
            meta: {
              title: '行业数据',
              icon: '',
              noCache: false,
              link: null
            }
          },
          {
            name: 'BatabaseCooperateBusiness',
            path: 'business',
            hidden: false,
            component: 'database/cooperate/business/index',
            meta: {
              title: '竞争对手',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: 'BatabaseLabel',
        path: 'label',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '后台管理',
          icon: 'bi-ambience-macro',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'BatabaseLabel',
            path: 'label',
            hidden: false,
            component: 'database/label/index',
            meta: {
              title: '标签管理',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      },
      
      
      {
        name: 'BatabaseuploadTp',
        path: 'uploadTp',
        hidden: false,
        component: 'database/uploadTp/index',
        meta: {
          title: '图片上传',
          icon: 'bi-ambience-macro',
          noCache: true,
          link: null
        }
      },
      {
        name: 'Batabaseuploadbg',
        path: 'uploadbg',
        hidden: false,
        component: 'database/uploadbg/index',
        meta: {
          title: '报告上传',
          icon: 'bi-ambience-macro',
          noCache: true,
          link: null
        }
      }
    ]
  },
  {
    name: 'System', // 开发订正自己的路由
    path: '/system',
    hidden: false,
    redirect: '/system/user',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '后台管理',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'SystemHome',
        path: '/system',
        hidden: false,
        redirect: 'noRedirect',
        component: 'ParentView',
        alwaysShow: true,
        meta: {
          title: '后台管理',
          icon: 'system',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'systemUser',
            path: 'user',
            hidden: false,
            component: 'system/user/index',
            meta: {
              title: '用户管理',
              icon: 'user',
              noCache: false,
              link: null
            }
          },
          {
            name: 'authUser',
            path: 'user-auth/role/:userId',
            hidden: true,
            component: 'system/user/authRole',
            meta: {
              title: '角色分配',
              icon: '',
              noCache: true,
              link: null,
              activeMenu: '/system/user'
            }
          },
          {
            name: 'systemRole',
            path: 'role',
            hidden: false,
            component: 'system/role/index',
            meta: {
              title: '角色管理',
              icon: 'peoples',
              noCache: false,
              link: null
            }
          },
          {
            name: 'authUserRole',
            path: 'role-auth/user/:roleId',
            hidden: true,
            component: 'system/role/authUser',
            meta: {
              title: '分配用户',
              icon: '',
              noCache: true,
              link: null,
              activeMenu: '/system/role'
            }
          },
          {
            name: 'systemMenu',
            path: 'menu',
            hidden: false,
            component: 'system/menu/index',
            meta: {
              title: '菜单管理',
              icon: 'tree-table',
              noCache: false,
              link: null
            }
          },
          {
            name: 'systemDept',
            path: 'dept',
            hidden: false,
            component: 'system/dept/index',
            meta: {
              title: '部门管理',
              icon: 'tree',
              noCache: false,
              link: null
            }
          },
          {
            name: 'systemPost',
            path: 'post',
            hidden: false,
            component: 'system/post/index',
            meta: {
              title: '岗位管理',
              icon: 'post',
              noCache: false,
              link: null
            }
          },
          {
            name: 'systemDict',
            path: 'dict',
            hidden: false,
            component: 'system/dict/index',
            meta: {
              title: '字典管理',
              icon: 'dict',
              noCache: false,
              link: null
            }
          },
          {
            name: 'dictData',
            path: 'dict-data/index/:dictId',
            hidden: true,
            component: 'system/dict/data',
            meta: {
              title: '字典数据',
              icon: '',
              noCache: true,
              link: null,
              activeMenu: '/system/dict'
            }
          },
          {
            name: 'systemConfig',
            path: 'config',
            hidden: false,
            component: 'system/config/index',
            meta: {
              title: '参数设置',
              icon: 'edit',
              noCache: false,
              link: null
            }
          },
          {
            name: 'systemNotice',
            path: 'notice',
            hidden: false,
            component: 'system/notice/index',
            meta: {
              title: '通知公告',
              icon: 'message',
              noCache: false,
              link: null
            }
          },
          {
            name: 'systemLog',
            path: 'log',
            hidden: false,
            redirect: 'noRedirect',
            component: 'ParentView',
            alwaysShow: true,
            meta: {
              title: '日志管理',
              icon: 'log',
              noCache: false,
              link: null
            },
            children: [
              {
                name: 'systemOperlog',
                path: 'operlog',
                hidden: false,
                component: 'system/operlog/index',
                meta: {
                  title: '操作日志',
                  icon: 'form',
                  noCache: false,
                  link: null
                }
              },
              {
                name: 'systemLogininfor',
                path: 'logininfor',
                hidden: false,
                component: 'system/logininfor/index',
                meta: {
                  title: '登录日志',
                  icon: 'logininfor',
                  noCache: false,
                  link: null
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    name: 'Machine7', // 开发订正自己的路由
    path: '/machine',
    hidden: false,
    redirect: '/machine/intel/car',
    component: 'BiLayout',
    alwaysShow: true,
    meta: {
      title: '我的订阅',
      icon: '',
      noCache: true,
      link: null
    },
    children: [
      {
        name: 'MachineIntel7',
        path: 'intel',
        hidden: false,
        component: 'ParentView',
        redirect: 'noRedirect',
        alwaysShow: true,
        meta: {
          title: '我的订阅',
          icon: '',
          noCache: true,
          link: null
        },
        children: [
          {
            name: 'MachineIntelCar7',
            path: 'car',
            hidden: false,
            component: 'machine/intel/car/index',
            meta: {
              title: '我的订阅',
              icon: '',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  }
]

export const routerLayout = [
  {
    name: 'System',
    path: '/system',
    hidden: false,
    redirect: 'noRedirect',
    component: 'Layout',
    alwaysShow: true,
    meta: {
      title: '系统管理',
      icon: 'system',
      noCache: false,
      link: null
    },
    children: [
      {
        name: 'User',
        path: 'user',
        hidden: false,
        component: 'system/user/index',
        meta: {
          title: '用户管理',
          icon: 'user',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Role',
        path: 'role',
        hidden: false,
        component: 'system/role/index',
        meta: {
          title: '角色管理',
          icon: 'peoples',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Menu',
        path: 'menu',
        hidden: false,
        component: 'system/menu/index',
        meta: {
          title: '菜单管理',
          icon: 'tree-table',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Dept',
        path: 'dept',
        hidden: false,
        component: 'system/dept/index',
        meta: {
          title: '部门管理',
          icon: 'tree',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Post',
        path: 'post',
        hidden: false,
        component: 'system/post/index',
        meta: {
          title: '岗位管理',
          icon: 'post',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Dict',
        path: 'dict',
        hidden: false,
        component: 'system/dict/index',
        meta: {
          title: '字典管理',
          icon: 'dict',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Config',
        path: 'config',
        hidden: false,
        component: 'system/config/index',
        meta: {
          title: '参数设置',
          icon: 'edit',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Notice',
        path: 'notice',
        hidden: false,
        component: 'system/notice/index',
        meta: {
          title: '通知公告',
          icon: 'message',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Log',
        path: 'log',
        hidden: false,
        redirect: 'noRedirect',
        component: 'ParentView',
        alwaysShow: true,
        meta: {
          title: '日志管理',
          icon: 'log',
          noCache: false,
          link: null
        },
        children: [
          {
            name: 'Operlog',
            path: 'operlog',
            hidden: false,
            component: 'system/operlog/index',
            meta: {
              title: '操作日志',
              icon: 'form',
              noCache: false,
              link: null
            }
          },
          {
            name: 'Logininfor',
            path: 'logininfor',
            hidden: false,
            component: 'system/logininfor/index',
            meta: {
              title: '登录日志',
              icon: 'logininfor',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    path: '/',
    hidden: false,
    component: 'Layout',
    children: [
      {
        name: 'Testgen',
        path: 'testgen',
        hidden: false,
        component: 'index/testgen/index',
        meta: {
          title: '测试代码生成',
          icon: 'job',
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: 'Monitor',
    path: '/monitor',
    hidden: false,
    redirect: 'noRedirect',
    component: 'Layout',
    alwaysShow: true,
    meta: {
      title: '系统监控',
      icon: 'monitor',
      noCache: false,
      link: null
    },
    children: [
      {
        name: 'Online',
        path: 'online',
        hidden: false,
        component: 'monitor/online/index',
        meta: {
          title: '在线用户',
          icon: 'online',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Job',
        path: 'job',
        hidden: false,
        component: 'monitor/job/index',
        meta: {
          title: '定时任务',
          icon: 'job',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Http://localhost:8718',
        path: 'http://localhost:8718',
        hidden: false,
        component: 'Layout',
        meta: {
          title: 'Sentinel控制台',
          icon: 'sentinel',
          noCache: false,
          link: 'http://localhost:8718'
        }
      },
      {
        name: 'Http://localhost:8848/nacos',
        path: 'http://localhost:8848/nacos',
        hidden: false,
        component: 'Layout',
        meta: {
          title: 'Nacos控制台',
          icon: 'nacos',
          noCache: false,
          link: 'http://localhost:8848/nacos'
        }
      },
      {
        name: 'Http://localhost:9100/login',
        path: 'http://localhost:9100/login',
        hidden: false,
        component: 'Layout',
        meta: {
          title: 'Admin控制台',
          icon: 'server',
          noCache: false,
          link: 'http://localhost:9100/login'
        }
      }
    ]
  },
  {
    name: 'Tool',
    path: '/tool',
    hidden: false,
    redirect: 'noRedirect',
    component: 'Layout',
    alwaysShow: true,
    meta: {
      title: '系统工具',
      icon: 'tool',
      noCache: false,
      link: null
    },
    children: [
      {
        name: 'Build',
        path: 'build',
        hidden: false,
        component: 'tool/build/index',
        meta: {
          title: '表单构建',
          icon: 'build',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Gen',
        path: 'gen',
        hidden: false,
        component: 'tool/gen/index',
        meta: {
          title: '代码生成',
          icon: 'code',
          noCache: false,
          link: null
        }
      },
      {
        name: 'Http://localhost:8080/swagger-ui/index.html',
        path: 'http://localhost:8080/swagger-ui/index.html',
        hidden: false,
        component: 'Layout',
        meta: {
          title: '系统接口',
          icon: 'swagger',
          noCache: false,
          link: 'http://localhost:8080/swagger-ui/index.html'
        }
      }
    ]
  }
]
