import JSEncrypt from 'jsencrypt'

const app =
    "-----BEGIN PUBLIC KEY-----\n" +
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtvRCw7jYs/Ee+mkYz78fqNN9A2AaukFY\n" +
    "0UuHI4Whwm97GTBHYiqjG09XzXON5jG8oacUFPETSasU35/Nq+TOZ52I/7EA33HDUuJGn0EPIA1z\n" +
    "E2yalnqOfHS9stg/hXpAZm/LuVgq7wInNPnah/vXwdnzX+V5XHSDFFVgVvwyYAjgf5LMttTd8TAv\n" +
    "yg0aFspt+uEMskXRvDXpep45tYbpXg/JxDqioKWvFylGzlJAQwC58di9FoIIOg+ytDjPE+r9HXQq\n" +
    "EH9RvqjNQMnMPWelAK3N8+3YyALvh7NUVR1jczA3A060XWgO49ycz4goL5UQeq0aiInpPct2Gips\n" +
    "Fh5huQIDAQAB\n" +
    "-----END PUBLIC KEY-----"



export function genFrUrl(url, username) {
    // url = 'https://frrc.app.yuchai.com/frbi/decision'
    let encrypt = new JSEncrypt();

    encrypt.setPublicKey(app);

    let encrypted = encrypt.encrypt(username);
    return url + '&ssoToken=' + encodeURIComponent(encrypted)
}