// 数据字典
export const typePointerType = {
  '0': '月度',
  '1': '季度',
  '2': '月累'
}
/**
 * 指标类型
 * 使用到的页面：首页-新能源市场/竞争对手
 */
export const dictsPointerType = [
  {
    value: '2',
    label: '月累'
  },
  {
    value: '0',
    label: '月度'
  },
  {
    value: '1',
    label: '季度'
  }
]

/**
 * 月度选择
 * 使用到的页面：首页-新能源市场/oem
 */
export const dictsMonth = [
  {
    value: '1',
    label: '1月'
  },
  {
    value: '2',
    label: '2月'
  },
  {
    value: '3',
    label: '3月'
  },
  {
    value: '4',
    label: '4月'
  },
  {
    value: '5',
    label: '5月'
  },
  {
    value: '6',
    label: '6月'
  },
  {
    value: '7',
    label: '7月'
  },
  {
    value: '8',
    label: '8月'
  },
  {
    value: '9',
    label: '9月'
  },
  {
    value: '10',
    label: '10月'
  },
  {
    value: '11',
    label: '11月'
  },
  {
    value: '12',
    label: '12月'
  }
]

/**
 * 月累选择
 * 使用到的页面：首页-新能源市场/oem
 */
export const dictsMonthTotal = [
  {
    value: '2',
    label: '1-2月'
  },
  {
    value: '3',
    label: '1-3月'
  },
  {
    value: '4',
    label: '1-4月'
  },
  {
    value: '5',
    label: '1-5月'
  },
  {
    value: '6',
    label: '1-6月'
  },
  {
    value: '7',
    label: '1-7月'
  },
  {
    value: '8',
    label: '1-8月'
  },
  {
    value: '9',
    label: '1-9月'
  },
  {
    value: '10',
    label: '1-10月'
  },
  {
    value: '11',
    label: '1-11月'
  },
  {
    value: '12',
    label: '1-12月'
  }
]
/**
 * 季度选择
 * 使用到的页面：首页-新能源市场/oem
 */
export const dictsQuarter = [
  {
    value: '1',
    label: '第一季度'
  },
  {
    value: '2',
    label: '第二季度'
  },
  {
    value: '3',
    label: '第三季度'
  },
  {
    value: '4',
    label: '第四季度'
  }
]

/**
 * 燃料(非新能源)
 * 使用到的页面： 首页-新能源市场/竞争对手/oem
 */
export const dictsFuelTypeA = [
  {
    value: '柴油',
    label: '柴油'
  },
  {
    value: '气体',
    label: '气体'
  },
  {
    value: '其他',
    label: '其他'
  }
]

/**
 * 燃料（新能源）
 * 使用到的页面： 首页-新能源市场/PPT23页 竞争环境-新能源
 */
export const dictsFuelType = [
  {
    value: '燃料电池',
    label: '燃料电池'
  },
  {
    value: '纯电动',
    label: '纯电动'
  },
  {
    value: '混合动力',
    label: '混合动力'
  }
]

export const dictsFuelType2 = [
  {
    value: '柴油',
    label: '柴油'
  },
  {
    value: '气体',
    label: '气体'
  },
  {
    value: '其他',
    label: '其他'
  }
]

/**
 * 主机厂
 * 使用到的页面： 首页-新能源市场/竞争对手
 */
export const dictsManuFacturer = [
  {
    label: '北汽福田',
    value: '北汽福田'
  },
  {
    label: '江淮轻商',
    value: '江淮轻商'
  },
  {
    label: '重汽集团',
    value: '重汽集团'
  },
  {
    label: '江铃汽车',
    value: '江铃汽车'
  },
  {
    label: '一汽解放（青岛）',
    value: '一汽解放（青岛）'
  },
  {
    label: '东风股份',
    value: '东风股份'
  },
  {
    label: '四川江淮',
    value: '四川江淮'
  },
  {
    label: '庆铃汽车',
    value: '庆铃汽车'
  },
  {
    label: '上汽大通',
    value: '上汽大通'
  },
  {
    label: '山东五征',
    value: '山东五征'
  },
  {
    label: '重汽海西',
    value: '重汽海西'
  },
  {
    label: '陕汽商用车',
    value: '陕汽商用车'
  },
  {
    label: '南京汽车',
    value: '南京汽车'
  },
  {
    label: '山东汽车',
    value: '山东汽车'
  },
  {
    label: '江西五十铃',
    value: '江西五十铃'
  },
  {
    label: '重汽王牌',
    value: '重汽王牌'
  },
  {
    label: '山东凯马',
    value: '山东凯马'
  },
  {
    label: '潍柴新能源',
    value: '潍柴新能源'
  },
  {
    label: '成都大运',
    value: '成都大运'
  },
  {
    label: '四川南骏',
    value: '四川南骏'
  },
  {
    label: '湖北三环',
    value: '湖北三环'
  },
  {
    label: '吉利江西',
    value: '吉利江西'
  },
  {
    label: '湖北大运',
    value: '湖北大运'
  },
  {
    label: '福田多功能',
    value: '福田多功能'
  },
  {
    label: '一汽红塔',
    value: '一汽红塔'
  },
  {
    label: '长安跨越',
    value: '长安跨越'
  },
  {
    label: '唐骏欧铃',
    value: '唐骏欧铃'
  },
  {
    label: '徐工汽车',
    value: '徐工汽车'
  },
  {
    label: '山东时风',
    value: '山东时风'
  },
  {
    label: '东风柳汽',
    value: '东风柳汽'
  },
  {
    label: '北汽青岛',
    value: '北汽青岛'
  },
  {
    label: '东风华神',
    value: '东风华神'
  },
  {
    label: '重庆长安',
    value: '重庆长安'
  },
  {
    label: '北汽黄骅',
    value: '北汽黄骅'
  },
  {
    label: '北京欧辉',
    value: '北京欧辉'
  },
  {
    label: '现代商用',
    value: '现代商用'
  },
  {
    label: '北汽有限',
    value: '北汽有限'
  },
  {
    label: '吉利四川',
    value: '吉利四川'
  },
  {
    label: '东风云汽',
    value: '东风云汽'
  },
  {
    label: '一汽本部',
    value: '一汽本部'
  },
  {
    label: '长城汽车',
    value: '长城汽车'
  },
  {
    label: '东风商用',
    value: '东风商用'
  },
  {
    label: '陕西重汽',
    value: '陕西重汽'
  },
  {
    label: '福田戴姆勒',
    value: '福田戴姆勒'
  },
  {
    label: '郑州日产',
    value: '郑州日产'
  },
  {
    label: '东风新疆',
    value: '东风新疆'
  },
  {
    label: '江淮重卡',
    value: '江淮重卡'
  },
  {
    label: '上汽红岩',
    value: '上汽红岩'
  },
  {
    label: '河北长安',
    value: '河北长安'
  },
  {
    label: '上汽通用五菱',
    value: '上汽通用五菱'
  },
  {
    label: '山西大运',
    value: '山西大运'
  },
  {
    label: '三一专汽',
    value: '三一专汽'
  },
  {
    label: '东风随专',
    value: '东风随专'
  },
  {
    label: '中联重科',
    value: '中联重科'
  },
  {
    label: '北汽重型',
    value: '北汽重型'
  },
  {
    label: '三一重起',
    value: '三一重起'
  },
  {
    label: '山西新能源',
    value: '山西新能源'
  },
  {
    label: '华晨鑫源',
    value: '华晨鑫源'
  },
  {
    label: '河北中兴',
    value: '河北中兴'
  },
  {
    label: '包头北奔',
    value: '包头北奔'
  },
  {
    label: '郑州宇通',
    value: '郑州宇通'
  },
  {
    label: '联合卡车',
    value: '联合卡车'
  },
  {
    label: '安徽华菱',
    value: '安徽华菱'
  },
  {
    label: '三一重卡',
    value: '三一重卡'
  },
  {
    label: '襄阳旅行车',
    value: '襄阳旅行车'
  }
]

/**
 * 发动机厂家
 * 使用到的页面： 首页-新能源市场/竞争对手
 */
export const dictsEngineFactory = [
  {
    label: '云内',
    value: '云内'
  },
  {
    label: '全柴',
    value: '全柴'
  },
  {
    label: '潍柴',
    value: '潍柴'
  },
  {
    label: '江铃',
    value: '江铃'
  },
  {
    label: '康明斯',
    value: '康明斯'
  },
  {
    label: '江淮汽车',
    value: '江淮汽车'
  },
  {
    label: '锡柴',
    value: '锡柴'
  },
  {
    label: '福田股份',
    value: '福田股份'
  },
  {
    label: '五十铃（中国）',
    value: '五十铃（中国）'
  },
  {
    label: '江西五十铃',
    value: '江西五十铃'
  },
  {
    label: '东风轻发',
    value: '东风轻发'
  },
  {
    label: '上柴',
    value: '上柴'
  },
  {
    label: '南京汽车',
    value: '南京汽车'
  },
  {
    label: '玉柴',
    value: '玉柴'
  },
  {
    label: '东安动力',
    value: '东安动力'
  },
  {
    label: '重庆小康',
    value: '重庆小康'
  },
  {
    label: '庆铃',
    value: '庆铃'
  },
  {
    label: '吉利四川',
    value: '吉利四川'
  },
  {
    label: '玉动',
    value: '玉动'
  },
  {
    label: '绵阳瑞擎',
    value: '绵阳瑞擎'
  },
  {
    label: '柳州五菱',
    value: '柳州五菱'
  },
  {
    label: '保定长城',
    value: '保定长城'
  },
  {
    label: '沈阳三菱',
    value: '沈阳三菱'
  },
  {
    label: '贵州航天圆通',
    value: '贵州航天圆通'
  },
  {
    label: '凯瑞动力',
    value: '凯瑞动力'
  },
  {
    label: '绵阳新晨',
    value: '绵阳新晨'
  },
  {
    label: '重汽',
    value: '重汽'
  },
  {
    label: '东风商用',
    value: '东风商用'
  },
  {
    label: '德国奔驰',
    value: '德国奔驰'
  },
  {
    label: '华菱汽车',
    value: '华菱汽车'
  },
  {
    label: '上菲红',
    value: '上菲红'
  },
  {
    label: '三一动力',
    value: '三一动力'
  },
  {
    label: '上海日野',
    value: '上海日野'
  },
  {
    label: '日本日野',
    value: '日本日野'
  },
  {
    label: '朝柴',
    value: '朝柴'
  },
  {
    label: '五十铃',
    value: '五十铃'
  },
  {
    label: '洛柴',
    value: '洛柴'
  },
  {
    label: '新柴',
    value: '新柴'
  },
  {
    label: '其他',
    value: '其他'
  },
  {
    label: '久保田',
    value: '久保田'
  },
  {
    label: '雷沃动力',
    value: '雷沃动力'
  },
  {
    label: '解放动力',
    value: '解放动力'
  },
  {
    label: '常柴',
    value: '常柴'
  },
  {
    label: '中国重汽',
    value: '中国重汽'
  },
  {
    label: '洋马',
    value: '洋马'
  },
  {
    label: '四达',
    value: '四达'
  },
  {
    label: '三菱',
    value: '三菱'
  },
  {
    label: '莱动',
    value: '莱动'
  },
  {
    label: '扬动',
    value: '扬动'
  },
  {
    label: '汉马',
    value: '汉马'
  },
  {
    label: '常发',
    value: '常发'
  },
  {
    label: '三一道依茨',
    value: '三一道依茨'
  },
  {
    label: '华丰',
    value: '华丰'
  },
  {
    label: '东康',
    value: '东康'
  },
  {
    label: '重康',
    value: '重康'
  },
  {
    label: 'MTU',
    value: 'MTU'
  }
]

/**
 * 四级联动选择（数据来源 -> 板块 -> 细分市场一 -> 细分市场二 ）
 * 使用到的页面： 1.首页-新能源市场/竞争对手 2.数据区页面筛选条件
 */
export const dictsResource = [
  {
    label: '上险数',
    value: '1',
    children: [
      {
        label: '商用车',
        value: '商用车',
        children: [
          {
            label: '客车',
            value: '客车',
            children: [
              {
                label: '轻卡',
                value: '轻卡'
              },
              {
                label: '皮卡',
                value: '皮卡'
              },
              {
                label: '公路',
                value: '公路'
              },
              {
                label: '公交',
                value: '公交'
              },
              {
                label: '校车',
                value: '校车'
              }
            ]
          },
          {
            label: '卡车',
            value: '卡车',
            children: [
              {
                label: '牵引车',
                value: '牵引车'
              },
              {
                label: '中重载货',
                value: '中重载货'
              },
              {
                label: '中重自卸',
                value: '中重自卸'
              },
              {
                label: '中重专用',
                value: '中重专用'
              },
              {
                label: '轻卡',
                value: '轻卡'
              },
              {
                label: '皮卡',
                value: '皮卡'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    label: '装机数',
    value: '2',
    children: [
      {
        label: '通机',
        value: '通机',
        children: [
          {
            label: '工程机械',
            value: '工程机械',
            children: [
              {
                label: '高空平台',
                value: '高空平台'
              },
              {
                label: '矿用车',
                value: '矿用车'
              },
              {
                label: '挖掘机',
                value: '挖掘机'
              },
              {
                label: '装载机',
                value: '装载机'
              }
            ]
          },
          {
            label: '工业动力',
            value: '工业动力',
            children: [
              // {
              //   label: '叉车',
              //   value: '叉车'
              // },
              {
                label: '空压机',
                value: '空压机'
              },
              {
                label: '内燃叉车',
                value: '内燃叉车'
              },
              {
                label: '钻机',
                value: '钻机'
              }
            ]
          },
          {
            label: '农业机械',
            value: '农业机械',
            children: [
              {
                label: '花生机',
                value: '花生机'
              },
              {
                label: '水稻机',
                value: '水稻机'
              },
              {
                label: '拖拉机',
                value: '拖拉机'
              },
              {
                label: '小麦机',
                value: '小麦机'
              },
              {
                label: '玉米机',
                value: '玉米机'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    label: '中内协',
    value: '4',
    children: [
      {
        label: '商用车',
        value: '商用车',
        children: [
          {
            label: '商用车',
            value: '商用车'
          }
        ]
      },
      {
        label: '通机',
        value: '通机',
        children: [
          {
            label: '工程机',
            value: '工程机'
          },
          {
            label: '农机',
            value: '农机'
          },
          {
            label: '通机（其他）',
            value: '通机（其他）'
          }
        ]
      },
      {
        label: '船电',
        value: '船电',
        children: [
          {
            label: '船机',
            value: '船机'
          },
          {
            label: '发电',
            value: '发电'
          }
        ]
      }
    ]
  },
  {
    label: '海关数',
    value: '5',
    children: [
      {
        label: '商用车',
        value: '商用车',
        children: [
          {
            label: '卡车',
            value: '卡车',
            children: [
              {
                label: '轻卡',
                value: '轻卡'
              },
              {
                label: '中卡',
                value: '中卡'
              },
              {
                label: '重卡',
                value: '重卡'
              }
            ]
          },
          {
            label: '客车',
            value: '客车',
            children: [
              {
                label: '大客',
                value: '大客'
              },
              {
                label: '轻客',
                value: '轻客'
              },
              {
                label: '中客',
                value: '中客'
              }
            ]
          }
        ]
      },
      {
        label: '通机',
        value: '通机',
        children: [
          {
            label: '工程机械',
            value: '工程机械',
            children: [
              {
                label: '挖掘机',
                value: '挖掘机'
              },
              {
                label: '装载机',
                value: '装载机'
              },
              {
                label: '筑路机及平地机',
                value: '筑路机及平地机'
              }
            ]
          },
          {
            label: '工业动力',
            value: '工业动力',
            children: [
              {
                label: '叉车',
                value: '叉车'
              }
            ]
          },
          {
            label: '农业机械',
            value: '农业机械',
            children: [
              {
                label: '收割机',
                value: '收割机'
              },
              {
                label: '拖拉机',
                value: '拖拉机'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    label: '货运新增',
    value: '6',
    children: [
      {
        label: '商用车',
        value: '商用车',
        children: [
          {
            label: '卡车',
            value: '卡车',
            children: [
              {
                label: '牵引车',
                value: '牵引车'
              },
              {
                label: '中重载货',
                value: '中重载货'
              },
              {
                label: '中重自卸',
                value: '中重自卸'
              },
              {
                label: '中重专用',
                value: '中重专用'
              },
              {
                label: '轻卡',
                value: '轻卡'
              },
              {
                label: '皮卡',
                value: '皮卡'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    label: '船电数',
    value: '7',
    children: [
      {
        label: '船电',
        value: '船电',
        children: [
          {
            label: '船机',
            value: '船机'
          },
          {
            label: '单机',
            value: '单机'
          }
        ]
      }
    ]
  },
  {
    label: '友商数', // 渠道数
    value: '9',
    children: [
      {
        label: '商用车',
        value: '商用车',
        children: [
          {
            label: '卡车',
            value: '卡车',
            children: [
              {
                label: '3.7≤L＜7.8',
                value: '3.7≤L＜7.8'
              },
              {
                label: '7.8≤L＜15.5L',
                value: '7.8≤L＜15.5L'
              },
              {
                label: 'L＜3.7',
                value: 'L＜3.7'
              },
              {
                label: 'L≥15.5',
                value: 'L≥15.5'
              }
            ]
          },
          {
            label: '客车',
            value: '客车',
            children: [
              {
                label: '3.7≤L＜7.8',
                value: '3.7≤L＜7.8'
              },
              {
                label: '7.8≤L＜15.5L',
                value: '7.8≤L＜15.5L'
              },
              {
                label: 'L＜3.7',
                value: 'L＜3.7'
              }
            ]
          },
          {
            label: '通机',
            value: '通机',
            children: [
              {
                label: '工程机',
                value: '工程机',
                children: [
                  {
                    label: '3.7≤L＜7.8',
                    value: '3.7≤L＜7.8'
                  }
                ]
              },
              {
                label: '农机',
                value: '农机',
                children: [
                  {
                    label: '7.8≤L＜15.5L',
                    value: '7.8≤L＜15.5L'
                  },
                  {
                    label: 'L＜3.7',
                    value: 'L＜3.7'
                  },
                  {
                    label: 'L≥15.5',
                    value: 'L≥15.5'
                  }
                ]
              },
              {
                label: '船电',
                value: '船电',
                children: [
                  {
                    label: '船机',
                    value: '船机',
                    children: [
                      {
                        label: '3.7≤L＜7.8',
                        value: '3.7≤L＜7.8'
                      },
                      {
                        label: '7.8≤L＜15.5L',
                        value: '7.8≤L＜15.5L'
                      },
                      {
                        label: 'L＜3.7',
                        value: 'L＜3.7'
                      },
                      {
                        label: 'L≥15.5',
                        value: 'L≥15.5'
                      }
                    ]
                  },
                  {
                    label: '发电',
                    value: '发电',
                    children: [
                      {
                        label: '3.7≤L＜7.8',
                        value: '3.7≤L＜7.8'
                      },
                      {
                        label: '7.8≤L＜15.5L',
                        value: '7.8≤L＜15.5L'
                      },
                      {
                        label: 'L＜3.7',
                        value: 'L＜3.7'
                      },
                      {
                        label: 'L≥15.5',
                        value: 'L≥15.5'
                      }
                    ]
                  }
                ]
              },
              {
                label: '基础机',
                value: '基础机',
                children: [
                  {
                    label: '基础机',
                    value: '基础机',
                    children: [
                      {
                        label: '3.7≤L＜7.8',
                        value: '3.7≤L＜7.8'
                      },
                      {
                        label: '7.8≤L＜15.5L',
                        value: '7.8≤L＜15.5L'
                      },
                      {
                        label: 'L＜3.7',
                        value: 'L＜3.7'
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]

/**
 * 品系
 * 使用到的页面： 商用车
 */

export const dictcping = [
  {
    label: '专用车',
    value: '专用车'
  },
  {
    label: '牵引车',
    value: '牵引车'
  },
  {
    label: '自卸车',
    value: '自卸车'
  },
  {
    label: '载货车',
    value: '载货车'
  }
]

/**
 * 查询类型（发动机/整车）
 * 使用到的页面： 首页-国内市场
 */

export const dictSearchType = [
  { label: '发动机', value: '0' },
  { label: '整车', value: '1' }
]

// 市场环境使用
// 数据分类

export const dataClassification = [
  {
    label: '汽油',
    value: '汽油'
  },
  {
    label: '微客',
    value: '微客'
  },
  {
    label: '微改',
    value: '微改'
  },
  {
    label: '微卡',
    value: '微卡'
  }
]

export const fadongjichangjia = {
  '船机': ['玉柴', '潍柴', '上柴', '东康', '重康'],
  '单机': ['玉柴', '潍柴', '上柴', '东康', '重康', 'MTU']
}

/**
 * 数据分类(汽油、微客、微改、微卡)
 */

// export const dictDataType = [
//   { label: '汽油', value: '汽油' },
//   { label: '微客', value: '微客' },
//   { label: '微改', value: '微改' },
//   { label: '微卡', value: '微卡' }
// ]
export const dictDataType = [
  { label: '总数', value: '汽油，微客，微改， 微卡' },
  { label: '总数（不含汽油，微客，微改， 微卡）', value: '   ' }
]

/**
 * 分类模块
 * 使用到的页面： 竞争环境-市场环境-通机
 */
export const dictSubMarket = [
  { label: '整体', value: '整体' },
  { label: '工程机械', value: '工程机械' },
  { label: '挖掘机', value: '挖掘机', parent: '工程机械' },
  { label: '装载机', value: '装载机', parent: '工程机械' },
  { label: '矿用车', value: '矿用车', parent: '工程机械' },
  { label: '高空作业平台', value: '高空作业平台', parent: '工程机械' },
  { label: '工业动力', value: '工业动力' },
  { label: '内燃叉车', value: '内燃叉车', parent: '工业动力' },
  { label: '钻机', value: '钻机', parent: '工业动力' },
  { label: '空压机', value: '空压机', parent: '工业动力' },
  { label: '农业机械', value: '农业机械' },
  { label: '拖拉机', value: '拖拉机', parent: '农业机械' },
  { label: '小麦机', value: '小麦机', parent: '农业机械' },
  { label: '水稻机', value: '水稻机', parent: '农业机械' },
  { label: '玉米机', value: '玉米机', parent: '农业机械' },
  { label: '花生机', value: '花生机', parent: '农业机械' }
]
