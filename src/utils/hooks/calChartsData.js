import { ref, reactive } from 'vue'

export default function () {
  // 参照数据
  const referData = reactive({
    monthSort: [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月'
    ],
    quarterSort: ['第一季度', '第二季度', '第三季度', '第四季度'],
    quarterRefer: { Q1: '第一季度', Q2: '第二季度', Q3: '第三季度', Q4: '第四季度' }
  })
  // 一个表的第二个y轴数据（常用型，非常用型自行设计）
  const yAxisRight = ref([
    {
      name: '单位：(%)',
      unit: '%',
      type: 'value',
      position: 'right',
      nameTextStyle: {
        color: '#44546A',
        fontSize: 12,
        align: 'center',
        padding: [0, 0, 0, 25]
      },
      axisLabel: {
        show: true,
        formatter: '{value}%',
        fontSize: 12,
        color: '#44546A'
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#9BA4AB'
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisPointer: {
        label: {
          show: true,
          precision: 2
        }
      }
    }
  ])

  /**
 * 设置series数据：一维度数组自行分类分X轴Y轴数据(适用于后端返回一维数组，分类xy轴数据都在一个对象里的)
 */
  const setOneArraySeriesData = ({ list, xAxisKey, yAxisKey, legendKey, legendSortKey }) => {
    const currentList = JSON.parse(JSON.stringify(list))
    const defaultItemJson = { name: '', value: '' } // 默认的item的所有属性
    // 设置默认的item的所有属性
    if (currentList && currentList.length > 0) {
      const item = currentList[0]
      for (let i in item) {
        defaultItemJson[i] = ''
      }
    }
    // 设置series分类name 分类数据data
    const series = []
    // 设置x轴name y轴value
    currentList.forEach(v => {
      v.name = v[xAxisKey]
      v.value = v[yAxisKey]
    })
    // 获取所有的分类名称
    let legendNameArray = []
    if (legendSortKey) {
      legendNameArray = dropArraySameJsonByKey(currentList, legendKey)
      legendNameArray = sortByValue(legendNameArray, legendKey, legendSortKey)
      legendNameArray = legendNameArray.map(v => v[legendKey])
    } else {
      legendNameArray = [...new Set(currentList.map(v => v[legendKey]))]
    }
    // 把其他项放到最后
    const legendOtherIndex = currentList.findIndex(e => e === '其他')
    if (legendOtherIndex !== -1) {
      const item = legendNameArray.splice(legendOtherIndex, 1)[0] // 移除指定位置的元素
      legendNameArray.push(item) // 将元素添加到数组末尾
    }
    // 获取所有的X轴不重复的值
    const xAxisNameArray = [...new Set(currentList.map(v => v[xAxisKey]))]
    // 遍历分类
    legendNameArray.forEach(ele => {
      // 获取同一分类的所有data数据
      const sameData = currentList.filter(v => v[legendKey] === ele)
      // 补齐每个分类中data缺少的数据
      const data = []
      xAxisNameArray.forEach(el => {
        const item = sameData.find(e => e[xAxisKey] === el)
        if (item) {
          data.push(item)
        } else {
          const json = { ...defaultItemJson }
          json[legendKey] = ele
          json.name = el
          json[xAxisKey] = el
          json.value = ''
          json[yAxisKey] = ''
          data.push(json)
        }
      })
      series.push({ name: ele, data })
    })
    return series
  }

  /**
   * 根据指定列表排序legend
   * @param {*} list 组合好的series数组
   * @param {*} sortArray 排序数组
   * @returns 排序好的数组
   */
  function sortLegendByArray(list, sortArray) {
    const newList = []
    const copyList = JSON.parse(JSON.stringify(list))
    sortArray.forEach(ele => {
      const item = copyList.find(el => el.name === ele)
      if (item) newList.push(item)
    })
    return newList
  }
  // 按指定数组排序xAxis 需要在数组组合成能展示echarts图之后再进行处理
  function sortByArray(list, sortArray, key) {
    const newList = JSON.parse(JSON.stringify(list))
    // 获取数据基础结构
    const defaultJson = {}
    for (let i = 0; i < newList.length; i++) {
      if (newList[i].data && newList[i].data.length > 0) {
        const dataItem = newList[i].data[0]
        for (let j in dataItem) {
          defaultJson[j] = ''
        }
        break
      }
    }
    // 按排序数组排序
    newList.forEach(el => {
      const oldData = el.data
      const newData = []
      sortArray.forEach(e => {
        const item = oldData.find(i => i[key] === e)
        if (item) {
          newData.push(item)
        } else {
          const json = { ...defaultJson }
          json[key] = e
          json.name = e
          newData.push(json)
        }
      })
      el.data = newData
    })
    return newList
  }
  /**
   * 将某个值（其他）放置到第一个
   * @param {*} data 图表数据 需要在数组组合成能展示echarts图之后再进行处理
   * @param {*} dataName 某个值（其他）
   * @returns 新的排序后的数组
   */
  function findOther2First(data, dataName = '其他') {
    const list = JSON.parse(JSON.stringify(data))
    const otherIndex = list.findIndex(el => el.name === dataName)
    if (otherIndex !== -1) {
      const other = list.splice(otherIndex, 1)
      list.unshift(...other)
    }
    return list
  }
  /**
   * 将某个值（其他）放置到最后一个
   * @param {*} data 图表数据 需要在数组组合成能展示echarts图之后再进行处理
   * @param {*} dataName 某个值（其他）
   * @returns 新的排序后的数组
   */
  function findOther2Last(data, dataName = '其他') {
    const list = JSON.parse(JSON.stringify(data))
    const otherIndex = list.findIndex(el => el.name === dataName)
    if (otherIndex !== -1) {
      const other = list.splice(otherIndex, 1)
      list.push(...other)
    }
    return list
  }

  // 数据按第一根柱状图从大到小排序 otherLast是否将其他放在最后 sortByXAxisIndex-按第几行排序，其中last表示按最后一列排序
  function sortRankByFirstBarData(data, otherLast = true, sortByXAxisIndex = 0) {  
    const seriesFirstBarRank = [] 
    let sortIndex = sortByXAxisIndex 
    data.forEach((el,index) => {
      const item = JSON.parse(JSON.stringify(el))
      if (sortByXAxisIndex === 'last' && index === 0) sortIndex = item?.data.length -1
      item.valueRank = item?.data[sortIndex]?.value ? item?.data[sortIndex].value : 0
      seriesFirstBarRank.push(item)
    })
    seriesFirstBarRank.sort(function (a, b) {
      return b.valueRank - a.valueRank
    })
    seriesFirstBarRank.forEach(el => {
      if (el.valueRank) delete el.valueRank
    })
    if (otherLast) {
      const otherIndex = seriesFirstBarRank.findIndex(el => el.name === '其他')
      if (otherIndex !== -1) {
        const other = seriesFirstBarRank.splice(otherIndex, 1)
        seriesFirstBarRank.push(...other)
      }
    }
    return seriesFirstBarRank
  }
  
  /**
   * 保留几位小数，尾数是0去掉
   * @param {*} num 要计算精度的数值
   * @param {*} decimals 精度
   * @returns 保留几位小数的值
   */
  function decimalPoint(num, decimals = 2) {
    const number = parseFloat(num);
    const factor = 10 ** decimals;
    return isNaN(number) ? '' : parseFloat((Math.round((number + 1e-12) * factor) / factor).toFixed(decimals));
  }

  /**
   * json数组删除同key的项返回新数组
   * @param {*} list 要操作的数组
   * @param {*} legendKey 数组每项重要对比的key
   */
  function dropArraySameJsonByKey(list, legendKey = 'name') {
    const copyList = JSON.parse(JSON.stringify(list))
    const seen = new Set();
    const newList = copyList.filter(obj => {
      if (!seen.has(obj[legendKey])) {
        seen.add(obj[legendKey]);
        return true;
      }
      return false;
    });
    return newList
  }

  /**
   * 根据某个key值按大到小排序，相同的按name字母排序
   * @param {*} list 
   * @param {*} key 
   */
  function sortByValue(list, nameKey, valueKey) {
    const copyList = JSON.parse(JSON.stringify(list))
    copyList.sort((a, b) => {
      if (a[valueKey] !== b[valueKey]) {
        return a[valueKey] - b[valueKey]; // 按数值升序排序
      } else {
        // 按中文拼音首字母排序（使用 `zh-CN` 本地化参数）
        return a[nameKey].localeCompare(b[nameKey], 'zh-CN');
      }
    });
    return copyList
  } 
 
  return {
    referData,
    yAxisRight,
    setOneArraySeriesData,
    sortLegendByArray,
    sortByArray,
    findOther2First,
    findOther2Last,
    sortRankByFirstBarData,
    decimalPoint
  }
}
