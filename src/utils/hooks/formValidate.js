// 表单提交相关方法
export default function () {
  /**
   * @description el-date-picker组件 今天以及今天之前的日期
   * @param time 日期选择器callback值
   */
  function disabledFeatureDate(time) {
     return time.getTime() > Date.now() - 8.64e6;  //如果没有后面的-8.64e6就是不可以选择今天的
  }

    /**
   * @description el-date-picker组件 只能选择今天或者今天之后的时间
   * @param time 日期选择器callback值
   */
  function disabledOldDate(time) {
    return time.getTime() < Date.now() - 8.64e7;  //如果没有后面的-8.64e7就是不可以选择今天的 
  }
  return {
    disabledFeatureDate
  }
}
