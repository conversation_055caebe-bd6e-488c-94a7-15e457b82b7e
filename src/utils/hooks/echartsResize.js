import { nextTick } from "vue";

export default function (myChart) {
  const store = useStore(); 
  const opened = computed(() => store.state.biapp.sidebar.opened); 
  watch(opened, async () => { 
    setTimeout(() => {
      resizeHandler()
   },281)

  })
  function resizeHandler() {
    if (myChart) {
      myChart.resize({
        width: 'auto',  // 宽度随着父容器变化而变化
        height: 'auto'  // 高度随着父容器变化而变化
    });
    }
  };
  onUnmounted(() => {
    if (myChart) {
      window.removeEventListener("resize", resizeHandler);
      if (myChart && !myChart.isDisposed()) myChart.dispose();
    }
  });
  return {
    resizeHandler,
  }
}