import { allDateRange } from '@/api/system/date-range'

/**
 * 获取字典数据
 */
export function getAllDateRange (dataSource) {
  return new Promise(async resolve => {
    let array = []
    // 有缓存值就不需要再调用接口
    if (sessionStorage.getItem('allDateRange')) {
      // commit('SET_ALL_DATE_RANGE', JSON.parse(sessionStorage.getItem('allDateRange')))
      array = JSON.parse(sessionStorage.getItem('allDateRange'))
      const dateInfo = array.find(v => v.dataSource === dataSource)
      resolve(dateInfo)
    } else {
      let { data } = await allDateRange({ allDateRange })
      // commit('SET_ALL_DATE_RANGE', data)
      sessionStorage.setItem('allDateRange', JSON.stringify(data))
      const dateInfo = data.find(v => v.dataSource === dataSource)
      resolve(dateInfo)
    }
  })
}
