import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

const ExpiresInKey = 'Admin-Expires-In'

export function getToken() {
  // TODO: 本地开发
  // return "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImM5OWNlZjIzLTA4NDYtNGMyYS1iM2JkLTU2N2UzN2IzN2EzMSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.iXZg3waR3gv-3GBwmQyrNP0OLzJfrT8ErgWtnHffmaAUu1cRvOigVBKtqmDGpg1IdtrVvKqdNHfxU3IOWnj6PA"
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getExpiresIn() {
    // TODO: 本地开发
  // return -1
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}
