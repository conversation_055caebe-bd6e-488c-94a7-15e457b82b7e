<template>
  <el-dialog
    v-model="modelValue"
    :title="`搜索:${props.searchKey}`"
    :close-on-click-modal="false"
    top="4vh"
    width="80vw"
    append-to-body
    @close="closeDialog"
  >
    <div class="wrap">
      <el-tabs v-model="data.active" type="border-card" class="bi-tabs">
        <el-tab-pane :name="0" label="新闻区">
          <el-table
            :data="news.list"
            v-loading="loading.news"
            class="table-box"
            height="calc(80vh - 88px)"
            :show-header="false"
            @row-click="toggleList"
            style="width: 100%"
          >
            <el-table-column>
              <template #default="{ row }">
                <div class="ellipsis">
                  <WordHighlighter
                    :query="props.searchKey"
                    :highlightStyle="{ 'font-weight': 'bolder', background: 'transparent' }"
                    >{{ row.title }}</WordHighlighter
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column width="60">
              <template #default="{ row }">
                <div class="views">
                  <el-icon :size="12" color="#115E93" class="icon"> <View /> </el-icon>
                  <div class="num">{{ row.clickNum }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="time" width="120" show-overflow-tooltip />
          </el-table>
          <BiPagination
            :total="news.total"
            v-model:page="news.params.pageNum"
            v-model:limit="news.params.pageSize"
            @pagination="initData"
          />
        </el-tab-pane>
        <el-tab-pane :name="1" label="报告区">
          <el-table
            class="table-box"
            :data="report.list"
            v-loading="loading.report"
            height="calc(80vh - 88px)"
            style="width: 100%"
          >
            <el-table-column label="文件名">
              <template #default="scope">
                <span :title="scope.row.fileKey">{{ scope.row.fileName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="keyWord" label="关键字" />
            <el-table-column prop="createTime" width="150" label="日期" />
            <el-table-column width="140" label="操作">
              <template #default="{ row }">
                <el-button-group style="margin-left: -12px">
                  <el-button type="primary" @click="downloadWorld(row)" text> 下载 </el-button>
                  <el-button type="primary" @click="previewResource(row)" text> 预览 </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <BiPagination
            :total="report.total"
            v-model:page="report.params.pageNum"
            v-model:limit="report.params.pageSize"
            @pagination="initReportList"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
  <infoDialog v-model="data.dialogFlag" :query="news.listQuery" :isPage="isPage" />
</template>
<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import WordHighlighter from 'vue-word-highlighter'
import infoDialog from '@/views/home/<USER>/infoDialog.vue'

import { gQueryHotNews as queryHotNews } from '@/api/intelligence/tag.js'
import { getReportListByAuth } from '@/api/common/common.js'
import { signedUrl } from '@/api/upload.js'

const { proxy } = getCurrentInstance()

const props = defineProps({
  searchKey: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['close'])
const modelValue = defineModel({ require: true })
const data = reactive({
  active: 0,
  dialogFlag: false
})
const isPage = ref(false)
const news = reactive({
  params: {
    hotTagName: '',
    startDate: '',
    endDate: '',
    searchKey: '',
    pageNum: 1,
    pageSize: 15
  },
  list: [],
  total: 0,
  listQuery: {}
})

const report = reactive({
  params: {
    dictModuleId: '',
    dictLeftMenuId: '',
    dictNewsId: '',
    fileType: '02',
    s_date: '',
    e_date: '',
    pageNum: 1,
    pageSize: 15,
    fileName: ''
  },
  list: [],
  total: 0
})

const loading = reactive({
  news: false,
  report: false
})
watch(modelValue, val => {
  news.list = []
  news.total = 0
  report.list = []
  report.total = 0
  if (val) {
    initData()
    initReportList()
  }
})
const initData = async () => {
  if (loading.news) return
  loading.news = true
  const params = news.params
  params.searchKey = props.searchKey
  const res = await queryHotNews(params).catch(e => e)
  if (res.code !== 200) return (loading.news = false)
  news.list = [...res.rows]
  news.total = res.total
  await nextTick()
  loading.news = false
}

const initReportList = async () => {
  if (loading.report) return
  loading.report = true
  const params = report.params
  params.fileName = props.searchKey
  const res = await getReportListByAuth(params).catch(e => e)
  if (res.code !== 200) return (loading.report = false)
  report.list = [...res.rows]
  report.total = res.total
  await nextTick()
  loading.report = false
}

const toggleList = ev => {
  console.log('测试',ev)

  if (ev.type == 0) {
    isPage.value = false
  }
  if (ev.type == 1) {
    isPage.value = true
  }
  news.listQuery = { searchKey: props.searchKey, newsInfoId: ev.newsId, newsTagId: ev.tagId,infoContent:ev.title }
  data.dialogFlag = true
  
}

const downloadWorld = row => {
  console.log(row.fileName)
  proxy.downloadJSON(
    `/upload/download?fileName=${row.fileName}&fileKey=${row.fileKey}`,
    {},
    `${row.fileName}`
  )
}

const previewResource = ({ fileKey }) => {
  signedUrl({ fileKey }).then(res => {
    window.open(res.data, '_blank')
  })
}
const closeDialog = () => {
  emit('close', true)
}
</script>
<style scoped lang="scss">
@import '@/assets/styles/bi/variables.module.scss';
.views {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .icon {
    margin-right: 4px;
  }
  .num {
    color: #051c2c;
  }
}
.wrap {
  height: 80vh;
  min-height: 80vh;
  padding: 0;
  :deep(.el-tabs) {
    border-radius: 8px;
    height: 100%;
    background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);
    .el-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
  :deep(.el-tabs--border-card) {
    & > .el-tabs__content {
      padding: 0 16px 16px 16px;
    }
  }
}
</style>
