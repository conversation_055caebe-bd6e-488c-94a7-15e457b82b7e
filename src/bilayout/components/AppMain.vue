<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <keep-alive :include="cachedViews">
        <component :is="Component" :key="route.path" />
      </keep-alive>
    </router-view>
  </section>
</template>

<script setup>
let store = useStore()
const route = useRoute()

const cachedViews = computed(() => {
  return store.state.tagsView.cachedViews
})

watch(
  () => route,
  val => {
    store.dispatch('tagsView/addCachedView', val)
  },
  {
    deep: true,
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.app-main {
  min-height: $bi-main-height; // 总高-头部高-marign高度-1px的处理顶部Marin重合问题
  // width: calc(100% - $bi-layout-margin);
  // margin-bottom: $bi-layout-margin;
  // background: $bi-main-background-color;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  // margin-top: calc($bi-head-height + $bi-layout-margin);
  margin-top: $bi-head-height;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
