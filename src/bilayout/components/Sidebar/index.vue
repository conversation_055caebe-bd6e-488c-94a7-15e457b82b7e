<template>
  <div v-if="props.localDevice === 'mobile'" class="mobile-bi">
    <el-drawer
      v-model="expand"
      direction="ltr"
      :show-close="false"
      :with-header="false"
      @closed="closeDrawer"
    >
      <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper mobile-bi">
        <div class="menu-select-name">{{ route.matched[0].meta.title }}</div>
        <el-menu
          :default-active="activeMenu"
          :background-color="
            sideTheme === 'theme-dark'
              ? variables.biSidebarBackgroundColor
              : variables.menuLightBackground
          "
          :text-color="sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
          :unique-opened="true"
          :active-text-color="theme"
          :collapse-transition="false"
          mode="vertical"
        >
          <sidebar-item
            v-for="(route, index) in sidebarRouters"
            :key="route.path + index"
            :item="route"
            :base-path="route.path"
            :active-menu="activeMenu"
          />
        </el-menu>
      </el-scrollbar>
    </el-drawer>
  </div>

  <div
    v-else
    class="sidebar-container-bi"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark'
          ? variables.biSidebarBackgroundColor
          : variables.menuLightBackground
    }"
  >
    <!-- <el-icon
      :class="{ 'is-active': getters.biSidebar.opened }"
      color="#d6ebfa"
      size="18"
      @click="toggleSideBar"
      class="bi-hamburger"
      ><CaretLeft
    /></el-icon> -->
    <div class="bi-hamburger" @click="toggleSideBar">
      <!-- <ArrowRight :class="{ 'is-active': getters.biSidebar.opened }" /> -->
      <img
        :class="{ 'is-active': getters.biSidebar.opened }"
        src="@/assets/images/arrow-right.png"
        alt=""
      />
    </div>
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <div v-if="!isCollapse" class="menu-select-name">{{ route.matched[0].meta.title }}</div>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          sideTheme === 'theme-dark'
            ? variables.biSidebarBackgroundColor
            : variables.menuLightBackground
        "
        :text-color="sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
          :active-menu="activeMenu"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/bi/variables.module.scss'
const emits = defineEmits(['closeDrawer'])
const props = defineProps({
  localDevice: {
    type: String,
    required: false
  },
  mobileExpend: {
    type: Boolean,
    default: false
  }
})
const getters = computed(() => store.getters)
const route = useRoute()
const store = useStore()
const expand = ref(false)
const sidebarRouters = computed(() => store.getters.biSidebarRouters)
const sideTheme = computed(() => store.state.bisettings.sideTheme)
const theme = computed(() => store.state.bisettings.theme)
const isCollapse = computed(() => !store.state.biapp.sidebar.opened)
watch(
  () => props.mobileExpend,
  val => {
    expand.value = val
    // console.log('expand.value ', expand.value)
  },
  { immediate: true }
)
const activeMenu = computed(() => {
  const { meta, path } = route
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
function toggleSideBar() {
  store.dispatch('biapp/toggleSideBar')
}

function closeDrawer() {
  emits('closeDrawer', true)
}
</script>
<style lang="scss" scoped>
.menu-select-name {
  height: 41px;
  background: url(/src/assets/images/side-top-bg.png) no-repeat;
  background-size: 100% 100%;
  font-size: 18px;
  font-weight: bold;
  color: #272a2e;
  padding: 8px 0 0 34.5px;
}
:deep(.el-drawer__body) {
  padding: 0;
}
</style>
