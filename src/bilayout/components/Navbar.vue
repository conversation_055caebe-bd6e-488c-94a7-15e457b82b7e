<template>
  <div class="navbar">
    <div class="bi-head">
      <div class="bi-logo">
        <!-- <img src="@/assets/icons/svg/bi-app-logo.svg" class="logo" /> -->
      </div>
      <div class="right-menu">
        <header-search style="margin-right: 24px" />
        <div class="avatar-container">
          <el-dropdown
            @command="handleCommand"
            class="right-menu-item hover-effect"
            trigger="click"
          >
            <div class="avatar-wrapper">
              <div class="item">
                <!-- <img :src="getters.avatar" class="user-avatar" /> -->
                 <div class="user-avatar">{{ getters?.nickName[0] }}</div>
                <div class="user-name">{{ getters.nickName }}</div>
              </div>
              <!-- <el-divider direction="vertical" /> -->
              <!-- <div class="item">
                <div class="user-name">{{ getters.name }}</div>
              </div>
              <el-divider direction="vertical" />
              <div class="item">
                <div class="user-name">{{ loginDateFormat }}</div>
              </div> -->
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <router-link to="/user/profile">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link> -->
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div class="bi-segmented">
      <el-icon v-if="localDevice === 'mobile'" class="bi-switch" @click="toggleExpand">
        <Expand v-if="!expand" />
        <Fold v-else />
      </el-icon>
      <el-scrollbar class="scrollbar-container">
        <el-segmented
          block
          v-model="segmentedValue"
          :options="segmentedOptions"
          @change="toggleNavBar"
        />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { Expand, Fold } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import HeaderSearch from './HeaderSearch'
const emits = defineEmits(['toggleExpand'])
const store = useStore()
const route = useRoute()
const router = useRouter()

const props = defineProps({
  localDevice: {
    type: String,
    required: false
  },
  mobileExpend: {
    type: Boolean,
    default: false
  }
})
const expand = ref(false)
watch(
  () => props.mobileExpend,
  val => {
    expand.value = val
  },
  { immediate: true }
)
function toggleExpand() {
  expand.value = !expand.value
  emits('toggleExpand', expand.value)
}
// 所有的路由信息
const routers = computed(() => store.state.permission.biDefaultRoutes)
const segmentedValue = ref('Home')
const getters = computed(() => store.getters)
// 顶部显示菜单
const segmentedOptions = computed(() => {
  let topMenus = []
  routers.value.map(menu => {
    if (menu.hidden !== true) {
      const json = {
        label: menu.meta ? menu.meta.title : '',
        value: menu.name,
        disabled: false,
        ...menu
      }
      topMenus.push(json)
    }
  })
  return topMenus
})
// 默认激活的菜单
const activeMenu = computed(() => {
  const matched = route.matched
  return matched[0].name
})

// 监听导航tab切换替换侧边栏导航-》通知store改变侧边栏
watch(
  activeMenu,
  ev => {
    segmentedValue.value = ev
    let sideBar = []
    const routersArray = segmentedOptions.value
    for (let i = 0; i < routersArray.length; i++) {
      const item = routersArray[i]
      if (item.name === ev) {
        sideBar = item.children
        sideBar.forEach(el => {
          if (el.path.indexOf(item.path) !== 0) {
            el.path = `${item.path}/${el.path}`
          }
        })
        break
      }
    }
    store.dispatch('setBiSidebarRouters', sideBar)
  },
  {
    immediate: true
  }
)
function handleCommand(command) {
  switch (command) {
    // case "setLayout":
    //   setLayout();
    //   break;
    case 'logout':
      logout()
      break
    default:
      break
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      store.dispatch('LogOut').then(() => {
        location.href = '/index'
      })
    })
    .catch(() => {})
}

// const emits = defineEmits(["setLayout"]);
// function setLayout() {
//   emits("setLayout");
// }
const toggleNavBar = ev => {
  router.push({ name: ev })
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.navbar {
  height: $bi-head-height;
  // padding: 0 56px;
  overflow: hidden;
  position: relative;
  box-shadow: 0px 4px 10px 0px rgba(18, 96, 230, 0.2);
  .right-menu {
    display: flex;
    align-items: center;
    line-height: 88px;
    margin-right: 21px;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      font-size: 16px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      display: flex;
      align-items: center;

      .avatar-wrapper {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .item {
          width: fit-content;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #2970da;
          color: white;
        }
        .user-name {
          margin: 0 12px 0 10px;
          font-size: 14px;
          color: #4b525a;
          font-weight: normal;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
  .bi-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    .bi-logo {
      img {
        width: 553px;
        height: 34px;
      }
    }
  }
  :deep(.bi-segmented) {
    display: flex;
    margin-top: 8px;
    height: 56px;
    .el-scrollbar__wrap {
      flex: 1;
      display: flex;
      align-items: center;
      .el-scrollbar__view {
        width: 100%;
      }
    }
  }
  :deep(.el-segmented) {
    width: 100%;
    // height: 44px;
    padding: 0;
    font-size: 16px;
    label {
      font-weight: normal;
    }
    --el-segmented-item-selected-color: #ffffff;
    --el-segmented-item-selected-bg-color: #2970da;
    --el-border-radius-base: 25px;
    color: #051c2c;
    background: transparent;
    &.is-block {
      .el-segmented__item {
        min-width: auto;
      }
    }
    .el-segmented__item:not(.is-disabled):not(.is-selected):hover {
      // --el-segmented-item-selected-bg-color: #DFEDFD;
      background: #dfedfd;
      // color: #d6ebfa;
    }
    .el-segmented__item.is-selected {
      color: #fff;
      font-weight: bold;
    }

    .el-segmented__group {
      display: flex;
      align-items: center;
      height: 34px;
      padding: 0 72px;
    }
    .el-segmented__item {
      min-width: 112px;
      // max-width: 110px;
      height: 100%;
    }
    .el-segmented__item-selected {
      background: url('/src/assets/images/bi-nav-bg.png') no-repeat;
      background-size: cover;
    }
  }
}
.bi-switch {
  width: 30px;
  height: 30px;
  margin: 10px 10px;
  color: #909399;
}
</style>
