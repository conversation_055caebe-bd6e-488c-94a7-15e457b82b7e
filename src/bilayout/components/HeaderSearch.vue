<template>
  <div class="header-search">
    <el-input v-model="data.search" placeholder="请输入搜索关键文字">
      <template #append>
        <el-button :icon="Search" @click="searchData" />
      </template>
    </el-input>
    <SearchDialog v-model="data.dialogFlag" @close="data.search = ''" :search-key="data.search" />
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import SearchDialog from './SearchDialog.vue'

const data = reactive({
  search: '',
  dialogFlag: false
})
const searchData = () => {
  // if (data.search.trim() === '') {
  //   return
  // }
  data.dialogFlag = true
}
</script>

<style lang="scss" scoped>
.header-search {
  :deep(.el-input) {
    font-size: 16px;
    width: 240px;
    height: 36px;
  }
}
</style>
