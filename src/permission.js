import router from './router'
import store from './store'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { getQueryObject } from '@/utils'
NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/auth-redirect', '/bind', '/register','/sso/workwx']

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch('GetInfo')
          .then(() => {
            store.dispatch('GenerateRoutes').then(accessRoutes => {
              // 根据roles权限生成可访问的路由表
              accessRoutes.forEach(route => {
                if (!isHttp(route.path)) {
                  router.addRoute(route) // 动态添加可访问路由表
                }
              })
              // 设置字典
              store.dispatch('dicts/getAllDictsData').then(() => {
                next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
              })
            })
          })
          .catch(err => {
            store.dispatch('LogOut').then(() => {
              ElMessage.error(err)
              next({ path: '/' })
            })
          })
      } else {
        next()
      }
    }
  } else {
    // 没有token 
    if (
      import.meta.env.VITE_APP_ENV === 'development' ||
      window.location.href.indexOf('qas.yuchai.com') != -1 ||
      window.location.href.indexOf('121.37.241.102') != -1
    ) {
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next()
      } else {
        next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
        NProgress.done()
      }
    } else {
      if(window.location.href.indexOf('/sso/workWx') != -1){
        next()
      }else{
        //需要单点登录时
        const { code = '' } = getQueryObject()
        let loginUrl =
            'http://mpaas-test.app.yuchai.com/oauth2/oauth/authorize?response_type=code&client_id=iis&scope=all&redirect_uri=http://iis-spider.qas.yuchai.com'
        let hrefUrl = 'http://i.qas.yuchai.com'
        //是否是生产环境
        if (window.location.href.indexOf('app.yuchai.com') != -1) {
          loginUrl =
              'http://mpaas.app.yuchai.com/oauth2/oauth/authorize?response_type=code&client_id=iis&scope=all&redirect_uri=https://iis.app.yuchai.com'
          hrefUrl = 'https://i.yuchai.com'
        }
        if (!code) {
          window.location.href = loginUrl
          NProgress.done()
        } else {
          store
              .dispatch('getSSonToken', { code })
              .then(() => {
                next({ path: '/' })
                NProgress.done()
              })
              .catch(() => {
                window.location.href = hrefUrl
                NProgress.done()
              })
        }
      }

    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
