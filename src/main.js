import { createApp } from 'vue'
import './utils/flexiable.js'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import locale from 'element-plus/es/locale/lang/zh-cn'
import "element-plus/theme-chalk/index.css"; 
import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive

// 注册指令
import plugins from './plugins' // plugins
import { download, downloadJSON } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control

import { useDict } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, hasPermission } from '@/utils/ruoyi'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.downloadJSON = downloadJSON

app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.hasPermission = hasPermission

// 全局组件挂载
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)
app.component('QuillEditor', QuillEditor)

directive(app)

router.beforeEach((to, from, next) => {
  if (to.path === '/') {
    next('/home/<USER>')
  } else {
    next()
  }
})
;(function () {
  var base = 1920
  var client = window.screen.availWidth
  var rate = (client < 1200 ? 1200 : client) / base
  document.querySelector('html').style.fontSize = Math.min(rate, 1) * 192 + 'px'
})()

store.state.app.commandKey = ''
// 按住shit键时点查询，强制从数据库查询
window.addEventListener('keydown', e => {
  store.state.app.commandKey = e.key
  // 2s后自动释放按键
  setTimeout(() => {
    store.state.app.commandKey = ''
  }, 2000)
})
window.addEventListener('keyup', e => {
  store.state.app.commandKey = ''
})
// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'large'
})
// 引用背景水印插件
import vue3WaterMarker from 'vue3-water-marker'
app.use(vue3WaterMarker)
app.mount('#app')
