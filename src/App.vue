<template>
  <vue3-water-marker
    v-if="showWaterMarker"
    opacity="0.1"
    :text="`${getters.name} ${getters.loginDate}`"
  ></vue3-water-marker>
  <router-view />
</template>

<script setup>
// 引用背景水印插件
import { ref, watch } from 'vue'
import { getToken } from '@/utils/auth'
let showWaterMarker = ref(false)
const route = useRoute()
const store = useStore()
const getters = computed(() => store.getters)
watch(route, newVal => {
  showWaterMarker.value = getToken()
})
</script>
