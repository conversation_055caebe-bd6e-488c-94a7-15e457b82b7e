@import './variables.module.scss';
.table-list {
  display: flex;
  flex-direction: column;
  height: $bi-main-height;
  padding: 0 $bi-layout-margin;
  
  &__search {
    padding: $bi-layout-margin 0 0;

    &__button {
      display: flex;
    }

    .el-col {
      .el-form-item {
        width: 100%;
        margin: 0 0 16px 0;
      }
    }
  }

  &__control {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 50px;
    padding-bottom: $bi-layout-margin;
  }
 
  &__content {
    flex: 1;
    overflow: hidden;
    &--control {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .el-button.el-button--default {
        border: 0;
        border-radius: 0;
        padding: 6px;
      }
    }
    // .cell {
    //   color: $bi-font-color;
    // }
    // .el-table__header-wrapper {
    //   th {
    //     font-size: 14px;
    //     font-weight: 400;
    //   }
    // }
  } 
}
.bi-engine-center .cell {
  display: flex;
  align-items: center;
  justify-content: center;
}