@import './variables.module.scss';
@import './sidebar.scss';
@import './table.scss'; 
.wrap {
  width: 100%;
  min-height: $bi-main-height;
  padding: $bi-layout-margin;
}
// 宽高比
.ratio-width {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 60.4%;
  &__wrap {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}

// flex布局设置换行边距
.el-row {
  margin-bottom: $bi-layout-margin;
  &:last-child {
    margin-bottom: 0;
  }
}
// 重写el-form下form搜索组件样式
.search-form {
  padding: 16px $bi-layout-margin 0;
  margin-bottom: $bi-layout-margin;
  background: linear-gradient(180deg, #D2E6FC 0%, rgba(210, 230, 252, 0.5) 100%);
  border-radius: 8px;
  &__button {
    display: flex;
  }
  .el-col{
    .el-form-item {
      width: 100%;
      margin: 0 0 10px 0;
    }
  }
}
.tabs-form {
  padding: 20px 0 0;
  &__button {
    display: flex;
  }
  .el-col {
    .el-form-item {
      width: 100%;
      margin: 0 0 16px 0;
    }
  }
}

// 重写el-table组件样式
.el-table {
  
  --el-table-header-bg-color: transparent;
  --el-table-header-text-color: #051C2C;
  --el-table-text-color: #051C2C;;
  thead {
    tr {
      background: linear-gradient(180deg, #C6DFF7 0%, #E8F2FC 100%);
    }
    th {
      font-weight: normal;
    }
  }
}

// 重写el-tabs组件样式
.el-tabs {
  --el-tabs-header-height: 50px;
}
.bi-tabs {
  &.el-tabs--border-card {
    background: transparent;
    border: none;
    .el-tabs__nav-scroll {
      display: flex;
      align-items: center;
      height: 50px;
    } 
    .el-tabs__nav {
      height: 42px;
      border-radius: 8px;
      align-items: center;
      background: #fff;
    }
    &>.el-tabs__header {
      padding: 0 4px;
      border: 1px solid;
      border-image: linear-gradient(270deg, rgba(138, 188, 249, 0) 0%, #83B5F3 51%, rgba(131, 181, 243, 0) 99%, rgba(131, 181, 243, 0) 99%) 1;
      background: transparent;
      .el-tabs__item {
        height: 38px;
        border: none;
        margin: 0;
        color: #051C2C;
        font-size: 16px;
        border-radius: 8px;
        &.is-active {
          border-radius: 8px;
          background: #0B5FC5;
          font-weight: bold;
          box-shadow: 0px 2px 4px 0px rgba(1, 80, 154, 0.2);
          color: #fff;
        }
        &:first-child {
          margin-left: 2px;
        }
        &:last-child {
          margin-right: 2px;
        }
      } 
    }

    .bi-tabs-in {
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 50px;
      }
      .el-tabs__nav-scroll {
        display: flex;
        align-items: center;
        height: 50px;
        padding: 0 20px;
      }
      .el-tabs__nav {
        height: 50px;
        border-radius: none;
        align-items: center;
        background: transparent;
      }
      .el-tabs__item {
        &.is-active {
          font-weight: bold;
        }
      }
    }
  } 
}

// 重写 el-page-header 组件样式
.bi-page-header { 
  .el-page-header__icon {
    margin-right: 6px;
  }
  .el-page-header__title {
    font-size: 16px;
  }
}

// 重写 el-loading 组件样式
.bi-loading {
  .el-loading-mask {
    background-color: rgba(0, 0, 0, 0.2);
    /* 调整为你想要的颜色 */
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



// 重置button默认颜色
.bi-button {
  padding: 6px 16px;
}
.el-button {
  &.el-button--primary,&.el-button--info,&.el-button--default {
    @extend .bi-button; 
  }
  &.el-button--text {
    padding: 6px;
  }
} 

// 重写 表单样式
.el-input {
  --el-input-icon-color: #051C2C;
}
.el-input__wrapper { 
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
}
.el-date-editor .el-range__icon {
  color: #051C2C;
}

.bi-segmented-page {
  .el-segmented--large {
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  }
  label.el-segmented__item {
    font-weight: normal;
  }
}
.el-select__wrapper.is-disabled {
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
}
.el-select__wrapper.is-disabled .el-select__selected-item {
  color:var(--el-text-color-regular);
}

// 图标右侧提示
.bar-box {
  position: relative;

  &__warn {
    position: absolute;
    top: 4px;
    right: 4px;
    z-index: 11;
  }
}
.bar-box-tooltip {
  width: 300px;
  background: rgba(13, 41, 102, 0.9) !important;
  font-size: 16px !important;
  line-height: 28px !important;

  .el-popper__arrow:before {
    background: rgba(13, 41, 102, 0.9) !important;
    border: 0.00521rem solid rgba(13, 41, 102, 0.9) !important;
}
}

.bi-uploads {
  .el-progress {
    display: none;
  }
}

// 重写el-card组件样式
.el-card {
  border: none;
  border-radius: 8px;
}
.el-card__header {
  border: none;
  padding: 0;
}
.el-card__body {
  padding: 0;
}

.bi-loading-mask .el-loading-mask {
  z-index: 8;
}

.charts-tooltip-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1px;
  padding: 0 4px;
  background-color: #F1F2F2;
  border-radius: 4px;
}