#app {

  .main-container-bi {
    min-height: calc(100% - 1px); // 视口高减去border-top占用1px
    transition: margin-left .28s;
    // margin-left: calc($bi-sidebar-width + $bi-layout-margin * 2);
    margin-left: $bi-sidebar-width;
    position: relative;
    padding-top: 1px ;
  }
  .mobile-main-container-bi {
    margin-left: 0;
  }
  .sidebar-container-bi {
    -webkit-transition: width .28s;
    transition: width 0.28s;
    width: $bi-sidebar-width !important;
    background-color: $bi-sidebar-background-color;
    // height: calc(100vh - $bi-layout-margin * 2 - $bi-head-height);
    height: calc(100vh - $bi-head-height);
    position: fixed;
    font-size: 0px;
    top: $bi-head-height;
    // top: $bi-head-height + $bi-layout-margin;
    bottom: 0;
    // left: $bi-layout-margin;
    // left: $bi-layout-margin;
    z-index: 1001;
    // overflow: hidden;
    border-right: 1px solid #f3f2f2;
  }

  .sidebar-container-bi, .mobile-bi {
    .bi-memu-item {
      --el-menu-item-height: 44px;
      --el-menu-item-font-size: 16px;
      --el-menu-text-color: $bi-font-color;
      --el-menu-active-color: $bi-font-color;
      padding: 5px 0;
      .el-menu-item, .el-sub-menu__title {
        border-radius: 8px;
        margin: 0 16px;
        color: #051C2C;
        &:hover {
          // background: #DFEDFD !important;
        }
  
        &.is-active {
          background: linear-gradient(90deg, #0B5FC5 0%, #AED8FF 100%);
          color: #fff;
          font-weight: bold;
          box-shadow: 0px 2px 8px 0px rgba(1, 80, 154, 0.15),inset 0px 0px 2px 0px #77AFF3;
          // background: #d6ebfa;
        }
  
  
        .svg-icon {
          width: 20px;
          height: 20px;
        }
  
        .el-menu-tooltip__trigger {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 !important;
        }
      }
    }

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 10px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
    .el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item, .el-sub-menu__title {
       padding: 0 20px; 
    }
    .el-menu-item,
    .el-sub-menu__title {
      // width: 100%;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: #DFEDFD !important;
      }
    }

    & .theme-dark .is-active>.el-sub-menu__title {
      color: $bi-font-color;
    }

    & .nest-menu .el-sub-menu>.el-sub-menu__title {
      min-width: $bi-sidebar-width !important;

      &:hover {
        background-color: #DFEDFD !important;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      // background-color: $base-sub-menu-background !important;

      &:hover {
        // background-color: $base-sub-menu-hover !important;
      }
    }
    .bi-hamburger {
      position: absolute;
      transform: translate(50%, -50%);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      right: 0;
      top: 50%;
      z-index: 1;
      width: 24px;
      height: 24px;
      // background: #3c4c66;
      width: 16px;
      height: 50px;
      border-radius: 8px;
      background: #FFFFFF;
      border: 1px solid rgba(216, 217, 220, 0.5);
      box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.1);
    }  
    .bi-hamburger img {
      width: 6px;
      height: 10px;
      &.is-active {
        transform: rotate(180deg);
      }
    }
    .hamburger-container-bi {
      position: relative;
      height: 10px;

      .hamburger {
        position: absolute;
        right: 5px;
        top: 5px;
        z-index: 1;
      }
    }
  }

  .hideSidebarBi {
    .sidebar-container-bi {
      width: $bi-hide-sidebar-width !important;
      .router-link-active {
        display: flex;
        align-items: center;
        padding: 0 16px;
      }
      // .is-active {
      //   width: 42px;
      // }
    }

    .main-container-bi {
      // margin-left: calc($bi-hide-sidebar-width + $bi-layout-margin * 2);
      margin-left: $bi-hide-sidebar-width;
    }
    .sidebar-container-bi {
      .bi-memu-item {
        display: flex;
        justify-content: center;
        // align-items: end;
        a {
          display: flex;
          justify-content: center;
        }
        .el-menu-item {
          margin: 0;
          padding: 0;
          width: 42px;
          .svg-icon {
            margin: 0;
          }
        }
      }
    }
    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        margin: 0;
        .svg-icon {
          margin: 0; 
        }

      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }

          &>i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $bi-sidebar-width !important;
  }

  // mobile responsive
  // .mobile-bi {
  //   .main-container-bi {
  //     margin-left: 0px;
  //   }

  //   .sidebar-container-bi {
  //     transition: transform .28s;
  //     width: $bi-sidebar-width !important;
  //   }

  //   &.hideSidebarBi {
  //     .sidebar-container-bi {
  //       pointer-events: none;
  //       transition-duration: 0.3s;
  //       transform: translate3d(-$bi-sidebar-width, 0, 0);
  //     }
  //   }
  // }

  .withoutAnimation {

    .main-container-bi,
    .sidebar-container-bi {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: #DFEDFD !important;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}