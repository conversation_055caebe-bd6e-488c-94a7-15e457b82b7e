<template>
  <CommonTabs :hideList="[1,3]" active="2" />
  <!-- <div>
    <el-tabs v-model="activeName" type="border-card" class="bi-tabs" @tab-click="tabsClickFun"> -->
      <!-- <el-tab-pane label="数据区" name="0">
        <DataChart moduleKey="competitor" />
      </el-tab-pane> -->
      <!-- <el-tab-pane label="新闻区" name="1">
        <NewsList />
      </el-tab-pane> -->
      <!-- <el-tab-pane label="报告区" name="2">
        <ReportList />
      </el-tab-pane> -->
    <!-- </el-tabs>
  </div> -->
</template>

<script setup>
import CommonTabs from '@/views/components/tabs/CommonTabs'

import { ref } from 'vue'
import NewsList from '@/views/components/tabs/NewsList.vue'
import ReportList from '@/views/components/tabs/ReportList.vue'
import DataChart from '@/views/components/tabs/DataChart.vue'
const activeName = ref('1')
const tabsClickFun = () => {}
</script>

<style lang="scss" scoped></style>
