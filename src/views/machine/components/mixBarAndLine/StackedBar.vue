<template>
  <div class="ratio-width" :style="{ paddingBottom: props.height }">
    <div ref="echartRef" class="ratio-width__wrap" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted } from 'vue'
import echartsResize from '@/utils/hooks/echartsResize.js'
import { color, colorMap, formatter, tooltip, numFormat, girtAndlenged } from '../../../components/echarts/config'
const props = defineProps({
  height: {
    // 是否展示标题
    type: String,
    required: false,
    default: '45%'
  },
  titleRotate: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  series: {
    // series数据
    // [{"name":"牵引车","data":[{"name":"2022","value":46.59}]}]
    type: Array,
    required: true,
    default: () => []
  },
  stack: {
    type: String,
    required: false,
    default: ''
  },
  yAxisName: {
    type: String,
    required: false,
    default: ''
  },
  yAxisLabelFormate: {
    type: String,
    required: false,
    default: ''
  },
  color: {
    // 每条折线的颜色
    type: Array,
    required: false,
    default: () => color
  },
  grid: {
    // grid数据
    type: Object,
    required: false,
    default: () => ({ left: 60, bottom: 46, right: 46, top: 46 })
  },
  showTotal: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  xAxisInterval: {
    type: [String, Number],
    required: false,
    default: 0
  },
})
watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const echartRef = ref(null)
const totalData = ref([]) // 柱形图总计值
let legendListener = null
onMounted(() => {
  console.log('onMounted: ');
  myChart = echarts.init(echartRef.value)
  // renderEcharts()
  const { resizeHandler: resizeHandlerL } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandlerL)
  // 监听lenged的选择
  legendListener = myChart.on('legendselectchanged', event => {
    let dataSeries = initSeries(props.series, event?.selected)
    myChart.setOption({
      series: dataSeries
    })
  })
})
// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  // 所有柱状图legend靠右
  let legend = props?.legend || {}
  let reverseLegend = props?.reverseLegend
  let grid = props?.grid || {}
  // console.log(props?.series,legend,'series')
  // 所有柱状图legend都靠右
  let type = props?.series && props?.series[0]?.type ? props?.series[0]?.type : 'bar'
  if (type === 'bar') {
    //计算grid
    let { grid: _grid } = girtAndlenged({...props,series:props.series})
    legend = {
      orient: 'vertical',
      top: '50',
      left: '4',
      itemHeight: 6,itemWidth: 6,
      textStyle: { fontSize: 10 },
      ...legend
    }
    reverseLegend = true
    grid = _grid
  }
  const options = {
    color: props?.color || color,
    tooltip: {
      ...JSON.parse(JSON.stringify({ ...tooltip, ...props?.tooltip }))
    },
    legend: {
      type: 'scroll',
      bottom: '5px',
      right: '4px',
      // data: props.series.map(v => v.name),
      data: (series => {
        if (reverseLegend) series = series.reverse()
        var sortSeries = []
        var hasOther = false
        if (!props.totalSortLegend) {
          for (var j in series) {
            if (series[j].name == '其他') {
              hasOther = true
            } else {
              sortSeries.push(series[j].name)
            }
          }
          if (hasOther) {
            if (reverseLegend) {
              sortSeries.unshift('其他')
            } else {
              sortSeries.push('其他')
            }
          }
        } else {
          sortSeries = getSortData(series, reverseLegend ? -1 : 1)
        }
        return sortSeries
      })(JSON.parse(JSON.stringify(props.series))),
      ...legend
    },
    grid: grid,
    xAxis: [
      {
        type: 'category',
        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          rotate: props.titleRotate ? 45 : 0,
          interval: props.xAxisInterval
        },
        axisTick: {
          show: false //显示x轴刻度
        },

        axisLine: {
          show:false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        },
        data: getXAxisData(props.series),
        ...props?.xAxis
      }
    ],
    yAxis: [
      {
        show:false,
        name: props.yAxisName,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: props.yAxisLabelFormate
          ? {
              formatter: props.yAxisLabelFormate,
              color: '#44546A'
            }
          : {
              color: '#44546A'
            },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        },
        ...props?.yAxis
      }
    ],
    series: initSeries(props.series)
  }
  if (props.yAxisMax !== '') {
    options.yAxis.forEach(el => {
      el.max = props.yAxisMax
    })
  }
  if (props.otherYAxis) {
    options.yAxis.push(...props.otherYAxis)
  }
  // tooltip添加总计
  // if (props.showTotal) {
  options.tooltip.formatter = params => {
    return formatter(params, totalData, props.tooltipUnits, props, props.addTooltipTotalPercent)
  }
  // }
  console.log('options: ', options);
  myChart.setOption(options)
}
// 生成符合视图的数据
function initSeries(data, selected) {
  const itemColor = props.color
  const series = []
  let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  const allData = Array.from({ length: allDataLength }).map(() => 0) // 总计

  data.forEach((el, index) => {
    let colorTmp = colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length]

    //玉柴集团的图标显示阴影
    const itemStyle = {}
    const lineStyle = {}
    if(el.name.includes('玉柴')){
      // itemStyle.shadowBlur = 6
      // // itemStyle.shadowColor = colorTmp
      // itemStyle.shadowOffsetX = 0
      // itemStyle.shadowOffsetY = 0
      // itemStyle.opacity = 1

      // lineStyle.shadowBlur = 6
      // // lineStyle.shadowColor = colorTmp
      // lineStyle.shadowOffsetX = 0
      // lineStyle.shadowOffsetY = 0
      // lineStyle.opacity = 1

      colorTmp = '#E72331'
    }
    const json = {
      symbol: 'circle',
      symbolSize: 6,
      name: el.name,
      type: 'bar',
      stack: 'total',
      ...el,
      barWidth: 30,
      itemStyle: {
        ...itemStyle,
        color:colorTmp,
        ...el?.itemStyle
      },
      lineStyle:{
        ...lineStyle
      },
      data: el.data,
      barWidth: '40%',
      z: 1
    }

    if (props.sort !== '') json.sort = props.sort
    series.push(json)
  })

  filterData?.forEach((el, index) => {
    el.data.forEach((v, vdx) => {
      let _value = Number(v.value || 0)
      allData[vdx] = allData[vdx] + _value
    })
  })

  // 添加总计
  if (props.showTotal) {
    series.push({
      name: '总计',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        position: 'top',
        formatter: function (p) {
          let _precision = props?.precision
          return numFormat(allData[p.dataIndex], _precision)
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: Array.from({ length: allDataLength }).map(() => 0)
    })
    totalData.value = allData
  }
  return series
}
function getXAxisData(value,suffix='年') {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => `${v.name}${suffix}`)
}

onUnmounted(() => {
  // 取消监听legend事件
  if (legendListener) {
    myChart.off('legendselectchanged', legendListener)
  }
  // 清理图表资源
  myChart && myChart.dispose()
})
</script>

<style lang="scss" scoped>
.ratio-width {
  padding-bottom: 18%;
}
</style>
