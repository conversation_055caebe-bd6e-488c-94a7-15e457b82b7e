<template>
  <div class="ratio-width" :style="{ paddingBottom: props.height }">
    <div ref="echartRef" class="ratio-width__wrap" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted } from 'vue'
import echartsResize from '@/utils/hooks/echartsResize.js'
import {
  color,
  colorMap,
  formatter,
  tooltip,
  numFormat,
  girtAndlenged
} from '../../../components/echarts/config'

const props = defineProps({
  height: {
    // 是否展示标题
    type: String,
    required: false,
    default: '45%'
  },
  titleRotate: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  series: {
    // series数据
    // [{"name":"牵引车","data":[{"name":"2022","value":46.59}]}]
    type: Array,
    required: true,
    default: () => []
  },
  stack: {
    type: String,
    required: false,
    default: ''
  },
  yAxisName: {
    type: String,
    required: false,
    default: ''
  },
  yAxisLabelFormate: {
    type: String,
    required: false,
    default: ''
  },
  color: {
    // 每条折线的颜色
    type: Array,
    required: false,
    default: () => color
  },
  grid: {
    // grid数据
    type: Object,
    required: false,
    default: () => ({ left: 56, bottom: 56, right: 150, top: 46 })
  },
  showTotal: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  xAxisInterval: {
    type: [String, Number],
    required: false,
    default: 0
  }
})
watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const echartRef = ref(null)
const totalData = ref([]) // 柱形图总计值
let legendListener = null
onMounted(() => {
  // console.log('onMounted: ');
  myChart = echarts.init(echartRef.value)
  // renderEcharts()
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)
  // 监听lenged的选择
  legendListener = myChart.on('legendselectchanged', event => {
    let dataSeries = initSeries(props.series, event?.selected)
    myChart.setOption({
      series: dataSeries
    })
  })
})
// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  if (props.series.length === 0) return
  const options = {
    color: props?.color || color,
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 0,
      textStyle: { fontSize: 14, color: '#1D2129' },
      axisPointer: {
        label: {
          precision: 2,
          show: true,
          margin: 5,
          backgroundColor: '#0b1f56',
          color: '#fff',
          fontSize: 14
        }
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      bottom: '10px',
      right: 0,
      itemHeight: '6',
      textStyle: { fontSize: 10 },
      data: props.series.filter(v => v.type == 'line').map(v => v.name)
    },
    grid: { left: 46, bottom: 46, right: 125, top: 46 },
    xAxis: [
      {
        type: 'category',
        data: getXAxisData(props.series),
        axisTick: { show: false, alignWithLabel: true },
        axisLine: { show: false },
        axisLabel: { fontSize: 10, color: '#44546A', interval: 0 }
      }
    ],
    yAxis: [
      {
        name: '',
        type: 'value',
        axisLine: {
          show: false
          // lineStyle: { color: '#9BA4AB' }
        },
        axisLabel: {
          show: false
          // formatter: '{value}',
          // color: '#44546A',
          // fontSize: 14,
          // lineHeight: 16
        },
        axisTick: { show: false },
        splitLine: {
          show: false
          // lineStyle: { color: 'rgba(28, 130, 197, .3)', type: 'dashed' }
        }
      },
      {
        name: '',
        // splitNumber: 1,
        type: 'value',
        nameTextStyle: {
          show: false,
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 12,
          color: '#44546A'
        },
        axisLine: { show: false, lineStyle: { color: '#9BA4AB' } },
        axisTick: { show: false },
        splitLine: { show: false }
      }
    ],
    series: initSeries(props.series)
    /* series: [
    {
      name: '非电',
      type: 'bar',
      data: [
        { name: '1月', value: 7.9 },
        { name: '2月', value: 5.43 },
        { name: '3月', value: 7.9 },
        { name: '4月', value: 5.43 },
        { name: '5月', value: 5.43 },
        { name: '6月', value: 7.9 },
        { name: '7月', value: 5.43 },
        { name: '8月', value: 7.9 },
        { name: '9月', value: 5.43 },
        { name: '10月', value: 7.9 },
        { name: '11月', value: 7.9 },
        { name: '12月', value: 5.43 }
      ],
      itemStyle: { color: '#051C2C' },
      stack: 'total',
      barWidth: '40%'
    },
    {
      name: '电',
      type: 'bar',
      data: [
        { name: '1月', value: 2.04 },
        { name: '2月', value: 1.36 },
        { name: '3月', value: 2.04 },
        { name: '4月', value: 1.36 },
        { name: '5月', value: 1.36 },
        { name: '6月', value: 2.04 },
        { name: '7月', value: 1.36 },
        { name: '8月', value: 2.04 },
        { name: '9月', value: 1.36 },
        { name: '10月', value: 2.04 },
        { name: '11月', value: 2.04 },
        { name: '12月', value: 1.36 }
      ],
      itemStyle: { color: '#00A9F4' },
      stack: 'total',
      barWidth: '40%'
    },
    {
      name: '柳汽同比',
      type: 'line',
      data: [
        { name: '1月', value: 20.49 },
        { name: '2月', value: 20 },
        { name: '3月', value: 20.49 },
        { name: '4月', value: 20 },
        { name: '5月', value: 20 },
        { name: '6月', value: 20.49 },
        { name: '7月', value: 20 },
        { name: '8月', value: 20.49 },
        { name: '9月', value: 20 },
        { name: '10月', value: 20.49 },
        { name: '11月', value: 20.49 },
        { name: '12月', value: 20 }
      ],
      itemStyle: { color: '#FFB300' },
      yAxisIndex: 1,
      showSymbol: false,
      symbolSize: 2,
      lineStyle: { color: '#FFB300' },
      label: { show: true, position: 'right', formatter: '{c}%' }
    },
  ] */
  }

  // tooltip添加总计
  options.tooltip.formatter = params => {
    return formatter(params, totalData)
  }
  // 通过实例.setOption(options)
  // console.log('options: ', JSON.stringify(options));
  myChart.setOption(options)
}
// 生成符合视图的数据
function initSeries(data, selected) {
  const itemColor = props.color
  const series = []
  if (selected) {
    // 纯电渗透率
    selected['纯电渗透率'] = false
  }
  let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  const allData = Array.from({ length: allDataLength }).map(() => 0) // 总计
  data.forEach((el, index) => {
    const json = {
      name: el.name,
      type: el.type,
      data: el.data,
      itemStyle: {
        color: colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length]
      }
    }
    if (el.stack) json.stack = el.stack
    if (el.type === 'bar') {
      json.barWidth = '40%'
    } else if (el.type === 'line') {
      json.yAxisIndex = 1
      json.showSymbol = true
      json.symbolSize = 2
      json.lineStyle = {
        color: itemColor[index]
      }
      json.label = {
        show: true,
        position: 'right',
        formatter: '{c}%'
      }
    }
    series.push(json)
  })
  filterData?.forEach((el, index) => {
    el.data.forEach((v, vdx) => {
      let _value = Number(v.value || 0)
      allData[vdx] = allData[vdx] + _value
    })
  })
  // allData.forEach(v => {
  //   if (countDecimalPlaces(v) > 2) {
  //     v = v.toFixed(2)
  //   }
  //   return v
  // })

  return series
}
function getXAxisData(value, suffix = '月') {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => `${v.name}${suffix}`)
}

onUnmounted(() => {
  // 取消监听legend事件
  if (legendListener) {
    myChart.off('legendselectchanged', legendListener)
  }
  // 清理图表资源
  myChart && myChart.dispose()
})
</script>

<style lang="scss" scoped>
.ratio-width {
  padding-bottom: 18%;
}
</style>
