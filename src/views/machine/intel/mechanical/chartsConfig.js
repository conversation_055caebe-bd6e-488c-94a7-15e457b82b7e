import { reactive } from 'vue'
export default function () {

  // 图表1数据配置
  const configChartA = reactive({
    legend: {
      orient: 'horizontal'
    },
    grid: { left: 66, bottom: 60, right: 26, top: 46 },
    xAxis: [
      {
        axisLabel: {
        }
      }
    ],
    yAxis: [
      {
        name: '单位：(台)'
      }
    ],
    series: []
  })

  // 图表2数据配置
  const configChartB = reactive({
    legend: {
      orient: 'horizontal'
    },
    xAxis: [
      {
        axisLabel: {
        }
      }
    ],
    yAxis: [
      {
        name: '单位：(台)'
      },
      {
        name: '单位：(%)',
        splitNumber: 5,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 12,
          color: '#44546A'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: []
  })

  // 图表3数据配置
  const configChartC = reactive({
    legend: {
      orient: 'horizontal'
    },
    grid: { left: 66, bottom: 60, right: 26, top: 46 },
    xAxis: [
      {
        axisLabel: {
        }
      }
    ],
    yAxis: [
      {
        name: '单位：(台)'
      }
    ],
    series: []
  })

  // 图表4数据配置
  const configChartD = reactive({
    legend: {
      orient: 'horizontal'
    },
    xAxis: [
      {
        axisLabel: {
        }
      }
    ],
    yAxis: [
      {
        name: '单位：(台)'
      },
      {
        name: '单位：(%)',
        splitNumber: 5,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 12,
          color: '#44546A'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: []
  })

  /**
  * 设置series数据：一维度数组自行分类分X轴Y轴数据(适用于后端返回一维数组，分类xy轴数据都在一个对象里的)
  */
  const setSeriesData = ({ list, xAxisKey, yAxisKey, legendKey }) => {
    const defaultItemJson = { name: '', value: 0 } // 默认的item的所有属性
    // 设置默认的item的所有属性
    if (list && list.length > 0) {
      const item = list[0]
      for (let i in item) {
        defaultItemJson[i] = ''
        if (/^[-+]?(\d+\.?\d*|\.\d+)([eE][-+]?\d+)?$/.test(item[i]))
          defaultItemJson[i] = typeof item[i] === 'string' ? '0' : 0
      }
    }
    // 设置series分类name 分类数据data
    const series = []
    // 设置x轴name y轴value
    list.forEach(v => {
      v.name = v[xAxisKey]
      v.value = v[yAxisKey]
    })
    // 获取所有的分类名称
    const legendNameArray = [...new Set(list.map(v => v[legendKey]))]
    // 把其他项放到最后
    const legendOtherIndex = list.findIndex(e => e === '其他')
    if (legendOtherIndex !== -1) {
      const item = legendNameArray.splice(legendOtherIndex, 1)[0] // 移除指定位置的元素
      legendNameArray.push(item) // 将元素添加到数组末尾
    }
    // 获取所有的X轴不重复的值
    const xAxisNameArray = [...new Set(list.map(v => v[xAxisKey]))]
    // 遍历分类
    legendNameArray.forEach(ele => {
      // 获取同一分类的所有data数据
      const sameData = list.filter(v => v[legendKey] === ele)
      // 补齐每个分类中data缺少的数据
      const data = []
      xAxisNameArray.forEach(el => {
        const item = sameData.find(e => e[xAxisKey] === el)
        if (item) {
          data.push(item)
        } else {
          const json = { ...defaultItemJson }
          json[legendKey] = ele
          json.name = el
          json[xAxisKey] = el
          json.value = 0
          json[yAxisKey] = 0
          data.push(json)
        }
      })
      series.push({ name: ele, data })
    })
    return series
  }
  return {
    configChartA,
    configChartB,
    configChartC,
    configChartD,
    setSeriesData
  }
}
