<template>
  <div ref="chart" style="width: 600px; height: 400px;"></div>
</template>
 
<script setup>
import { onMounted, ref ,defineProps} from 'vue';
import * as echarts from 'echarts';
import echartsResize from '@/utils/hooks/echartsResize.js'
 const animationDuration = 1000
const chart = ref(null);
const props = defineProps({
  modulesData: {
    type: String,
    required: true,
  }
});
onMounted(() => {
  const myChart = echarts.init(chart.value);
  const option = {
    // ECharts 配置项
    title: {
      left: "center",
      text: props.modulesData === 'car'?'柳汽份额结构走势（北斗数）':
      props.modulesData === 'mechanical'?'工程机械之三一动力结构比例走势':
      props.modulesData === 'agro'?'农业机械之常州东风动力结构比例走势':'',
      textStyle: {
       fontSize: 15
     },
    },
    tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  
  legend: {
    left: 'right',      // 放置在右侧
    top: 'bottom',  
    bottom: 0,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLabel: {
        fontSize: 10  // 减小字体大小
    },
           // 去除背景线
       splitLine: {
            show: true
        }

    }
  ],
  yAxis: [
    {
      type: 'value',
        name: '单位（%）',
      axisLabel: {
            formatter: '{value} %' // 显示为百分比
        },
             // 去除背景线
       splitLine: {
            show: false
        }

    }
  ],
  series: [
    {
        type: "bar",
        data: [30, 20, 40, 40, 40, 40, 50, 30, 20, 40, 50, 40],
        name: "玉柴",
        symbolSize:1,
        stack: "total",
        animationDuration,
        barWidth:'50%',
        emphasis: {
            focus: 'series'
          },
          itemStyle: {
              color: '#115E93'
          },
        
      },

      {
        type: "bar",
        data: [40, 40, 20, 30, 20, 30, 10, 30, 10, 40, 30, 30],
        name: "潍柴",
        symbolSize:1,
        stack: "total",
        animationDuration,
        emphasis: {
        focus: 'series'
        },
        itemStyle: {
            color: '#5FCEFF'
        },
      },
      {
        type: "bar",
        data: [20, 30, 30, 20, 30, 20, 30, 30, 60, 10, 10, 20],
        name: "康明斯",
        symbolSize:1,
        stack: "total",
        // barWidth: "40%",
        animationDuration,
        emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#C2E7F2'
      },
      },
      {
        type: "bar",
        data: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
        name: "其他",
        symbolSize:1,
        stack: "total",
        animationDuration,
        emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#00A9F4'
      },
      },
  ]
  };
 
  option && myChart.setOption(option);
  onMounted(() => {
  optionBiao(props.dataSource)
  
});
});

</script>
 
<style>
/* 你的样式 */
</style>