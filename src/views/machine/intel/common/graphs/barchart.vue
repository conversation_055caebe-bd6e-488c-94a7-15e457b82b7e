<template>
  <div ref="chart" style="width: 600px; height: 400px;"></div>
</template>
 
<script setup>
import {ref, onMounted ,defineProps} from 'vue';
import * as echarts from 'echarts';
import echartsResize from '@/utils/hooks/echartsResize.js'
const animationDuration = 1000
const chart = ref(null);
const dataSource00 = ref('')
const props = defineProps({
  modulesData: {
    type: String,
    required: true,
  },
  dataSource: {
    type: String,
    required: true,
  }
});


watch(() => props.dataSource, (newVal, oldVal) => {
      // console.log('DOM 已更新',newVal);
      // 这里可以根据需要对父组件传递的值做出相应的处理
      dataSource00.value = newVal
      optionBiao(newVal)
      // onMounted()
    });

    const  optionBiao = (newVal) =>{
      const myChart = echarts.init(chart.value);
  const option = {
    // ECharts 配置项
    title: {
      left: "center",
      text: props.modulesData === 'car'?'柳汽卡车份额结构走势'+'('+newVal+')':
      props.modulesData === 'mechanical'?'工业动力之杭叉动力结构走势'+'('+newVal+')':
      props.modulesData === 'agro'?'农业机械之常州东风动力结构走势'+'('+newVal+')':'' ,
      textStyle: {
       fontSize: 15
     },
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params) {
                // var total = 0;
                // for (var i = 0, l = params.length; i < l; i++) {
                //     total += params[i].data;
                // }
                // return params[0].name + '<br/>' + '总数' + ' : ' + total;
                var total = 0;
            var result = params[0].name + '<br/>';
            params.forEach(function (item) {
                if (item.value != null) {
                    total += item.value;
                }
            });
            params.forEach(function (item) {
                if (item.value != null) {
                    result += item.marker +item.seriesName + ' : ' + item.value +'<br/>';
                }
            });
            result += '总和 : ' + total;
            return result;
            }

  },
  
  legend: {
    left: 'right',      // 放置在右侧
    top: 'bottom',  
    bottom: 0,
    
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLabel: {
        fontSize: 10  // 减小字体大小
    }
    }
  ],
  yAxis: [
    {
      nameGap: 25, // 名称与轴线之间的距离
       name:'万台',
      type: 'value',
         // 去除背景线
         splitLine: {
            show: true
        }

      
    }
  ],
  series: [
    {
      name: '玉柴',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#115E93'
      },
      barWidth:'50%',
      data: [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      animationDuration
    },
    
    {
      name: '潍柴',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#5FCEFF'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      animationDuration
    },
    {
      name: '康明斯',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#C2E7F2'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      animationDuration,
      label: {
                show: true,
                position: 'top',
                formatter: function(params) {
                    // 计算总和
                    let total = 0;
                    for (let i = 0; i < option.series.length; i++) {
                        total += option.series[i].data[params.dataIndex];
                    }
                    return total; 
                }
            }
    },
  ]
  };
 
  option && myChart.setOption(option);
     }
onMounted(() => {
  optionBiao(props.dataSource)
});
// 为window的resize事件绑定处理函数，让图表自适应大小

</script>
 
<style>
/* 你的样式 */
</style>