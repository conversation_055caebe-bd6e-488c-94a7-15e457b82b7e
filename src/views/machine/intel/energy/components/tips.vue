<template>
  <div class="chartTips">
    <div style="margin-bottom: 5px" class="tipTitle">
      {{ sortedParams && sortedParams[0] ? sortedParams[0].name : '' }}
    </div>
    <div class="tipItems">
      <div class="tipItem" v-for="(item, index) in sortedParams" :key="index">
        <div>
          <span :style="{ backgroundColor: item.color }" class="rang"></span>{{ item.seriesName }}
        </div>
        <div class="tipItemRight">
          <template v-if="item.seriesType != 'line'">
            <span>销量:{{  numberFormat(item.data.current_count || item.data.installationcount || 0,0) }}{{ item.seriesType == 'line' ? '%' : '台' }},</span>
            <span>占比:{{ numberFormat(item.data.proportion, 1) || 0  }}%</span>
            <span v-if="sortedParams[0].axisIndex != 0">同比:{{ numberFormat(item.data.growth_rate_percent,1) || 0 + '%' }}</span>
          </template>
          <template v-if="item.seriesType == 'line'">
            <span>销量:{{ numberFormat(item.data.current_count, 0) || 0 }}台</span>
            <span>同比:{{ numberFormat(item.data.growth_rate_percent,1) || 0 }}</span>
          </template>
        </div>
      </div>
    </div>
    <!-- <div style="margin-bottom: 10px" v-if="sortedParams[0].axisIndex == 1">
      <span
        style="
          display: inline-block;
          margin-right: 4px;
          border-radius: 10px;
          width: 10px;
          height: 10px;
          background-color: #115e93;
        "
      ></span>
      总计: {{ sortedParams.reduce((sum, x) => x.seriesType !== 'line' ? sum + x.data.current_count : sum, 0) || 0 }}台
    </div> -->
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { numberFormat } from '@/utils/format'

defineOptions({
  name: 'tips'
})

const props = defineProps({
  params: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: ''
  },
  showTotal: {
    type: Boolean,
    required: false,
    default: false
  },
  showYoy: {
    type: Boolean,
    required: false,
    default: true
  }
})

// 创建计算属性对 params 进行排序和倒序处理
const sortedParams = computed(() => {
  // 检查每个对象是否有 data.current_count 字段
  if (props.params.every(item => item.data && 'current_count' in item.data)) {
  // 按 data.current_count 字段降序排序
  return [...props.params].sort((a, b) => b.data.current_count - a.data.current_count)  
    
  }
   if (props.params.every(item => item.data && 'installationcount' in item.data)) {
  // 按 data.current_count 字段降序排序
  return [...props.params].sort((a, b) => b.data.installationcount - a.data.installationcount)  
    
  }


  return props.params
  
})
</script>

<style>
.rang {
  display: inline-block;
  margin-right: 4px;
  border-radius: 10px;
  width: 10px;
  height: 10px;
}
.tipItem {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.tipItemRight {
  display: flex;
  gap: 10px;
}
</style>