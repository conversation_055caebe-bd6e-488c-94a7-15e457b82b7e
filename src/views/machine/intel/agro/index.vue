<template>
  <CommonTabs :hideList="[1]" active="2" />

  <!-- <div class="container">
    <el-tabs v-model="activeName" type="border-card" class="bi-tabs"> -->
      <!-- <el-tab-pane label="数据区" name="0">
        <graphs :modulesData="modulesData" />
      </el-tab-pane> -->
      <!-- <el-tab-pane label="新闻区" name="1">
        <NewsList />
      </el-tab-pane>
      <el-tab-pane label="报告区" name="2">
        <ReportList />
      </el-tab-pane>
    </el-tabs>
    <morenews ref="modolForm" />
  </div> -->
</template>

<script setup>
import CommonTabs from '@/views/components/tabs/CommonTabs'

import NewsList from '@/views/components/tabs/NewsList.vue'
import ReportList from '@/views/components/tabs/ReportList.vue'
import news from '../common/news'
import graphs from '../common/graphs'
import report from '../common/report'
import Tabs from '@/views/components/Tabs'
import morenews from './morenews.vue'
const { proxy } = getCurrentInstance()
let activeName = ref('1')

function handleCurrentQuery() {
  proxy.$refs.modolForm.show()
}
</script>

<style lang="scss" scoped>
.btnClass {
  float: right;
  font-size: 14px;
  color: #2970da;
  padding: 0px;
}
.box {
  width: 100%;
  height: 100%;
  background: #fff;
}
.el-tabs--border-card {
  border: none;
}
</style>
