<template>
    <!-- 详情对话框 -->
    <el-dialog :title="title" v-model="visible" append-to-body>
      <span v-html="adata"></span>
     <template #footer>
        <div class="dialog-footer">
           <el-button @click="cancel">关闭</el-button>
        </div>
     </template>
  </el-dialog>
 </template>
 
 <script setup >
  const visible = ref(false);
  const title = ref("");
  let adata = ref('')

 
   /** 详情按钮操作 */
   function show(row) {
    visible.value = true;
    title.value = "";
    adata.value = `<p style="text-align:justify;"><b>进口方面，</b>2024年上半年零部件进口7.75亿美元，同比下降16.8%，占进口总额的59%。进口整机5.32亿美元，同比增长33.1%，占进口总额的41%。进口增长的主要产品有非公路矿用自卸车、其他起重机、320马力以上、、其他，其他工程车辆和及工程钻机等。进口额下降的主要有零部件、堆垛机、电动及升降平台、械、电梯与扶梯、液压挖掘机等。</p >  `
  }
   /** 取消按钮 */
   function cancel() {
    visible.value = false;
  }
 
  defineExpose({
   show,
 });
  </script>
  <style lang="scss" scoped>
  .texts{  
   text-indent: 20px;
   line-height: 20px;

  }
  .titles{ 
   text-align: center;
  }

</style>
  