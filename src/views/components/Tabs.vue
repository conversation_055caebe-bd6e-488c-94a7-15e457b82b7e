<template>
  <div class="tabsBox">
    <div class="flex" ref="tabs">
      <el-tabs class="tabMain" :style="dynamicStyle" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="item in tabsData" :key="item.value" :label="item.label" :name="item.value">
        </el-tab-pane>
      </el-tabs>
      <a class="more" ref="lookmore" @click="lookMore">查看更多></a>
    </div>
    <ul class="tabsUl">
      <li v-for="item2 in dataList" :key="item2.id"><div @click="handleCurrentQuery" class="textNav">{{item2.text}}</div><div class="dateNav">{{item2.date}}</div></li>
    </ul>
  </div>
  
</template>

<script setup>
import { ref, defineEmits, defineProps, defineExpose   } from 'vue'

const props = defineProps({
  tabsData: {
    type: Array,
    required: true,
  },
  dataList:{
    type:Array,
    required:true,
  },
});
const activeName = ref('')
activeName.value=props.tabsData[0].value
const emit = defineEmits(['call-parent-method', 'lookMore', 'handleCurrentQuery']);
// 点击tabs触发父组件方法
function handleClick(tab, event) {
  activeName.value= tab.props.name
  emit('call-parent-method')
}
// 点击新闻列表数据，触发父组件详情方法
function handleCurrentQuery (){ 
  emit('handleCurrentQuery')
} 
// 点击查看更多触发父组件方法
function lookMore (){ 
  emit('lookMore')
} 
// 动态样式
const dynamicStyle = reactive({
  width: '90%'
});
// 动态更新样式
function updateStyle() {
  dynamicStyle.width = tabsWidth.value+'px';
}
const tabs = ref(null)
const myElement = ref(null)
const lookmore = ref(null)
const lookmore2 = ref(null)
const tabsWidth = ref(null)
onMounted(() => {
  myElement.value = tabs.value.offsetWidth;
  lookmore2.value = lookmore.value.offsetWidth
  tabsWidth.value = myElement.value - lookmore2.value -10
  updateStyle()
});
</script>

<style lang="scss" scoped>

.tabsBox {
  width: 100%;
  padding: 10px;
}
.flex{width: 100%; display: flex; justify-content: space-between}
.tabMain{float: left;}
.tabsUl{ list-style: none;padding-inline-start: 0; padding-top: 0 !important;margin-top: 0 !important; }
.tabsUl li{ font-size: 14px; display: flex; align-items: center; border-bottom: 1px solid #f2f2f2; padding: 14px 0; }
.textNav{ flex: 1; color: #333;}
.dateNav{ width: 100px; color: #bbb;}
.more{  
  width: 10%;
  text-align: end;

  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  padding-left: 8px; 
   box-sizing: border-box;  border-bottom: 3px solid #e4e7ed; float: left; height: 40px; line-height: 44px; color: rgb(17, 94, 147); font-size: 16px;}
</style>
<style>
.el-tabs__item { 
  font-size: 16px;
  font-weight: 500;

}
.el-tabs__item.is-active, .el-tabs__item:hover{font-weight: bold; color: #115E93;}
.el-tabs__active-bar{background-color: #115E93;}
</style>