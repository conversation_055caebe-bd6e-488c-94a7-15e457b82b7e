<template>
  <el-card>
    <template #header>
      <block-title :title="props.title.text" :icon="props.title.icon" />
    </template>
    <div class="ratio-width bar-box" :style="{ paddingBottom: props.height }">
      <div ref="myChartDom" class="ratio-width__wrap" />
    </div>
  </el-card>
</template>

<script setup>
// tooltipValue 用来展示自定义显示tooltip内容
import BlockTitle from '@/views/components/BlockTitle.vue'
import _ from 'lodash'
import { numberFormat } from '@/utils/format'
import chartConfig from './chartConfig.js'


const props = defineProps({
  options: {
    type: Object,
    required: true,
    default: () => ({})
  },
  series: {
    // series数据
    type: Array,
    required: true,
    default: () => []
  },
  title: {
    // 标题（标题不是用echarts渲染的）
    type: Object,
    required: false,
    default: () => ({
      text: '',
      icon: new URL('@/assets/images/title-icon.png', import.meta.url).href
    })
  },
  height: {
    // 高度（占宽度的百分比）
    type: String,
    required: false,
    default: '45%'
  },
  showTotal: {
    // 是否展示总计
    type: Boolean,
    required: false,
    default: false
  },
  precision: {
    // 值显示的精度（几位小数）
    type: Number,
    required: false,
    default: 1
  },
  legendWrap: {
    // legend是否文字换行
    type: Boolean,
    required: false,
    default: true
  }
})
// 初始化实例
const myChartDom = ref(null)
let currentOptions = reactive({}) // 当前echarts使用的options（整合过后的）
const totalData = ref([]) // 柱形图总计值
const { myChart, symbolItemColor, defaultOptions } = chartConfig({
  props,
  myChartDom,
  renderEcharts,
  initSeries
})
watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 构建options,配置对象
function renderEcharts() {
  myChart.value.clear()
  const options = _.merge(_.cloneDeep(toRaw(defaultOptions)), props.options)
  // 设置x轴name
  options.xAxis.forEach(el => {
    el.data = getXAxisData(props.series)
  })
  // 设置series数据，生成符合视图的数据
  if (props.series && props.series.length > 0) {
    options.series = initSeries(props.series)
  } else {
    options.series = []
  }
  if (!props?.options?.legend?.data) {
    options.legend.data = (function () {
      const series = _.cloneDeep(props.series).reverse()
      return series.map(v => v.name)
    })()
  }

  // 设置grid
  options.grid = girdAndLegend(props)
  // 保存当前的options相应数据
  currentOptions = reactive(options)
  myChart.value.setOption(options)
}

// 生成符合视图的数据
function initSeries(data, selected) {
  if (!data) return []
  const series = []
  let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  const allData = Array.from({ length: allDataLength }).map(() => '') // 总计
  let totalStackName = undefined
  data.forEach((el, index) => {
    // 设置每项颜色，有指定值颜色先指定颜色，没有的按
    let color = symbolItemColor[el.name]
      ? symbolItemColor[el.name]
      : currentOptions.color
        ? currentOptions.color[index]
        : ''
    if (el.name && el.name.includes('玉柴')) {
      // itemStyle.shadowBlur = 6
      // // itemStyle.shadowColor = colorTmp
      // itemStyle.shadowOffsetX = 0
      // itemStyle.shadowOffsetY = 0
      // itemStyle.opacity = 1

      // lineStyle.shadowBlur = 6
      // // lineStyle.shadowColor = colorTmp
      // lineStyle.shadowOffsetX = 0
      // lineStyle.shadowOffsetY = 0
      // lineStyle.opacity = 1

      color = '#E72331'
    }

    const defaultJson = {
      symbol: 'circle',
      symbolSize: 6,
      type: 'bar',
      barWidth: 30,
      itemStyle: {
        color
      },
      barWidth: '40%',
      z: 1
    }
    const json = _.merge(defaultJson, el)
    if (json.type === 'bar' && json.stack !== undefined) totalStackName = json.stack
    series.push(json)
  })

  // 添加总计
  if (props.showTotal) {
    filterData?.forEach(el => {
      if (el.type === 'bar' || el.type === undefined) {
        el.data.forEach((v, vdx) => {
          if (v.value !== null && v.value !== undefined && v.value !== '') {
            if (allData[vdx] === '') allData[vdx] = 0
            allData[vdx] = allData[vdx] + Number(v.value)
          }
        })
      }
    })
    series.push({
      name: '总计',
      type: 'bar',
      stack: totalStackName,
      label: {
        show: true,
        position: 'top',
        formatter: function (p) {
          const precision = props?.precision
          return allData[p.dataIndex] !== null &&
            allData[p.dataIndex] !== undefined &&
            allData[p.dataIndex] !== ''
            ? numberFormat(allData[p.dataIndex], precision)
            : ''
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: Array.from({ length: allDataLength }).map(() => 0)
    })
    totalData.value = allData
  }
  return series
}
function getXAxisData(value) {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => v.name)
}

/**
 * 计算gird的距离
 */
function girdAndLegend(props) {
  // y轴左侧单位要多计算长度
  let yAxisLabelFormate = ''
  if (
    props.options?.yAxis &&
    props.options?.yAxis[0] &&
    props.options?.yAxis[0]?.axisLabel?.formatter
  )
    yAxisLabelFormate = props.options?.yAxis[0]?.axisLabel?.formatter
  const yAxisLabelFormateLength = new TextEncoder().encode(
    yAxisLabelFormate.replace(/\{.*?\}/g, '')
  ).length
  // y轴右侧侧单位要多计算长度
  let yAxisLabelFormateRight = ''
  if (
    props.options?.yAxis &&
    props.options?.yAxis[1] &&
    props.options?.yAxis[1]?.axisLabel?.formatter
  )
    yAxisLabelFormateRight = props.options?.yAxis[1]?.axisLabel?.formatter
  const yAxisLabelFormateRightLength = new TextEncoder().encode(
    yAxisLabelFormateRight.replace(/\{.*?\}/g, '')
  ).length

  let series = [...props?.series]
  // 左侧值的长度
  const allValueLeftArray = series.filter(
    e => e.yAxisIndex === 0 || e.yAxisIndex === '0' || e.yAxisIndex === undefined
  )
  let maxValueLeft = 0
  if (allValueLeftArray && allValueLeftArray.length > 0) {
    maxValueLeft = Math.max(
      ...allValueLeftArray.map(({ data = [] }) => {
        let _data = data.map(({ value = '' }) => (value ? value : 0))
        return Math.max(..._data)
      })
    )
  }
  maxValueLeft = Math.round(maxValueLeft)
  const maxValueLeftLength = new TextEncoder().encode(maxValueLeft).length
  // 右侧值的长度
  const allValueRightArray = series.filter(e => e.yAxisIndex === 1 || e.yAxisIndex === '1')
  let maxValueRight = 0
  if (allValueRightArray && allValueRightArray.length > 0) {
    maxValueRight = Math.max(
      ...allValueRightArray.map(({ data = [] }) => {
        let _data = data.map(({ value = '' }) => (value ? value : 0))
        return Math.max(..._data)
      })
    )
  }
  maxValueRight = Math.round(maxValueRight)
  const maxValueRightLength = new TextEncoder().encode(maxValueRight).length

  // legend长度
  let allLegendName = series.map(({ name = [] }) => {
    return name || ''
  })
  let maxLegendNameLength = getLongestString(allLegendName)
  if (maxLegendNameLength > 18 && props.legendWrap) maxLegendNameLength = 18

  let left = 6 + (maxValueLeftLength + yAxisLabelFormateLength) * 5
  let right = 46 + (maxLegendNameLength + maxValueRightLength + yAxisLabelFormateRightLength) * 5

  if (left < 46) left = 46
  return { left: left, bottom: 40, right, top: 36 }
}

// 获取数组重字符串宽度最长的长度
function getLongestString(arr) {
  if (!arr.length) return 0

  const filtered = arr.filter(str => typeof str === 'string')
  if (!filtered.length) return 0

  const maxLen = Math.max(...filtered.map(str => new TextEncoder().encode(str).length))
  return maxLen
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.ratio-width {
  padding-bottom: 45%;
}
:deep(.el-card__body) {
  padding: 0 6px 6px !important;
}
:deep(.el-card__header) {
  padding: 0;
}
</style>
