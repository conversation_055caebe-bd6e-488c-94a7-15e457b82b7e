import { reactive } from 'vue'
import { numberFormat as nf } from '@/utils/format'
export default function (myChart) {
  // 某些项强制颜色设置
  const symbolItemColor = {
    '其他': '#DDDDDD',
    '其它': '#DDDDDD'
  }
  const defaultOptions = reactive({
    color: [
      '#115E93',
      '#87AEC9',
      '#3A76FF',
      '#9CBAFF',
      '#00A9F4',
      '#7FD3F9',
      '#9BA4AB',
      '#CDD1D5',
      '#3BDBD6',
      '#9DEDEA',
      '#C280FF',
      '#E0BFFF',
      '#7248DB',
      '#B8A3ED',
      '#EE824B',
      '#F6C0A5',
      '#9B7A01'
    ],
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 0,
      textStyle: {
        //提示框自己的样式
        fontSize: 14,
        // color: '#fff'
        color: '#1D2129'
      },
      axisPointer: {
        label: {
          precision: 2,
          show: true,
          margin: 5,
          backgroundColor: '#0b1f56',
          color: '#fff',
          fontSize: 14
        }
      }
    },
    legend: {
      orient: 'vertical',
      bottom: '5',
      right: '5',
      type: 'scroll',
      itemHeight: '6',
      textStyle: { fontSize: 10, lineHeight: 20 }
    },
    xAxis: [
      {
        type: 'category',

        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          rotate: 0,
          interval: 0
        },
        axisTick: {
          alignWithLabel: true,
          show: true, //显示x轴刻度
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        }
      }
    ],
    yAxis: [
      {
        name: '',
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          color: '#44546A'
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        }
      }
    ]
  })
  const store = useStore()
  const opened = computed(() => store.state.biapp.sidebar.opened)
  watch(opened, async () => {
    setTimeout(() => {
      resizeHandler()
    }, 281)
  })
  function resizeHandler() {
    if (myChart) {
      myChart.resize({
        width: 'auto', // 宽度随着父容器变化而变化
        height: 'auto' // 高度随着父容器变化而变化
      })
    }
  }

  const numberFormat = (num, precision) => {
    return nf(num, precision)
  }

  onUnmounted(() => {
    if (myChart) {
      window.removeEventListener('resize', resizeHandler)
      myChart && myChart.dispose()
    }
  })
  return {
    symbolItemColor,
    defaultOptions,
    resizeHandler,
    numberFormat
  }
}
