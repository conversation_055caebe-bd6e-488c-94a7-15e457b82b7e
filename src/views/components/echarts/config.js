// echarts 通用配置

import { numberFormat } from '../../../utils/format'

/**
 * 各种分类线条柱形图颜色
 */
export const color = [
  '#115E93',
  '#87AEC9',
  '#3A76FF',
  '#9CBAFF',
  '#00A9F4',
  '#7FD3F9',
  '#9BA4AB',
  '#CDD1D5',
  '#3BDBD6',
  '#9DEDEA',
  '#C280FF',
  '#E0BFFF',
  '#7248DB',
  '#B8A3ED',
  '#EE824B',
  '#F6C0A5',
  '#9B7A01'
]


export const lineColor = ['#00A9F4', '#0033CC', '#115E93', '#808080', '#99CCFF', '#DDEBF7']

export const colorMap = {
  '其他': '#DDDDDD',
  '其它': '#DDDDDD'
  // '潍柴': '#FF0000'
  // '北汽福田': '#00FF00'
}

/**
 * tooltip提示框设置
 */
export const tooltip = {
  trigger: 'axis',
  appendToBody: true,
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  borderWidth: 0,
  textStyle: {
    //提示框自己的样式
    fontSize: 14,
    // color: '#fff'
    color: '#1D2129'
  },
  axisPointer: {
    label: {
      precision: 2,
      show: true,
      margin: 5,
      backgroundColor: '#0b1f56',
      color: '#fff',
      fontSize: 14
    }
  }
}
/**
 * legend提示框设置
 */
export const legend = {
  type: 'scroll',
  // bottom: 0,
  left: 'center',
  bottom: '5px'
  // right: 32
}

const pieTooltip = params => {
  if (params.value && parseFloat(params.value)) {
    const { units = '', precision = 1, data = {} } = params
    const { nowProp = '' } = data

    const value = `${params.value}` ? `${numFormat(params.value, 0)}${units}` : ''
    const _nowProp = `${nowProp}` ? `${numFormat(nowProp, precision)} %` : ''

    let tipsHtml =
      "<div class='tipItem tipItem1'>" +
      params.marker +
      params.name +
      ' : ' +
      value +
      ` , ` +
      _nowProp +
      '</div>'
    return tipsHtml
  }
  return ''
}

const getStringLength = str => {
  let arr = (str + '').split('')
  // 将数组中的字符串转为Unicode编码，并计算长度
  let length = arr
    .map(char => {
      let code = char.charCodeAt(0)
      // 对于多字节字符，charCodeAt返回的是字节的首字节
      if (0xd800 <= code && code <= 0xdbff) {
        // 可能是高代理字符（高代理字符应该与下一个字符组合成代理对）
        let low = arr[arr.indexOf(char) + 1]
        if (low) {
          let lowCode = low.charCodeAt(0)
          if (0xdc00 <= lowCode && lowCode <= 0xdfff) {
            // 是一个代理对，返回4（2个字节）
            return 4
          }
        }
      }
      // 对于单字节字符和基本多语言面的字符，返回1
      return 1
    })
    .reduce((a, b) => a + b, 0)

  return length
}

export const formatter = (params, totalData, units, otherData = {}, addTooltipTotalPercent) => {
  if (!params) return 
  if (params.length > 0 && (params.every(el => el.value === '') || totalData.value[params[0].dataIndex] === '')) return
  let { precision = 1 } = otherData || {}
  // const { seriesType = '' } = params[0]
  const seriesType = params?.[0]?.seriesType || params?.seriesType || '';

  const length = (params || []).length
  let { _yearMonth = '' } = params[0]?.data || {}

  let yearMonth = ''
  if (_yearMonth) {
    yearMonth = _yearMonth
  }
  if (length > 0) {
    // 展示顺序改为与堆叠一致，由下至上
    // params = params.reverse()

    // 展示顺序由大到小，其他放最后
    params.sort((a, b) => {
      return (b.value || 0) - (a.value || 0)
    })

    var list = []
    var other = []
    for (var i = 0; i < params.length; i++) {
      if (params[i].seriesName !== '其他') {
        list.push(params[i])
      } else {
        other = params[i]
      }
    }
    list.push(other)
    params = list
  }

  var chartTipsHtml = `
    <div class="chartTips">
      <div style="margin-bottom: 2px;" class='tipTitle'>${yearMonth} ${params && params[0] ? params[0].name : ''}</div>`

  // 超出10行，按两列展示
  var needWrap = length > 8
  var tipsHtml = `<div class="tipItems ${needWrap ? 'wraps' : ''}">`
  if (seriesType === 'pie') {
    tipsHtml += pieTooltip({ ...params, units, precision })
  }
  var totalHtml = ''
  for (var i = 0; i < length; i++) {
    if (params[i].seriesName === '总计') {
      if (totalData.value[params[i].dataIndex] !== '') totalHtml = `<div class='tipItem'  style='margin-bottom: 2px;display:flex;justify-content: space-between'>
      <span>${params[i].marker}${params[i].seriesName}</span> 
      <span>${numFormat(totalData.value[params[i].dataIndex], precision)}${`${params[i].value}` ? (units ? units : '') : ''}</span>
      </div>`
    } else {
      if (params[i].value === '' || params[i].value === null || params[i].value === undefined) continue
      // params[i].value = parseFloat(params[i].value) || 0
      // 有总计添加每个分类占总计的百分比
      let oemPercent = ''
      if (addTooltipTotalPercent) {
       
        oemPercent = params[i].value / totalData.value[params[i].dataIndex]
        if (!isNaN(oemPercent)) {oemPercent = '| ' + `${(numFormat(oemPercent * 100, otherData?.precision ?? 1) )}%` }
        else{
          oemPercent =  `${numFormat(oemPercent * 100, otherData?.precision ?? 1)|| 0}%`
        }
      }

      // if (params[i].value && parseFloat(params[i].value)) {
      var tipDiv = "<div class='tipItem'>"
      var tipText = `
      <span>${params[i].marker}${params[i].seriesName}&emsp;</span>
      `+ "<span>" + (params[i].data.tooltipValue ? params[i].data.tooltipValue : ((params[i].value !== null && params[i].value !== undefined && params[i].value !== '' ? numFormat(params[i].value, precision) : '') +
          (params[i].value !== null && params[i].value !== undefined && params[i].value !== '' ? (units ? units : '') : '') +
          ' ')) +
        oemPercent + "</span>"

      if (needWrap) {
        // 针对换行后，文本超出长度后的处理
        var x = getStringLength(tipText)
        var totalWidht = 10 * x + 10
        if (totalWidht < 145) {
          totalWidht = 145
        } else {
          totalWidht = 300
        }
        tipDiv = `<div class='tipItem' style='width:${totalWidht / 192}rem'>`
      }
      // tipsHtml += tipDiv + (i + 1) + '. ' + tipText + '</div>'
      tipsHtml += tipDiv + tipText + '</div>'
      // }
    }
  }
  tipsHtml += '</div>'
  return chartTipsHtml + tipsHtml + totalHtml + '</div>'
}

export const numFormat = (num, precision) => {
  

  return numberFormat(num, precision)
}


export const titleTipObj = {
  "物流景气指数走势": "中国物流业景气指数反映物流业经济发展变化情况，以50%作为经济强弱的分界点，高于50%表示物流业经济扩张，低于50%则表示物流业经济收缩。",
  "制造业采购经理人指数月度走势": "制造业PMI指数反映制造业经济总体变化趋势，50%为荣枯分水线，高于50%处于扩张区，低于50%处于萎缩水平。",
  "公路物流运价指数走势": "中国公路物流运价指数直接反映了市场供需关系的动态。基数为100%，高于100%运力趋紧，低于100%运力过剩。",
  "消费者信心指数月度走势": "消费者信心指数是反映消费者信心强弱的指标，指数等于100表示消费者信心处于强弱临界点，超过100时，表明消费者信心处于强信心区，小于100时，表示消费者信心处于弱信心区。",
  "小松挖掘机开工小时数": "小松挖掘机开工小时数是反映房地产和基建施工景气度的重要指标‌，开工小时数的增加通常被视为经济活动回暖的信号‌",
  "商用车气缸数销量情况": "0缸为新能源，其他缸数的为燃油车。",

}


export const girtAndlenged = (props) => {
  const yAxisLabelFormate = props?.yAxisLabelFormate || '' // y轴左侧单位要多计算长度
  const yAxisLabelFormateLength = new TextEncoder().encode(yAxisLabelFormate.replace(/\{.*?\}/g, '')).length

  const yAxisLabelFormateRight = props?.otherYAxis[0]?.axisLabel.formatter // y轴右侧侧单位要多计算长度
  let yAxisLabelFormateRightLength = 0
  if (yAxisLabelFormateRight) yAxisLabelFormateRightLength = new TextEncoder().encode(yAxisLabelFormateRight.replace(/\{.*?\}/g, '')).length
  let grid = props?.grid || {}
  let series = [...props?.series]

  const allValueLeftArray = series.filter(e => e.yAxisIndex === 0 || e.yAxisIndex === '0' || e.yAxisIndex === undefined)
  let maxValueLeft = 0
  if (allValueLeftArray && allValueLeftArray.length > 0) {
    maxValueLeft = Math.max(...allValueLeftArray.map(({ data = [] }) => {
      let _data = data.map(({ value = '' }) => (value ? value : 0))
      return Math.max(..._data)
    }))
  }
  const allValueRightArray = series.filter(e => e.yAxisIndex === 1 || e.yAxisIndex === '1')
  let maxValueRight = 0
  if (allValueRightArray && allValueRightArray.length > 0) {
    maxValueRight = Math.max(...allValueRightArray.map(({ data = [] }) => {
      let _data = data.map(({ value = '' }) => (value ? value : 0))
      return Math.max(..._data)
    }))
  }
  maxValueRight = Math.round(maxValueRight)
  let allLegendName = series.map(({ name = [] }) => {
    return name || ''
  })
  let maxLegendNameLength = getLongestString(allLegendName) 
  if(maxLegendNameLength > 18 && props.legendWrap) maxLegendNameLength = 18
  let left = 6 + maxValueLeft.toString().length * 10 + yAxisLabelFormateLength * 5
  let right = 56 + 4 * maxLegendNameLength + maxValueRight.toString().length * 10 + yAxisLabelFormateRightLength * 5

  if (left < 46) left = 46
  grid = { left: left, bottom: 40, right, top: 36, ...grid }
  return { grid }

}

// 获取数组重字符串宽度最长的长度
function getLongestString(arr) {
  if (!arr.length) return 0;

  const filtered = arr.filter(str => typeof str === 'string');
  if (!filtered.length) return 0;

  const maxLen = Math.max(...filtered.map(str => new TextEncoder().encode(str).length));
  return maxLen;
}
