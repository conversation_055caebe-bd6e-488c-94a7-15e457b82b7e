<template>
  <el-card>
    <template #header>
      <block-title :title="props.title.text" :icon="props.title.icon" />
    </template>
    <div class="ratio-width bar-box" :style="{ paddingBottom: props.height }">
      <div ref="target" class="ratio-width__wrap" />
    </div>
  </el-card>
</template>
<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import { numberFormat } from '@/utils/format'
import * as echarts from 'echarts'
import _ from 'lodash'
import hooksChartsConfig from './hooksChartsConfig.js'
/** 自定义改造  */
const props = defineProps({
  useDefaultOptions: {
    // 是否与默认options配置进行合并
    type: Boolean,
    required: false,
    default: true
  },
  options: {
    type: Object,
    required: true,
    default: () => ({})
  },
  /**
   * 自定义格式化函数
   * @param {*} params
   */
  formatter: {
    type: Function,
    required: false
  },
  title: {
    // 标题（标题不是用echarts渲染的）
    type: Object,
    required: false,
    default: () => ({
      text: '',
      icon: new URL('@/assets/images/title-icon.png', import.meta.url).href
    })
  },
  height: {
    // 高度（占宽度的百分比）
    type: String,
    required: false,
    default: '45%'
  },
  showTotal: {
    // 是否展示总计
    type: Boolean,
    required: false,
    default: false
  },
  precision: {
    // 值显示的精度（几位小数）
    type: Number,
    required: false,
    default: 1
  },
  isSort: {
    // tooltips的展示顺序由大到小，其他放最后
    type: Boolean,
    required: false,
    default: false
  }
})

// 初始化实例
let myChart = null
const target = ref(null) // charts对象
const totalData = ref([]) // 柱形图总计值
let legendListener = null
let currentOptions = reactive({}) // 当前echarts使用的options（整合过后的）
let defaultOptions = reactive({})
let symbolItemColor = {} // 某些项如其它设置固定颜色
watch(
  () => props.options.series,
  () => {
    renderEcharts()
  },
  // { deep: true }
)
onMounted(() => {
  myChart = echarts.init(target.value)
  const {
    resizeHandler,
    defaultOptions: options,
    symbolItemColor: symbolColor
  } = hooksChartsConfig(myChart)

  symbolItemColor = symbolColor
  defaultOptions = options

  renderEcharts()
  window.addEventListener('resize', resizeHandler)
  // 监听lenged的选择
  legendListener = myChart.on('legendselectchanged', event => {
    let dataSeries = initSeries(props.options.series, event?.selected)
    myChart.setOption({
      series: dataSeries
    })
  })
})
// 构建options,配置对象
const renderEcharts = () => {
  myChart.clear()
  let options = props.options
  // 使用默认配置
  if (props.useDefaultOptions) {
    options = _.merge(toRaw(defaultOptions), props.options)
    // 设置x轴name
    options.xAxis.forEach(el => {
      el.data = getXAxisData(options.series)
    })
  }
  // 设置tooltip的样式
  options.tooltip.formatter = props.formatter

  
  // series数据重新组装
  options.series = props.options.series
  
  options.series.forEach((el, index) => {
    if (el.name && el.name.includes('玉柴')) {
      el.itemStyle = {
        color: '#E72331'
      }
    }
  })
  // options.series = initSeries(options.series)
  currentOptions = reactive(options)
  // console.log('currentOptions', currentOptions)
  currentOptions.legend.formatter = function (name) {
    if (name.length > 6) {
      return `${name.substring(0, 6)}\n${name.substring(6)}`
    } else {
      return `${name}`
    }
  }
  // 媒体拼接
  // if (props.options.media) {
  let mOption = {
    baseOption: currentOptions,
    media: [
      {
        query: {
          maxAspectRatio: 1
        },
        option: {
          legend: {
            top: 0,
            right: '5',
            width: '90%'
          },
          grid: [
            {
              left: '30',
              right: '30',
              top: '10%',
              bottom: '55%',
              height: '35%'
            },
            {
              left: '44',
              right: '44',
              top: '60%',
              bottom: '5%',
              height: '35%'
            }
          ]
        }
      }
    ]
  }
  // }
  // console.log('currentOptions', mOption)
  myChart.setOption(mOption)
}

// 生成符合视图的数据
function initSeries(data, selected) {
  if (!data) return []

  const series = []
  let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  const allData = Array.from({ length: allDataLength }).map(() => 0) // 总计
  data.forEach((el, index) => {
    // 设置每项颜色，有指定值颜色先指定颜色，没有的按
    let color = symbolItemColor[el.name]
      ? symbolItemColor[el.name]
      : currentOptions.color
        ? currentOptions.color[index]
        : ''

    const defaultJson = {
      symbol: 'circle',
      symbolSize: 6,
      type: 'bar',
      itemStyle: {
        // color
      },
      z: 1
    }
    const json = _.merge(defaultJson, el)
    series.push(json)
  })

  filterData?.forEach((el, index) => {
    el.data.forEach((v, vdx) => {
      let _value = Number(v.value || 0)
      allData[vdx] = allData[vdx] + _value
    })
  })

  // 柱状堆叠图添加总计
  if (props.showTotal) {
    series.push({
      name: '总计',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        position: 'top',
        formatter: function (p) {
          let _precision = props?.precision
          return numFormat(allData[p.dataIndex], _precision)
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: Array.from({ length: allDataLength }).map(() => 0)
    })
    totalData.value = allData
  }
  // console.log('series', JSON.parse(JSON.stringify(series)))
  return series
}
function getXAxisData(value) {
  const data = value?.[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => v.name)
}

const tooltipFormatter = params => {
  let {
    options: { series, yAxis },
    precision,
    isSort
  } = props
  const length = (params || []).length
  if (length > 0) {
    for (let i = 0; i < params.length; i++) {
      const realYAxisIndex = series[params[i].seriesIndex]?.yAxisIndex
      params[i].realYAxisIndex = realYAxisIndex === undefined ? 0 : realYAxisIndex
    }
    // 展示顺序由大到小，其他放最后
    if (isSort) {
      params.sort((a, b) => {
        return (b.value || 0) - (a.value || 0)
      })
      let list = []
      let other = []
      for (let i = 0; i < params.length; i++) {
        if (params[i].seriesName !== '其他') {
          list.push(params[i])
        } else {
          other = params[i]
        }
      }
      list.push(other)
      params = list
    }
  }

  let chartTipsHtml = `
      <div class="chartTips">
        <div style="margin-bottom: 5px;" class='tipTitle'> ${params && params[0] ? params[0].name : ''}</div>`

  // 超出10行，按两列展示
  const needWrap = length > 12

  let tipsHtml = `<div class="tipItems ${needWrap ? 'wrap' : ''}">`
  let totalHtml = ''
  for (let i = 0; i < length; i++) {
    // 获取yAXIS括号里面的单位
    const units = yAxis[params[i].realYAxisIndex].name
      ? yAxis[params[i].realYAxisIndex].name.match(/\(([^)]+)\)/)[1]
      : ''
    if (params[i].seriesName === '总计') {
      totalHtml = `
          <div style='margin-bottom: 5px'>
            ${params[i].marker}${params[i].seriesName}:${numberFormat(totalData.value[params[i].dataIndex], precision)}${`${params[i].value}` ? (units ? units : '') : ''}
          </div>
        `
    } else {
      params[i].value = parseFloat(params[i].value) || 0

      // if (params[i].value && parseFloat(params[i].value)) {
      var tipDiv = "<div class='tipItem'>"
      var tipText =
        `
        <span>${params[i].marker}${params[i].seriesName}&emsp;</span>
        ` +
        '<span>' +
        numberFormat(params[i].value, precision) +
        (params[i].value !== '' || params[i].value == '0' ? (units ? units : '') : '') +
        ' ' +
        '</span>'

      if (needWrap) {
        // 针对换行后，文本超出长度后的处理
        var x = getStringLength(tipText)
        var totalWidht = 10 * x + 10
        if (totalWidht < 145) {
          totalWidht = 145
        } else {
          totalWidht = 300
        }
        tipDiv = `<div class='tipItem' style='width:${totalWidht / 192}rem'>`
      }
      // tipsHtml += tipDiv + (i + 1) + '. ' + tipText + '</div>'
      tipsHtml += tipDiv + tipText + '</div>'
      // }
    }
  }
  tipsHtml += '</div>'
  return chartTipsHtml + tipsHtml + totalHtml + '</div>'
}
const getStringLength = str => {
  let arr = (str + '').split('')
  // 将数组中的字符串转为Unicode编码，并计算长度
  let length = arr
    .map(char => {
      let code = char.charCodeAt(0)
      // 对于多字节字符，charCodeAt返回的是字节的首字节
      if (0xd800 <= code && code <= 0xdbff) {
        // 可能是高代理字符（高代理字符应该与下一个字符组合成代理对）
        let low = arr[arr.indexOf(char) + 1]
        if (low) {
          let lowCode = low.charCodeAt(0)
          if (0xdc00 <= lowCode && lowCode <= 0xdfff) {
            // 是一个代理对，返回4（2个字节）
            return 4
          }
        }
      }
      // 对于单字节字符和基本多语言面的字符，返回1
      return 1
    })
    .reduce((a, b) => a + b, 0)

  return length
}
onUnmounted(() => {
  // 取消监听legend事件
  if (legendListener) {
    myChart.off('legendselectchanged', legendListener)
  }
})



</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.ratio-width {
  padding-bottom: 45%;
}
:deep(.el-card__body) {
  padding: 0 6px 6px !important;
}
:deep(.el-card__header) {
  padding: 0;
}
</style>
