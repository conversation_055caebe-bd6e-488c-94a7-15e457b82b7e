<template>
  <el-card>
    <template #header>
      <block-title :title="props.title" :icon="props.titleIcon" />
    </template>
    <div class="ratio-width bar-box" :style="{ paddingBottom: props.height }">
      <div ref="target" class="ratio-width__wrap" />
      <el-tooltip
        v-if="contentTip"
        :content="contentTip"
        popper-class="bar-box-tooltip"
        placement="top"
      >
        <el-icon :size="18" color="rgba(13, 41, 102, 0.9)" class="bar-box__warn"
          ><InfoFilled
        /></el-icon>
      </el-tooltip>
    </div>
  </el-card>
</template>

<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import * as echarts from 'echarts'
import echartsResize from '@/utils/hooks/echartsResize.js'
import {
  color,
  colorMap,
  formatter,
  tooltip,
  numFormat,
  titleTipObj,
  girtAndlenged
} from './config.js'

const props = defineProps({
  title: {
    // 是否展示标题
    type: String,
    required: false,
    default: ''
  },
  titleIcon: {
    // 是否展示标题
    type: String,
    required: false,
    default: 'data1'
  },
  titleRotate: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  series: {
    // series数据
    type: Array,
    required: true,
    default: () => []
  },
  stack: {
    type: String,
    required: false,
    default: ''
  },
  yAxisName: {
    type: String,
    required: false,
    default: ''
  },
  yAxisMax: {
    type: [Number, String],
    required: false,
    default: ''
  },
  yAxisLabelFormate: {
    type: String,
    required: false,
    default: ''
  },
  color: {
    // 每条折线的颜色
    type: Array,
    required: false,
    default: () => color
  },
  grid: {
    // grid数据
    type: Object,
    required: false,
    default: () => ({ left: 56, bottom: 60, right: 46, top: 46 })
  },
  legend: {
    type: Object,
    required: false,
    default: () => ({})
  },
  height: {
    // 是否展示标题
    type: String,
    required: false,
    default: '45%'
  },
  showTotal: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  sort: {
    type: String,
    required: false,
    default: ''
  },
  tooltipUnits: {
    type: String,
    required: false,
    default: ''
  },
  xAxisInterval: {
    type: [String, Number],
    required: false,
    default: 0
  },
  xAxis: {
    type: Object,
    required: false,
    default: () => ({})
  },
  yAxis: {
    type: Object,
    required: false,
    default: () => ({})
  },
  otherYAxis: {
    type: Array,
    required: false,
    default: () => []
  },

  tooltip: {
    type: Object,
    required: false,
    default: () => ({})
  },
  precision: {
    type: Number,
    required: false,
    default: 1
  },
  addTooltipTotalPercent: {
    type: Boolean,
    required: false,
    default: false
  },
  reverseLegend: {
    type: Boolean,
    required: false,
    default: false
  },
  totalSortLegend: {
    // 是否按汇总排序图例
    type: Boolean,
    required: false,
    default: false
  },
  legendWrap: {
    // 是否文字换行
    type: Boolean,
    required: false,
    default: true
  },
  legendDataDeal: {
    // 是否处理legend的data数据
    type: Boolean,
    required: false,
    default: true
  }
})
const titleIcon = reactive({
  a: new URL('@/assets/images/title-icon.png', import.meta.url).href
})
watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const target = ref(null)
const totalData = ref([]) // 柱形图总计值
const contentTip = ref('')

let legendListener = null

onMounted(() => {
  myChart = echarts.init(target.value)
  renderEcharts()

  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)

  // 监听lenged的选择
  legendListener = myChart.on('legendselectchanged', event => {
    let dataSeries = initSeries(props.series, event?.selected)
    myChart.setOption({
      series: dataSeries
    })
  })
})
// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  let title = props?.title
  contentTip.value = titleTipObj[title] || ''
  // 所有柱状图legend靠右
  let legend = props?.legend || {}
  let reverseLegend = props?.reverseLegend
  let grid = props?.grid || {}
  // console.log(props?.series,legend,'series')
  // 所有柱状图legend都靠右
  let type = props?.series && props?.series[0]?.type ? props?.series[0]?.type : 'bar'
  if (type === 'bar') {
    //计算grid
    let { grid: _grid } = girtAndlenged(props)
    legend = {
      orient: 'vertical',
      bottom: '50',
      right: '4',
      ...legend
    }
    reverseLegend = true
    grid = _grid
  }
  const options = {
    color: props?.color || color,
    tooltip: {
      ...JSON.parse(JSON.stringify({ ...tooltip, ...props?.tooltip }))
    },
    legend: {
      type: 'scroll',
      bottom: '5px',
      right: '4px',
      itemWidth: 14,
      itemHeight: 8,
      itemGap: 4,
      formatter: function (name) {
        if (!props.legendWrap) return name
        const maxWidth = 40 // 最大允许宽度（单位：px）
        const fontSize = 12 // 字体大小
        const maxChars = Math.floor((maxWidth / fontSize) * 2) // 估算每行字符数

        let result = ''
        let currentLine = ''

        name.split('').forEach((char, index) => {
          currentLine += char
          if ((index + 1) % maxChars === 0) {
            result += currentLine + '\n'
            currentLine = ''
          }
        })

        if (currentLine) result += currentLine
        return result
      },
      textStyle: { fontSize: 10 },
      // data: props.series.map(v => v.name),
      data: (series => {
        if (props.legendDataDeal) {
          // 要处理datas数据
          if (reverseLegend) series = series.reverse()
          var sortSeries = []
          var hasOther = false
          if (!props.totalSortLegend) {
            for (var j in series) {
              if (series[j].name == '其他') {
                hasOther = true
              } else {
                sortSeries.push(series[j].name)
              }
            }
            if (hasOther) {
              if (reverseLegend) {
                sortSeries.unshift('其他')
              } else {
                sortSeries.push('其他')
              }
            }
          } else {
            sortSeries = getSortData(series, reverseLegend ? -1 : 1)
          }
          return sortSeries
        } else {
          // 按原本数据排序
          series = series.reverse()
          return series.map(v => v.name)
        }
      })(JSON.parse(JSON.stringify(props.series))),
      ...legend
    },
    grid: grid,
    xAxis: [
      {
        type: 'category',
        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          rotate: props.titleRotate ? 45 : 0,
          interval: props.xAxisInterval
        },
        axisTick: {
          show: true, //显示x轴刻度
          alignWithLabel: true //刻度线与标签对齐
        },

        axisLine: {
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        },
        data: getXAxisData(props.series),
        ...props?.xAxis
      }
    ],
    yAxis: [
      {
        name: props.yAxisName,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: props.yAxisLabelFormate
          ? {
              formatter: props.yAxisLabelFormate,
              color: '#44546A'
            }
          : {
              color: '#44546A'
            },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        },
        ...props?.yAxis
      }
    ],
    series: initSeries(props.series)
  }
  // console.log('props.series', props.series)
  if (props.yAxisMax !== '') {
    options.yAxis.forEach(el => {
      el.max = props.yAxisMax
    })
  }
  if (props.otherYAxis) {
    options.yAxis.push(...props.otherYAxis)
  }
  // tooltip添加总计
  // if (props.showTotal) {
  if (!props?.tooltip?.formatter) {
    options.tooltip.formatter = params => {
      return formatter(params, totalData, props.tooltipUnits, props, props.addTooltipTotalPercent)
    }
  } else {
    options.tooltip.formatter = props.tooltip.formatter
  }
  options.tooltip.position = props.tooltip.position
  myChart.setOption(options)
}

// 汇总排序数据
function getSortData(data, sortControl = 1) {
  // 计算各个数据总量
  let totalMap = {}
  // 遍历每个产品
  data.forEach(product => {
    // 遍历每个产品的数据项
    const obj = product.data[product.data.length - 1]
    // 如果总量映射中不存在该数据项的名称，则初始化为 0
    if (!totalMap[product.name]) {
      totalMap[product.name] = 0
    }
    // 累加该数据项的值到总量映射中
    totalMap[product.name] += Number(obj.value)
  })
  // console.log('totalMap', totalMap)
  // 根据总量排序公司名称,'其他' 项放在最后
  var sortedCompanies = Object.keys(totalMap).sort((a, b) => {
    // 如果 a 是 '其他'，则将其排在 b 前面
    if (a === '其他') {
      return -1
    }
    // 如果 b 是 '其他'，则将其排在 a 前面
    if (b === '其他') {
      return 1
    }
    // 根据 sortControl 控制排序方向
    return sortControl * (totalMap[b] - totalMap[a])
  })
  // console.log('sortedCompanies', sortedCompanies)
  // 返回排序处理后的数据数组
  return sortedCompanies
}
// 生成符合视图的数据
function initSeries(data, selected) {
  const itemColor = props.color
  const series = []
  let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  const allData = Array.from({ length: allDataLength }).map(() => '') // 总计
  let totalStackName = 'total'
  data.forEach((el, index) => {
    let colorTmp = colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length]

    //玉柴集团的图标显示阴影
    const itemStyle = {}
    const lineStyle = {}
    if (el.name && el.name.includes('玉柴')) {
      // itemStyle.shadowBlur = 6
      // // itemStyle.shadowColor = colorTmp
      // itemStyle.shadowOffsetX = 0
      // itemStyle.shadowOffsetY = 0
      // itemStyle.opacity = 1

      // lineStyle.shadowBlur = 6
      // // lineStyle.shadowColor = colorTmp
      // lineStyle.shadowOffsetX = 0
      // lineStyle.shadowOffsetY = 0
      // lineStyle.opacity = 1

      colorTmp = '#E72331'
    }
    const json = {
      symbol: 'circle',
      symbolSize: 6,
      name: el.name,
      type: 'bar',
      stack: 'total',
      ...el,
      barWidth: 30,
      
      itemStyle: {
        ...itemStyle,
        color: colorTmp,
        ...el?.itemStyle
      },
      lineStyle: {
        ...lineStyle
      },
      data: el.data,
      barWidth: '40%',
      barMaxWidth: 30,
      z: 1
    }

    if (props.sort !== '') json.sort = props.sort
    if (json.type === 'bar' && json.stack !== undefined) totalStackName = json.stack
    series.push(json)
  })

  filterData?.forEach((el, index) => {
    if (el.type === 'bar' || el.type === undefined) {
      el.data.forEach((v, vdx) => {
        if (v.value !== null && v.value !== undefined && v.value !== '') {
          if (allData[vdx] === '') allData[vdx] = 0
          allData[vdx] = allData[vdx] + Number(v.value)
        }
      })
    }
  })

  // 添加总计
  if (props.showTotal) {
    series.push({
      name: '总计',
      type: 'bar',
      stack: totalStackName,
      label: {
        show: true,
        position: 'top',
      
        formatter: function (p) {
          let _precision = props?.precision
          return allData[p.dataIndex] !== null &&
            allData[p.dataIndex] !== undefined &&
            allData[p.dataIndex] !== ''
            ? numFormat(allData[p.dataIndex], _precision)
            : ''
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: Array.from({ length: allDataLength }).map(() => 0)
    })
    totalData.value = allData
  }
  return series
}

function getXAxisData(value) {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => v.name)
}

onUnmounted(() => {
  // 取消监听legend事件
  if (legendListener) {
    myChart.off('legendselectchanged', legendListener)
  }
  // 清理图表资源
  myChart && myChart.dispose()
})

// /**
//  * 计算小数点位数
//  * @param num 数字
//  */
// function countDecimalPlaces(num) {
//   // 将数字转换为字符串
//   const numStr = num.toString()
//   // 查找小数点
//   const decimalIndex = numStr.indexOf('.')
//   // 如果没有小数点，返回0
//   if (decimalIndex === -1) {
//     return 0
//   }
//   // 返回小数点后的字符数
//   return numStr.length - decimalIndex - 1
// }
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.ratio-width {
  padding-bottom: 45%;
}
:deep(.el-card__body) {
  padding: 0 6px 6px !important;
}
:deep(.el-card__header) {
  padding: 0;
}
</style>
