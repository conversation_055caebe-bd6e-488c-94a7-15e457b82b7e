<template>
  <el-card>
    <div class="ratio-width bar-box" :style="{ paddingBottom: props.height }">
      <div ref="target" class="ratio-width__wrap"></div>
      <el-tooltip
        v-if="contentTip"
        :content="contentTip"
        popper-class="bar-box-tooltip"
        placement="top"
      >
        <el-icon :size="18" color="rgba(13, 41, 102, 0.9)" class="bar-box__warn"
          ><InfoFilled
        /></el-icon>
      </el-tooltip>
    </div>
  </el-card>
</template>

<script setup>
import * as echarts from 'echarts'
import echartsResize from '@/utils/hooks/echartsResize.js'
import {
  color,
  colorMap,
  formatter,
  tooltip,
  numFormat,
  titleTipObj,
  girtAndlenged
} from './config.js'

const props = defineProps({
  title: {
    // 是否展示标题
    type: String,
    required: false,
    default: ''
  },
  titleRotate: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  series: {
    // series数据
    type: Array,
    required: true,
    default: () => []
  },
  stack: {
    type: String,
    required: false,
    default: ''
  },
  yAxisName: {
    type: String,
    required: false,
    default: ''
  },
  yAxisMax: {
    type: [Number, String],
    required: false,
    default: ''
  },
  yAxisLabelFormate: {
    type: String,
    required: false,
    default: ''
  },
  color: {
    // 每条折线的颜色
    type: Array,
    required: false,
    default: () => color
  },
  grid: {
    // grid数据
    type: Object,
    required: false,
    default: () => [{ left: 56, bottom: 60, right: 46, top: 46 }]
  },
  legend: {
    type: Object,
    required: false,
    default: () => ({})
  },
  height: {
    // 是否展示标题
    type: String,
    required: false,
    default: '45%'
  },
  showTotal: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  sort: {
    type: String,
    required: false,
    default: ''
  },
  tooltipUnits: {
    type: String,
    required: false,
    default: ''
  },
  xAxisInterval: {
    type: [String, Number],
    required: false,
    default: 0
  },
  xAxis: {
    type: Object,
    required: false,
    default: () => ({})
  },
  yAxis: {
    type: Object,
    required: false,
    default: () => ({})
  },
  otherYAxis: {
    type: Array,
    required: false,
    default: () => []
  },

  tooltip: {
    type: Object,
    required: false,
    default: () => ({})
  },
  precision: {
    type: Number,
    required: false,
    default: 1
  },
  addTooltipTotalPercent: {
    type: Boolean,
    required: false,
    default: false
  },
  reverseLegend: {
    type: Boolean,
    required: false,
    default: false
  }
})

watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const target = ref(null)
const totalData = ref([]) // 柱形图总计值
const contentTip = ref('')

let legendListener = null

onMounted(() => {
  myChart = echarts.init(target.value)
  console.log(props.series, '999')
  renderEcharts()

  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)

  // 监听lenged的选择
  legendListener = myChart.on('legendselectchanged', event => {
    let dataSeries = initSeries(props.series, event?.selected)
    myChart.setOption({
      series: dataSeries
    })
  })
})
// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  let title = props?.title
  contentTip.value = titleTipObj[title] || ''
  // 所有柱状图legend靠右
  let legend = props?.legend || {}
  let reverseLegend = props?.reverseLegend
  let grid = props?.grid || [{}]
  // console.log(props?.series,legend,'series')
  // 所有柱状图legend都靠右
  let type = props?.series && props?.series[0]?.type ? props?.series[0]?.type : 'bar'
  if (type === 'bar') {
    //计算grid
    let { grid: _grid } = girtAndlenged(props)
    legend = {
      orient: 'vertical',
      bottom: '50',
      right: '4',
      itemHeight: '6',
      textStyle: { fontSize: 10 },
      ...legend
    }
    reverseLegend = true
    grid = _grid
  }
  console.log(grid, 'grid')
  console.log(props.series, 'props.series')

  const options = {
    title: {
      // text: '上下双柱状图',
    },
    // color: [
    //   'red',
    //   'green',
    //   '#fac858',
    //   '#ee6666',
    //   '#73c0de',
    //   '#3ba272',
    //   '#fc8452',
    //   '#9a60b4',
    //   '#ea7ccc'
    // ], // 每个数据项都使用不同的颜色
    tooltip: {
      show: true,
      trigger: 'axis',
      confine: true,
      extraCssText: 'z-index: 9999'
    },
    legend: [ // 图例配置
      {
        //  orient: 'horizontal',
        //   itemWidth: 80, // 每个图例项的宽度
        width: '90%',
        padding: [5, 10, 5, 10], // 上/右/下/左
        bottom: '10',
        left: 'center',
        type: 'scroll', // 'plain'：普通图例。缺省就是普通图例。 'scroll'：可滚动翻页的图例。当图例数量较多时可以使用。
        pageIconSize: 15, // 翻页按钮的大小
        pageButtonPosition: 'end', //'start'：控制块在左或上。'end'：控制块在右或下。
        pageIconInactiveColor: '#aaa', // 翻页按钮不激活时（即翻页到头时）的颜色。
        pageIconColor: '#222', // 翻页按钮的颜色
        textStyle: {
          color: '#777',
          fontSize: '12', // 文字的字体大小
        },
        selectedMode: true
      }
    ],
    grid: [
      {
        left: '1%',  // 调整左侧边距
        right: '1%', // 调整右侧边距
        show: false,
        top: '10%',
        bottom: '8%',
        containLabel: true,
        height: '37%'
      },
      {
        left: '1%',  // 调整左侧边距
        right: '1%', // 调整右侧边距
        show: false,
        bottom: '15%',
        containLabel: true,
        height: '37%'
      }
    ],
    xAxis: [
      {
        type: 'category',
        boundaryGap: true, // 开启边界间隙
        axisTick: {
            alignWithLabel: true // 刻度线和标签对齐
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        splitLine: {
          show: false
        },
        data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
      },
      {
        gridIndex: 1,
        type: 'category',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
      }
    ],
    yAxis: [
      {
        name: '单位(万台)',
        type: 'value'
      },
      {
        gridIndex: 1,
        type: 'value',
        inverse: true
      }
    ],
    series: [
      {
        name: '小米',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
        //   color: ['red', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'] // 如果系列没有设置颜色，则会依次循环从该列表中取颜色作为系列颜色。
      },
      {
        name: '网易',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '快手',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '美团',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '字节',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '百度',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '京东',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '腾讯',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '阿里',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '拼多多',
        type: 'bar',
        barWidth: 25,
        stack: '1',
        data: [11, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40],
        itemStyle: {
          //     color: '#F59A3F',
        }
      },
      {
        name: '小米1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '网易1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '快手1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '美团1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '字节1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '百度1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '京东1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '腾讯1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '阿里1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      },
      {
        name: '拼多多1',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        barWidth: 25,
        stack: '2',
        data: [10, 20, 30, 40, 50, 10, 20, 30, 40, 50, 30, 40]
      }
    ]
  }
  options.tooltip.formatter = params => {
    return formatterTooltip(params)
  }
  myChart.setOption(options)
}
// 自定义tooltip
function formatterTooltip(params) {
  // 自定义标题
  const customTitle = `${params[0].name}月`;
  let content = `<div style="font-weight: bold;">${customTitle}</div>`;
  params.forEach((param) => {
      // 获取系列的颜色，用于显示前面的图标
      const color = param.color;
      const seriesName = param.seriesName;
      const value = param.value;
      content += `<div><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>${seriesName}: ${value}</div>`;
  });
  return content;
}
// 生成符合视图的数据
function initSeries(data, selected) {
  // const itemColor = props.color
  // const series = []
  // let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  // const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  // const allData = Array.from({ length: allDataLength }).map(() => 0) // 总计
  // console.log(data, 'data')
  // data.forEach((el, index) => {
  //   let json = {}
  //   if (index % 2 == 0) {
  //     json = {
  //       symbol: '1',
  //       symbolSize: 1,
  //       name: el.name,
  //       type: 'bar',
  //       stack: '2',
  //       xAxisIndex: 1,
  //       yAxisIndex: 1,
  //       ...el,
  //       barWidth: 30,
  //       itemStyle: {
  //         color: colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length],
  //         ...el?.itemStyle
  //       },
  //       data: el.data,
  //       barWidth: '40%',
  //       z: 1
  //     }
  //   } else {
  //     json = {
  //       symbol: '1',
  //       symbolSize: 1,
  //       name: el.name,
  //       type: 'bar',
  //       stack: '1',
  //       ...el,
  //       barWidth: 30,
  //       itemStyle: {
  //         color: colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length],
  //         ...el?.itemStyle
  //       },
  //       data: el.data,
  //       barWidth: '40%',
  //       z: 1
  //     }
  //   }

  //   if (props.sort !== '') json.sort = props.sort
  //   series.push(json)
  // })
  // console.log(series, 'series')
  // filterData?.forEach((el, index) => {
  //   el.data.forEach((v, vdx) => {
  //     let _value = Number(v.value || 0)
  //     allData[vdx] = allData[vdx] + _value
  //   })
  // })

  // // 添加总计
  // if (props.showTotal) {
  //   series.push({
  //     name: '总计',
  //     type: 'bar',
  //     stack: 'total',
  //     label: {
  //       show: true,
  //       position: 'top',
  //       formatter: function (p) {
  //         let _precision = props?.precision
  //         return numFormat(allData[p.dataIndex], _precision)
  //       }
  //     },
  //     emphasis: {
  //       focus: 'series'
  //     },
  //     data: Array.from({ length: allDataLength }).map(() => 0)
  //   })
  //   totalData.value = allData
  // }
  // return series
}
function getTitle(tit) {
  let title = {}
  if (tit !== '' && tit !== undefined && tit !== null) {
    title = {
      text: tit, //tooltipTitle(tit),
      left: 'center',
      textStyle: {},
      textStyle: {
        fontSize: 16,
        fontWeight: 400,
        color: '#051C2C',
        rich: {
          a: {
            color: 'blue',
            fontSize: 16,
            fontWeight: 'bold',
            // 鼠标悬停显示的气泡样式
            textShadowBlur: 2,
            textShadowColor: '#666',
            textBorderColor: '#ddd',
            textBorderWidth: 2
          }
        }
      }
    }
  }
  return title
}
// 获取x轴数据
function getXAxisData(value) {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => v.name)
}

onUnmounted(() => {
  // 取消监听legend事件
  if (legendListener) {
    myChart.off('legendselectchanged', legendListener)
  }
  // 清理图表资源
  myChart && myChart.dispose()
})

// /**
//  * 计算小数点位数
//  * @param num 数字
//  */
// function countDecimalPlaces(num) {
//   // 将数字转换为字符串
//   const numStr = num.toString()
//   // 查找小数点
//   const decimalIndex = numStr.indexOf('.')
//   // 如果没有小数点，返回0
//   if (decimalIndex === -1) {
//     return 0
//   }
//   // 返回小数点后的字符数
//   return numStr.length - decimalIndex - 1
// }
</script>

<style lang="scss" scoped>
.ratio-width {
  padding-bottom: 45%;
}
:deep(.el-card__body) {
  padding: 6px !important;
}
</style>
