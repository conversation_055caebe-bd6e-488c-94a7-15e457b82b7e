import { reactive } from 'vue'
import * as echarts from 'echarts'
import { numberFormat as nf } from '@/utils/format'
export default function ({ props, myChartDom, renderEcharts, initSeries }) {
  const myChart = { value: null }
  let legendListener = null
  // 某些项强制颜色设置
  const symbolItemColor = {
    '其他': '#DDDDDD',
    '其它': '#DDDDDD'
  }
  const defaultOptions = reactive({
    color: [
      '#115E93',
      '#87AEC9',
      '#3A76FF',
      '#9CBAFF',
      '#00A9F4',
      '#7FD3F9',
      '#9BA4AB',
      '#CDD1D5',
      '#3BDBD6',
      '#9DEDEA',
      '#C280FF',
      '#E0BFFF',
      '#7248DB',
      '#B8A3ED',
      '#EE824B',
      '#F6C0A5',
      '#9B7A01'
    ],
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 0,
      textStyle: {
        //提示框自己的样式
        fontSize: 14,
        // color: '#fff'
        color: '#1D2129'
      },
      axisPointer: {
        label: {
          precision: 2,
          show: true,
          margin: 5,
          backgroundColor: '#0b1f56',
          color: '#fff',
          fontSize: 14
        }
      },
      position: function (point, params, dom, rect, size) {
        return [point[0], point[1] - size.contentSize[1] - 30]
      },
      formatter: function (params) {
        const precision = props.precision
        if (!params) return
        if (params.length > 0 && params.every(el => el.value === '')) return
        params.reverse()
        let itemsHtml = ''
        let totalUnit = ''
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName !== '总计') {
            // 获取单位  
            if (params[i].data.tooltipValue && totalUnit === '') {
              if (params[i].data.totalUnit) {
                totalUnit = params[i].data.totalUnit
              } else {
                const match = params[i].data.tooltipValue.match(/.*\d(.*)/)
                totalUnit = match ? match[1] : ''
              }
            }
            if (params[i].value === '' || params[i].value === null || params[i].value === undefined)
              continue
            itemsHtml += `<div class="charts-tooltip-item">
                  <span>${params[i].marker}${params[i].seriesName}&nbsp;&nbsp;</span>
                  <span> ${params[i].data.tooltipValue ?? params[i].value}</span>
                </div>`
          }
        }
        const barArray = params.filter(e => e.seriesType === 'bar')
        const totalSale = barArray.reduce((sum, x) => sum + (x?.data?.value ?? 0), 0)
        let totalHtml = `<div class="charts-tooltip-item">
        <span>总计&nbsp;&nbsp;</span> 
        <span>${numberFormat(totalSale, precision)}${totalUnit}</span>
        </div>`

        let tooltipHtml = `
            <div>
              <div> ${params && params[0] ? params[0].name : ''}</div>
              ${itemsHtml}${props.showTotal ? totalHtml : ''}
            </div>`
        return tooltipHtml
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      bottom: '5px',
      right: '4px',
      itemWidth: 14,
      itemHeight: 8,
      itemGap: 4,
      textStyle: { fontSize: 12 },
      formatter: function (name) {
        if (!props.legendWrap) return name
        const maxWidth = 40 // 最大允许宽度（单位：px）
        const fontSize = 12 // 字体大小
        const maxChars = Math.floor((maxWidth / fontSize) * 2) // 估算每行字符数

        let result = ''
        let currentLine = ''

        name.split('').forEach((char, index) => {
          currentLine += char
          if ((index + 1) % maxChars === 0) {
            result += currentLine + '\n'
            currentLine = ''
          }
        })

        if (currentLine) result += currentLine
        return result
      }
    },
    xAxis: [
      {
        type: 'category',
        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          rotate: 0,
          interval: 0
        },
        axisTick: {
          alignWithLabel: true,
          show: true //显示x轴刻度
        },
        axisLine: {
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        },
        nameTextStyle: { height: '220px' }
      }
    ],
    yAxis: [
      {
        name: '',
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          color: '#44546A'
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        splitLine: {
          show: false // 隐藏分割线
        },
        axisPointer: {
          label: {
            show: true,
            precision: 2
          }
        }
      }
    ]
  })
  const store = useStore()
  const opened = computed(() => store.state.biapp.sidebar.opened)
  watch(opened, async () => {
    setTimeout(() => {
      resizeHandler()
    }, 281)
  })
  function resizeHandler() {
    if (myChart.value) {
      myChart.value.resize({
        width: 'auto', // 宽度随着父容器变化而变化
        height: 'auto' // 高度随着父容器变化而变化
      })
    }
  }

  const numberFormat = (num, precision) => {
    return nf(num, precision)
  }
  onMounted(() => {
    myChart.value = echarts.init(myChartDom.value)
    renderEcharts()
    window.addEventListener('resize', resizeHandler)

    // 监听lenged的选择
    legendListener = myChart.value.on('legendselectchanged', event => {
      let dataSeries = initSeries(props.series, event?.selected)
      myChart.value.setOption({
        series: dataSeries
      })
    })
  })

  onUnmounted(() => {
    if (myChart.value) {
      window.removeEventListener('resize', resizeHandler)
      myChart.value && myChart.value.dispose()
    }
    // 取消监听legend事件
    if (legendListener) {
      myChart.value.off('legendselectchanged', legendListener)
    }
  })
  return {
    myChart,
    symbolItemColor,
    defaultOptions,
    resizeHandler,
    numberFormat
  }
}
