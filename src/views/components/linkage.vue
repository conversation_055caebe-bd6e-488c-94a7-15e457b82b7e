<template>
  <template v-for="(i, index) in prop.props" :key="i">
    <el-col
      v-if="!prop.props[index].hide"
      :span="prop?.span"
      :xs="prop.xs"
      :sm="prop.sm"
      :md="prop.md"
    >
      <el-form-item
        :label="prop.showLabel ? prop.props[index].name : ''"
        :prop="prop.props[index].key"
      >
        <el-select
          v-model="prop.form[prop.props[index].key]"
          :placeholder="prop.showLabel ? prop.props[index].placeholder : prop.props[index].name"
          :clearable="!prop.props[index].clearable"
          filterable
          :disabled="prop.props[index].disabled"
          @change="toggleLevel($event, index)"
          style="width: 100%"
        >
          <el-option
            v-for="item in dicts[`level${index}`]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </template>
</template>

<script setup>
// 级联选择

const prop = defineProps({
  dicts: {
    // 数据字典
    type: Array,
    required: true,
    default: () => []
  },
  props: {
    // 表单key值默认首页新能源市场key
    type: Array,
    required: false,
    default: () => [
      {
        name: '信息模块',
        placeholder: '请输入信息模块',
        key: 'dictModuleId',
        hide: false
      },
      {
        name: '左侧菜单',
        placeholder: '请输入左侧菜单',
        key: 'dictLeftMenuId',
        hide: false
      },
      {
        name: '新闻种类',
        placeholder: '请输入新闻种类',
        key: 'dictNewsId',
        hide: false
      }
    ]
  },
  form: {
    // 搜索表单form
    type: Object,
    required: true,
    default: () => ({})
  },
  span: {
    type: [Number, String],
    required: false,
    default: 24
  },
  showLabel: {
    // 数据字典
    type: Boolean,
    required: false,
    default: true
  },
  xs: {
    type: Number,
    required: false,
    default: 8
  },
  sm: {
    type: Number,
    required: false,
    default: 8
  },
  md: {
    type: Number,
    required: false,
    default: 4
  }
})

const dicts = reactive({})

watch(
  () => prop.dicts,
  () => {
    initData()
  },
  {
    deep: true
  }
)

watch(
  () => prop.form,
  (current, prev) => {
    const arr = prop.props.map(v => v.key)
    for (let i = 0; i < arr.length; i++) {
      if (current[arr[i]] !== prev[arr[i]]) {
        setDefaultData()
        break
      }
    }
  },
  {
    deep: true
  }
)

/**
 * 初始化每级联动存储数据的空间
 */
function initDictsLevelData() {
  const linkageLevel = prop.props.length // 几级联动
  for (let i = 0; i < linkageLevel; i++) {
    if (i === 0) {
      dicts[`level${i}`] = prop.dicts
    } else {
      dicts[`level${i}`] = []
    }
  }
}
function toggleLevel(event, level) {
  const linkageLevel = prop.props.length // 几级联动
  if (event === undefined) {
    prop.form[prop.props[level].key] = ''
    let flag = level + 1
    while (flag < linkageLevel) {
      dicts[`level${flag}`] = []
      prop.form[prop.props[flag].key] = ''
      flag++
    }
    return
  }
  let flag = level + 1
  while (flag < linkageLevel) {
    prop.form[prop.props[flag].key] = ''
    flag++
  }
  const select = dicts[`level${level}`]

  const index = select.findIndex(i => i.value === event)
  dicts[`level${level + 1}`] = select[index]?.children ?? []
}

const setDefaultData = () => {
  const form = JSON.parse(JSON.stringify(toRaw(prop.form)))
  if (prop.props && prop.props[0] && prop.props[0].key && form && form[prop.props[0].key]) {
    toggleLevel(form[prop.props[0].key], 0)
  }
  if (prop.props && prop.props[1] && prop.props[1].key && form && form[prop.props[1].key]) {
    prop.form[prop.props[1].key] = form[prop.props[1].key]
    toggleLevel(form[prop.props[1].key], 1)
  }
  if (prop.props && prop.props[2] && prop.props[2].key && form && form[prop.props[2].key]) {
    prop.form[prop.props[2].key] = form[prop.props[2].key]
    toggleLevel(form[prop.props[2].key], 2)
  }
}
// 初始化
const initData = () => {
  initDictsLevelData()
  setDefaultData()
}
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped></style>
