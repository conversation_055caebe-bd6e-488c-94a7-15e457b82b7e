<template>
    <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        style="height: 200px; overflow-y: hidden;"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
      />
    </div>
</template>
<script setup>
  import '@wangeditor/editor/dist/css/style.css' // 引入 css
  import { onBeforeUnmount, ref, shallowRef, onMounted } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

   const props= defineProps({
      content: {
        type: String,
        default: ''
      }
    })
  // 编辑器实例，必须用 shallowRef
      const editorRef = shallowRef()
    //   const valueHtml = ref('')
      // 内容 HTML
      const valueHtml = defineModel()
      const mode = ref('default')
      
      // 模拟 ajax 异步获取内容
      onMounted(() => {
        // setTimeout(() => {
        //   editorRef.value.setHtml('<p>请输入内容...</p>')
        // },500)
      })

      const toolbarConfig = {}
      const editorConfig = { placeholder: '请输入内容...',readOnly:true }

      // 组件销毁时，也及时销毁编辑器
      onBeforeUnmount(() => {
        const editor = editorRef.value
        if (editor == null) return
        editor.destroy()
      })

      const handleCreated = (editor) => {
        editorRef.value = editor // 记录 editor 实例，重要！
      }
</script>