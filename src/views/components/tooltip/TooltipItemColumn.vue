<template>
  <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
    <div v-if="Array.isArray(items)" v-for="(item, index) in items" :key="prefix + '-' + index" class="tipItems">
      <div v-if="!(item.seriesType === 'line' && !showLineValues)" class="tipItem">
        <div>
          <span :style="{ backgroundColor: item?.color }" class="rang"></span>
          {{ item?.seriesName }}
        </div>
        <div class="tipItemRight">
          <slot name="item" :item="item">
            <span>{{ item.value || 0 }}{{ item.seriesType === 'line' ? '%' : '台' }} </span>
          </slot>
        </div>
      </div>
    </div>
    <div v-else class="tipItems">
      <div v-if="!(items.seriesType === 'line' && !showLineValues)" class="tipItem">
        <div>
          <span :style="{ backgroundColor: items?.color }" class="rang"></span>
          {{ items?.name }}
        </div>
        <div class="tipItemRight">
          <slot name="item" :item="items">
            <span>{{ items.value || 0 }}{{ items.seriesType === 'line' ? '%' : '台' }} </span>
          </slot>
        </div>
      </div>
    </div>
  </el-col>
</template>

<script setup>
import { ElCol } from "element-plus";

const props = defineProps({
  items: {
    type: Array || Object,
    required: true
  },
  prefix: {
    type: String,
    required: true
  },
  params: {
    type: Array,
    default: () => []
  },
  showLineValues: {
    type: Boolean,
    default: true
  }
})
</script>


<style scoped>
.tipItems {
  min-width: 280px;
  max-width: 450px;
}

.rang {
  display: inline-block;
  margin-right: 4px;
  border-radius: 10px;
  width: 10px;
  height: 10px;
}
</style>