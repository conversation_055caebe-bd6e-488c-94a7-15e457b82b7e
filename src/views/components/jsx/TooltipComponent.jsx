import Tpis from '@/views/components/tooltip/index.vue'
import { numberFormat } from '@/utils/format'

// Tooltip组件渲染
export const TooltipComponent = propos => {
  let params = propos.params
  params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos} params={params}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {numberFormat(item.value, item.seriesType == 'line' ? 1 : 0) ||  '0' }
                {item.seriesType == 'line' ? '%' : '台'}
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

// Tooltip组件渲染
export const TooltipPercentageComponent = propos => {
  propos.params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{numberFormat(item.value, 1) || 0}%</span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

// Tooltip组件渲染 销量与占比
export const TooltipSalesAndPercentageComponent = propos => {
  let prop = propos
  let params = propos.params
  // 将所有包含"其他"的项移到最后
  const others = params.filter(item => item.seriesName && item.seriesName.includes('其它'))
  const notOthers = params.filter(item => !(item.seriesName && item.seriesName.includes('其它')))
  params = [...notOthers, ...others]
  prop.params = params
  console.log('代码内容', params)

  return (
    <Tpis {...prop}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {numberFormat(item.value, 0) || 0} | {numberFormat(item.data.proportion, 1) || 0}%
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

// Tooltip组件渲染销量，占比，同比
export const TooltipSalesProportionYoYComponent = propos => {
  propos.params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {/* {numberFormat(item.value, item.seriesType == 'line' ? 1 : 0) || 0}
                {item.seriesType == 'line' ? '%' : '台'} */}
                {item.data.sales} | {item.data.proportion} | {item.data.yoy}
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

export const TooltipJgqsPercentageComponent = propos => {
  debugger
  let prop = propos
  let params = propos.params
  let result = params
  // 排序倒序
  result = result.sort.call(result, (a, b) => {
    return b.value - a.value
  })
  if (params && params.length >= 6) {
    result = params.slice(0, 6)
  }
  params = result

  params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...prop} params={params}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{numberFormat(item.value, 1) || 0}%</span>
            </>
          )
        }
      }}
    </Tpis>
  )
}



// 同比渲染出现同期的时候数值使用 - 代替
export const TooltipComponentYoy = propos => {
  let params = propos.params
  params.sort((a, b) => {
      // 首先按 seriesType 排序，line 类型的放后面
    if (a.seriesType !== b.seriesType) {
      return a.seriesType == 'line' ? 1 : -1
    }
    
    // 如果都是 bar 类型，则按 seriesName 中的年份倒序排序
    const yearA = a.seriesName.match(/\d{4}/)
    const yearB = b.seriesName.match(/\d{4}/)
    
    if (yearA && yearB) {
      return parseInt(yearB[0]) - parseInt(yearA[0]) // 年份倒序排序
    }
  
    // 如果其中一个没有年份信息，保持原有顺序
    return 0
  })
  // console.log(params)
  // params seriesName 
  

  return (
    <Tpis {...propos} params={params}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {numberFormat(item.value, item.seriesType == 'line' ? 1 : 0) || '-'}
                {item.seriesType == 'line' ? '%' : propos?.unit || '台'}
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}