<template>
  <div class="block-title">
    <img :src="comIcon" class="block-title__icon" />
    <div class="block-title__text">{{ props.title }}</div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'data1'
  }
})
const comIcon  = computed(()=>{
  // src/../../
  const temp = new URL(`../../assets/images/${props.icon}.png`, import.meta.url).href
  // const res = temp.replace('title-icon',props.icon)
  return temp
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.block-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 50px;
  padding: 0;
  font-weight: bold;
  font-size: 16px;
  background: linear-gradient(180deg, #c6dff7 0%, #e8f2fc 100%);
  color: var(--el-text-color-regular);
  &__icon {
    display: block;
    width: 50px;
    height: 50px;
  }
}
</style>
