<template>
  <BiContent>
    <template #searchArea>

      <Search @searchChart="getEchart" />
    </template>
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover" v-loading="loading">
          <template #header>
            <block-title :title="title" />
          </template>
          <Charts :echartOption="echartOption" :echartOption2="echartOption2" :showTwo="showTwo" />
        </el-card>
      </el-col>
    </el-row>
  </BiContent>
</template>

<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import BiContent from '@/views/components/tabs/BiContent/index.vue'
import { ref, defineProps, nextTick } from 'vue'
import Search from './components/search/index'
import Charts from './components/charts/index'
import * as competitor from '@/api/common/common'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance()
import { color } from '@/views/components/echarts/config'

const props = defineProps({
  //模块关键词
  moduleKey: {
    type: String,
    required: false,
    default: ''
  }
})
const loading = ref(false)
const currentRoute = useRoute()
const engineMap = {
  shangyongche: '商用车',
  tongji: '通机',
  chuandian: '船电',
  newPower: '新能源'
}
/** 图表查询 */
const showTwo = ref(false)
const echartOption = ref({})
const echartOption2 = ref({})
const title = ref('暂无数据') // 图表标题
let dataSourceName = ''
let curKeyName = ''
let topM = ''
const engineFactoryName = ref(currentRoute?.meta?.title || '') // 厂家名称

const renderEchartDate = async response => {
  await nextTick()
  const data1 = ref([]) // 主机厂销量
  const data2 = ref([]) // 发动机销量
  const data3 = ref([]) // 厂家
  const data4 = ref([]) // 发动机厂销量占比
  // const title = ref([]) // 图表标题
  if (response.data.length > 0) {
    response.data.sort((a, b) => {
      return b.engineManufacturerSales - a.engineManufacturerSales
    })

    const idx = response.data.findIndex(el => el.manuFacturer === '其他')
    if (idx !== -1) {
      const [item] = response.data.splice(idx, 1) // 删除目标项
      response.data.push(item) // 追加到末尾
    }
    response.data.forEach(item => {
      data1.value.unshift(item.manuFacturerSales)
      data2.value.unshift(item.engineManufacturerSales)
      const manufacturer = item.manufacturer ? item.manufacturer : item.manuFacturer
      data3.value.unshift(manufacturer)
      data4.value.unshift(item.slice)
    })
    title.value = `${engineFactoryName.value}${engineMap[curKeyName]}TOP${topM}客户及份额情况(台)-${dataSourceName}`
  } else {
    data1.value = []
    data2.value = []
    data3.value = []
    data4.value = []
    title.value = '暂无数据'
  }
  //玉柴集团的图标显示阴影
  const itemStyle = {}
  const lineStyle = {}
  let colorTmp = null
  if (engineFactoryName.value.includes('玉柴')) {
    // itemStyle.shadowBlur = 6
    // // itemStyle.shadowColor = colorTmp
    // itemStyle.shadowOffsetX = 0
    // itemStyle.shadowOffsetY = 0
    // itemStyle.opacity = 1

    // lineStyle.shadowBlur = 6
    // // lineStyle.shadowColor = colorTmp
    // lineStyle.shadowOffsetX = 0
    // lineStyle.shadowOffsetY = 0
    // lineStyle.opacity = 1

    colorTmp = '#E72331'
  }
  echartOption.value = {
    color,
    /* title: {
      text: title.value,
      left: 'center'
    }, */
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
      // formatter: function (params) {
      //   let text = `
      //     <div style="text-align:left">
      //       ${params[0].marker} ${params[0].seriesName}:${params[0].value} <br/>
      //       ${params[1].marker} ${params[1].seriesName}:${((params[1].value / params[0].value) * 100).toFixed(2)}%
      //     </div>
      //   `
      //   return text
      // }
    },
    legend: {
      right: '0',
      bottom: '45px',
      orient: 'vertical'
    },
    grid: {
      left: '3%',
      right: '100px',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      nameGap: 20,
      axisLabel: {
        show: true // 显示坐标轴标签
      },
      axisLine: {
        show: true // 显示坐标轴线
      },
      axisTick: {
        alignWithLabel: true,
      }
    },
    yAxis: {
      type: 'category',
      data: data3.value,
      name: '厂家',
      axisLabel: {
        show: true // 显示坐标轴标签
      },
      axisLine: {
        show: true // 显示坐标轴线
      },
      axisTick: {
        alignWithLabel: true,
      }
    },
    series: [
      {
        name: '主机厂销量',
        type: 'bar',
        data: data1.value,
        itemStyle: {
          // color: 'rgb(68,84,106)',
          borderRadius: [0, 2, 2, 0]
        },
        label: {
          show: true, // 显示值
          position: 'right' // 值的位置，可以是 'top', 'left', 'right', 'bottom', 'inside', 或者 'insideLeft'
          // 可以通过 formatter 自定义显示格式
        }
      },
      {
        name: '发动机销量',
        type: 'bar',
        data: data2.value,
        itemStyle: {
          ...itemStyle,
          color: colorTmp || 'auto',
          borderRadius: [0, 2, 2, 0]
        },
        lineStyle: {
          ...lineStyle
        },
        label: {
          show: true, // 显示值
          position: 'right', // 值的位置，可以是 'top', 'left', 'right', 'bottom', 'inside', 或者 'insideLeft'
          // 可以通过 formatter 自定义显示格式
          formatter: function (params) {
            // params 是形如 {name: '示例', value: 123} 的对象
            let index = params.dataIndex
            let percentage = data4.value[index]
            return `${engineFactoryName.value},${percentage}`
          }
        },
        barGap: '-40%' /*重叠40%*/
      }
    ]
  }
}
const getEchart = (echartParams, defaultName, curKey) => {
  dataSourceName = defaultName
  curKeyName = curKey
  topM = echartParams.topM || '10'
  engineFactoryName.value = echartParams?.engineFactory
  switch (props.moduleKey) {
    case 'competitor':
      if (currentRoute.path.includes('shangyongche') || currentRoute.path.includes('tongji')) {
        loading.value = true
        competitor
          .getCompetitorList(echartParams)
          .then(response => {
            if (response.code === 200) {
              renderEchartDate(response)
            } else {
              proxy.$modal.msgSuccess(response.msg)
            }
          })
          .finally(() => {
            loading.value = false
          })
      } else if (currentRoute.path.includes('newPower')) {
        loading.value = true
        competitor
          .getCompetitorNewEnergyList(echartParams)
          .then(response => {
            if (response.code === 200) {
              renderEchartDate(response)
            } else {
              proxy.$modal.msgSuccess(response.msg)
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
    // case 'competitor':
    default:
      break
  }
  return
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

/* :deep(.el-card) {
  width: inherit;
  height: calc(100% - 106px);
  } */
.ratio-width {
  padding-bottom: 45%;
}

:deep(.el-card__body) {
  padding: 0 6px 6px !important;
}

:deep(.el-card__header) {
  padding: 0;
}
</style>
