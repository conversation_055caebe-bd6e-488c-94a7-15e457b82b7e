<template>
  <div class="ratio-width bar-box" ref="barBoxRef">
    <div v-if="!showTwo">
      <div class="ratio-width__wrap chart-box" ref="chart"></div>
    </div>
    <div v-else>
      <div ref="chart" style="width: 40%; height: 400px; float: left"></div>
      <div ref="chart2" style="width: 40%; height: 400px; float: left; margin-left: 10%"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, defineExpose } from 'vue'
import * as echarts from 'echarts'
const props = defineProps({
  echartOption: {
    type: Object,
    required: true
  },
  echartOption2: {
    type: Object,
    required: true
  },
  showTwo: {
    type: Boolean,
    required: true
  }
})
const chart = ref(null)
const chart2 = ref(null)
const barBoxRef = ref(null)
onMounted(() => {})
onUpdated(() => {
  const myChart = echarts.init(chart.value)
  var option = props.echartOption
  option && myChart.setOption(option)

  var sta = props.showTwo
  if (sta) {
    const myChart2 = echarts.init(chart2.value)
    var option2 = props.echartOption2
    option2 && myChart2.setOption(option2)
  }

  if(window.ResizeObserver) {
    // 使用 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      console.log('ResizeObserver: ')
      myChart.resize({
        width: 'auto', // 宽度随着父容器变化而变化
        height: 'auto' // 高度随着父容器变化而变化
      })
    })
  
    // 开始监听容器大小变化
    resizeObserver.observe(barBoxRef.value)
  }else{
    window.addEventListener('resize', () => {
      console.log('onresize: ')
      myChart.resize({
        width: 'auto', // 宽度随着父容器变化而变化
        height: 'auto' // 高度随着父容器变化而变化
      })
    })
  }
})
</script>

<style lang="scss" scoped>
.bar-box {
  width: 100%;
  padding-bottom: 33%;
}
.ratio-width {
  padding-bottom: 45%;
}

.chart-box {
  // width: calc(100vw - 350px);
  // height: calc(100vh - 400px);
  // position: absolute;
  // top: 0;
  // left: 0;
  // bottom: 0;
  // right: 0;
}
</style>
