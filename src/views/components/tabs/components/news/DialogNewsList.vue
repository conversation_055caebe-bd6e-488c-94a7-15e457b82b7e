<template>
  <el-dialog
    append-to-body
    v-model="dialogTableVisible"
    title="新闻列表"
    width="80%"
    :destroy-on-close="true"
  >
    <el-tabs v-model="tabsActive">
      <el-tab-pane v-for="i in tabs" :label="i.label" :name="i.value">
        <el-table
          :data="i.list"
          class="table-box"
          height="600px"
          @row-click="toggleList"
          style="width: 100%"
        >
          <el-table-column prop="title" />
          <el-table-column prop="time" width="150" />
        </el-table>
        <BiPagination
          :total="data.total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
const tabsData = [
  {
    label: '宏观动态',
    value: '0',
    list: []
  },
  {
    label: '行业趋势与政策法规',
    value: '1',
    list: []
  },
  {
    label: '战略合作',
    value: '2',
    list: []
  },
  {
    label: '经营动态',
    value: '3',
    list: []
  },
  {
    label: '高管动态',
    value: '4',
    list: []
  },
  {
    label: '人事变动',
    value: '5',
    list: []
  },
  {
    label: '市场活动与订单',
    value: '6',
    list: []
  },
  {
    label: '供货价格及商务政策',
    value: '7',
    list: []
  },
  {
    label: '终端返利与服务政策',
    value: '8',
    list: []
  },
  {
    label: '产品与技术',
    value: '9',
    list: []
  },
  {
    label: '产品市场表现',
    value: '10',
    list: []
  },
  {
    label: '四化动态',
    value: '11',
    list: []
  }
]
const json = {
  title: '公开新闻很多文字很多文字很多文字很多文字',
  read: '21',
  unread: '123',
  time: '2024-09-11 12:00',
  check: 'y'
}
tabsData.forEach((item, index) => {
  for (let i = 0; i < 20; i++) {
    item.list.push({
      ...json,
      title: `${json.title}-${json.check}-${item.label}-${index}${i}`
    })
  }
})
const data = reactive({
  total: 20,
  queryParams: {
    pageNum: 1,
    pageSize: 10
  },
  tabsActive: '0',
  tabs: tabsData
})
const { queryParams, tabs, tabsActive } = toRefs(data)

function getList() {}
const emit = defineEmits(['handleCurrentQuery2'])
const dialogTableVisible = ref(false)

const toggleList = ev => {
  console.log(ev)
  // router.push("/home/<USER>");
  dialogTableVisible.value = true
  emit('handleCurrentQuery2')
}
/** 详情按钮操作 */
function show(row) {
  dialogTableVisible.value = true
}
defineExpose({
  show
})
</script>

<style lang="scss" scoped>
// el-tabs 重写
:deep(.el-tabs) {
  height: 100%;
  .el-tab-pane {
    height: 100%;
  }
}
:deep(.el-tabs__header) {
  margin: 0;
}
:deep(.el-tabs--border-card) {
  & > .el-tabs__content {
    padding: 0 !important;
  }
}
.table-box {
  padding-top: 12px;
  :deep(.el-table__header-wrapper) {
    display: none;
  }
  :deep(.cell) {
    cursor: pointer;
  }
}
.on-top {
  z-index: 2000 !important; /* 设置一个足够高的z-index值 */
}
</style>
