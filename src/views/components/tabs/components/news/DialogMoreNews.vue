<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body>
    <span v-html="adata"></span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
let visible = ref(false)
let title = ref('')
let adata = ref('')

/** 详情按钮操作 */

const show = row => {
  visible.value = true
  title.value = row.title
  adata.value = row.html
}
/** 取消按钮 */
const cancel = () => {
  visible.value = false
}

defineExpose({
  show
})
</script>
<style lang="scss" scoped>
.texts {
  text-indent: 20px;
  line-height: 20px;
}
.titles {
  text-align: center;
}
</style>
