<template>
  <el-form label-width="0" :inline="true" class="tabs-form">
    <el-row :gutter="16" style="margin-right: unset">
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item>
          <el-date-picker
            style="width: 100%"
            value-format="YYYY"
            v-model="data.queryParams.year"
            type="year"
            placeholder="年份"
            :disabled-date="disabledFeatureDate"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item>
          <el-select
            v-model="data.queryParams.pointerType"
            placeholder="指标类型"
            @change="pointerTypeChange"
          >
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item v-if="data.queryParams.pointerType === '0'">
          <el-select v-model="data.queryParams.month" placeholder="月份">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="data.queryParams.pointerType === '2'">
          <el-select v-model="data.queryParams.month" placeholder="月累">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else>
          <el-select v-model="data.queryParams.quarter" placeholder="季度">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="data.queryParams"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            // hide: true,
            disabled: data.disabledDataSource,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment',
            disabled: true
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            key: 'subMarket2',
            hide: !defaultShowItem[data.curPage].subMarket2,
            disabled: false
          }
        ]"
        :propsEngineFactory="{
          name: '发动机厂',
          key: 'engineFactory',
          show: defaultShowItem[data.curPage].engineFactory
        }"
        :propsManuFacturer="{
          name: '主机厂',
          key: 'manuFacturer',
          show: defaultShowItem[data.curPage].manuFacturer
        }"
        :propsFuelType="{
          name: '燃料',
          key: 'fuelType',
          show: defaultShowItem[data.curPage].fuelType,
          type: 'B'
        }"
        :propsBreed="{
          name: '品系',
          key: 'breed',
          show: defaultShowItem[data.curPage].breed,
          disabled: data.disabledBreed
        }"
        :propsWeightMidLight="{
          name: '重中轻',
          key: 'weightMidLight',
          show: defaultShowItem[data.curPage].weightMidLight,
          disabled: false
        }"
         :propsDataType="{
          name: '数据扩展',
          key: 'dataType',
          show: true
        }"
        :xs="8"
        :sm="8"
        :md="3"
      />
      <!-- <el-col v-if="data.queryParams.dataSource === '1'" :xs="8" :sm="8" :md="3">
        <el-form-item prop="dataType">
          <el-select
            v-model="data.queryParams.dataType"
            multiple
            placeholder="数据扩展"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictDataType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col> -->
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item>
          <el-input min="2" type="number" v-model="data.queryParams.topM" placeholder="排名">
            <template #append>排名</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'

import { dictsPointerType, dictDataType } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'

const store = useStore()
const route = useRoute()
const emit = defineEmits(['searchChart'])

// const engineFactoryName = route?.meta?.title
const path = route?.path
const curKey = path.split('/')[2] // 获取路由地址关键字
const curLastKey = path.split('/')[3].replace(/\d/g, '') // 获取路由地址最后关键字
console.log('curLastKey', curLastKey)
const currentMonth = (new Date().getMonth() + 1).toString() // 月
const currentQuarter = getCurrentQuarter().toString() // 季度

// 不同页面默认值 需要将细分市场2 换成重中轻的，只有上险，货运，流向
const filterObj = {
  shangyongche: ['货运新增数', '上险数'],
  tongji: ['装机数'],
  chuandian: ['船电数'],
  newPower: ['上险数']
}
// 不同页面搜索框默认值
const defaultParams = {
  shangyongche: {
    segment: '商用车',
    subMarket1: '卡车'
  },
  tongji: {
    segment: '通机'
  },
  chuandian: {
    segment: '船电'
  },
  newPower: {
    segment: '商用车'
  }
}
const data = reactive({
  curPage: curKey,
  queryParams: {
    year: '2024', // 年份
    month: '12', // 月份
    quarter: '', // 季度
    // startDate: '',
    // endDate: '',
    pointerType: '2', // 指标类型
    dataSource: '', // 数据来源
    segment: '', // 板块
    subMarket1: '', // 细分市场1
    subMarket2: '', //细分市场2
    weightMidLight: '',
    engineFactory: route?.meta?.title ?? '', // 发动机厂家
    fuelType: '', // 燃料
    dataType: [],
    topM: '10' // 默认为前10排名
  },
  linkageData: [], // 多级联动数据
  disabledDataSource: false,
  disabledSubMarket2: false,
  disabledBreed: false
})
// 搜索条件是否显示 换成重中轻的，只有上险，货运，流向
const defaultShowItem = ref({
  shangyongche: {
    engineFactory: true,
    manuFacturer: false,
    fuelType: true,
    breed: true,
    subMarket2: false,
    weightMidLight: true
  },
  tongji: {
    engineFactory: true,
    manuFacturer: false,
    fuelType: false,
    breed: false,
    subMarket2: true,
    weightMidLight: false
  },
  chuandian: {
    engineFactory: true,
    manuFacturer: false,
    fuelType: false,
    breed: false,
    subMarket2: false,
    weightMidLight: false
  },
  newPower: {
    engineFactory: true,
    manuFacturer: false,
    fuelType: false,
    breed: true,
    subMarket2: false,
    weightMidLight: true
  }
})
// 设置不同页面默认搜索值
data.queryParams.dataSource = store.state.dicts.dictsDataSource.find(
  v => v.label === filterObj[curKey][0]
)?.value
Object.assign(data.queryParams, defaultParams[curKey])
data.disabledDataSource = filterObj[curKey].length === 1

// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(data.queryParams, onSearch)

// 指标类型联动
watch(
  () => data.queryParams.pointerType,
  val => {
    // // 指标类型(0-月，2-月累，1-季度)
    // if (val === '0') {
    //   data.queryParams.month = currentMonth
    //   data.queryParams.quarter = ''
    // } else if (val === '2') {
    //   data.queryParams.month = currentMonth === '1' ? '2' : currentMonth
    //   data.queryParams.quarter = ''
    // } else if (val === '1') {
    //   data.queryParams.month = ''
    //   data.queryParams.quarter = currentQuarter
    // }
    innerdate()
  }
)
// 监听年份变化
watch(
  () => data.queryParams.year,
  val => {
    innerdate()
  }
)
watch(
  () => data.queryParams.dataSource,
  val => {
    console.log('val', val)
    if (val === '1') {
      // 上险数
      initDateRange('上险数')
    } else if (val === '2') {
      // 装机数
      initDateRange('装机数')
    } else if (val === '6') {
      // 货运新增数
      initDateRange('货运新增数')
    } else if (val === '7') {
      // 船电数
      initDateRange('船电数')
    }

    if (val !== '1') {
      data.queryParams.dataType = []
    }
    if (val === '1') {
      // 上险数
      data.queryParams.segment = '商用车'
      data.queryParams.subMarket1 = '卡车'
    } else if (val === '6') {
      // 货运新增数
      data.queryParams.segment = '商用车'
      data.queryParams.subMarket1 = '卡车'
    }

    // val 1-上险数 6-货运新增数
    if (curKey === 'shangyongche' && curLastKey === 'yuchai') {
      data.queryParams.engineFactory = '玉柴'
    } else if (curKey === 'shangyongche' && curLastKey === 'weichai') {
      data.queryParams.engineFactory = '潍柴'
    } else if (curKey === 'shangyongche' && curLastKey === 'kangmingsi') {
      data.queryParams.engineFactory = '康明斯'
    } else if (curKey === 'shangyongche' && curLastKey === 'yunnei') {
      data.queryParams.engineFactory = '云内'
    }
  }
)

watch([() => data.queryParams.subMarket2, () => data.queryParams.breed], val => {
  if (val[0] && !val[1]) {
    data.disabledSubMarket2 = false
    data.disabledBreed = true
  } else if (!val[0] && val[1]) {
    data.disabledSubMarket2 = true
    data.disabledBreed = false
  } else {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
  }
})

watch(
  () => data.queryParams.segment,
  val => {
    if (curLastKey === 'yuchai') {
      data.queryParams.engineFactory = '玉柴'
    } else if (curLastKey === 'weichai') {
      data.queryParams.engineFactory = '潍柴'
    } else if (curLastKey === 'kangmingsi') {
      data.queryParams.engineFactory = '康明斯'
    } else if (curLastKey === 'yunnei') {
      data.queryParams.engineFactory = '云内'
    }
    console.log('curKey', curKey, curLastKey, val)
  }
)

// 切换指标类型
const pointerTypeChange = () => {
  data.queryParams.month = ''
  data.queryParams.quarter = ''
  innerdate()
}

function onSearch() {
  const labelName = store.state.dicts.dictsDataSource.find(
    v => v.value === data.queryParams.dataSource
  )?.label
  const queryParams = JSON.parse(JSON.stringify(data.queryParams))
  // 处理 dataType 参数（兼容单选和多选）
  queryParams.dataType = Array.isArray(queryParams.dataType) ? queryParams.dataType.join() : (queryParams.dataType || '')

  emit('searchChart', queryParams, labelName, curKey)
}

function getCurrentQuarter() {
  const now = new Date()
  const month = now.getMonth() // 0-11
  return Math.floor(month / 3) + 1 // 1-4
}

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: filterObj[curKey]
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}

onMounted(() => {
  initDateRange(
    store.state.dicts.dictsDataSource.find(v => v.value === data.queryParams.dataSource)?.label,
    true
  )
})
getDictsData()
</script>

<style lang="scss" scoped>
.search-form {
  padding: 0;
}
</style>
