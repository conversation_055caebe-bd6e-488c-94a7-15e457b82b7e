<template>
  <!-- 组件主体 -->
  <el-card shadow="hover" v-loading="loading">
    <template #header>
      <div class="card-header">
        <div class="search-box">
          <el-row :gutter="16" style="margin-right: 0px">
            <el-col :span="10" style="margin-bottom: 5px">
              <el-date-picker
                v-model="dataRange"
                type="daterange"
                range-separator="-"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 100%"
                clearable
                :disabledDate="disabledDate"
              />
            </el-col>
            <el-col :span="10">
              <el-input
                v-model="queryParams.searchKey"
                :suffix-icon="Search"
                placeholder="请输入关键字"
                clearable
                style="width: 100%"
              />
            </el-col>
            <el-col :span="4">
              <el-button
                class="btn"
                :disabled="tabsData.length === 0"
                type="primary"
                @click="onSearch"
              >
                <span>搜索</span>
              </el-button>
              <!-- <el-button type="primary" color="#115E93" @click="onReset" style="width: 60px">
                重置
              </el-button> -->
            </el-col>
          </el-row>
        </div>
        <div class="flex" ref="tabs">
          <el-tabs
            class="tabMain"
            :style="dynamicStyle"
            v-model="activeName"
            @tab-click="handleClick"
          >
            <el-tab-pane
              v-for="item in tabsData"
              :key="item.id"
              :label="item.newsSection"
              :name="item.id"
            >
            </el-tab-pane>
          </el-tabs>
          <!-- <div class="more" @click="lookMore">
              <span>查看更多</span>
              <el-icon><ArrowRightBold /></el-icon>
            </div> -->
        </div>
      </div>
    </template>
    <ul class="tabsUl">
      <li v-for="item in dataList" :key="item.tagId">
        <div class="flex">
          <div @click="handleCurrentQuery(item)" class="left-part">{{ item.title }}</div>
          <div class="right-part">
            <div class="icon-box">
              <el-icon color="#115E93"><View /></el-icon>
              <div>{{ item.clickNum }}</div>
            </div>
            <div class="time-box">{{ item.time }}</div>
          </div>
        </div>
      </li>
      <div class="empty-box">
        <el-empty v-if="dataList.length === 0" description="暂无数据" />
      </div>
    </ul>
    <div class="pagination-box">
      <BiPagination
        :total="count"
        v-model:page="currentPage"
        v-model:limit="pageSize"
        @pagination="sizeChange"
      />
    </div>
  </el-card>
  <DialogMoreNews ref="DialogMoreNewsRef" />
  <DialogNewsList ref="DialogNewsListRef" />
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { ref, defineProps, reactive, onMounted } from 'vue'
import { getNewsList, getTabList } from '@/api/common/common.js'
import { disabledDate } from './config.js'
import DialogMoreNews from './components/news/DialogMoreNews.vue'
import DialogNewsList from './components/news/DialogNewsList.vue'
import BiPagination from '@/views/components/BiPagination.vue'
const route = useRoute()
const dictNewsId = route?.meta?.menuId
const { proxy } = getCurrentInstance()
const props = defineProps({
  // 获取tabs传参
  //   getTabsDataParams: {
  //     type: Object,
  //     required: true,
  //     default: () => {
  //       {
  //       }
  //     }
  //   }
})
onMounted(() => {
  getTabsData()
})

// 顶部tab区域
let activeName = ref()
let loading = ref(false)
// 获取tab数据功能
let tabsData = ref([])
const getTabsData = () => {
  loading.value = true
  getTabList({ dictNewsId })
    .then(res => {
      if (res.code == 200) {
        if (res.total === 0) {
          loading.value = false
          return
        }
        tabsData.value = res.rows
        queryParams.newsTagId = tabsData.value[0].id
        activeName.value = tabsData.value[0].id
        getNewsListFun()
      }
    })
    .catch(e => {})
}

// 搜索框区域
let dataRange = ref([])
let queryParams = reactive({
  newsTagId: '',
  searchKey: '',
  pageNum: 1,
  pageSize: 15,
  startDate: '',
  endDate: ''
})
// 点击搜索按钮
const onSearch = () => {
  queryParams.startDate = dataRange.value[0]
  queryParams.endDate = dataRange.value[1]
  getNewsListFun()
}

// 点击重置按钮
// const onReset = () => {
//   queryParams.searchKey = ''
//   dataRange.value = []
//   getNewsListFun()
// }

// 分页组件区域
const currentPage = ref(1)
const pageSize = ref(15)
const count = ref(0)

const sizeChange = () => {
  getNewsListFun()
}

// 新闻列表区域
let dataList = ref([])
const getNewsListFun = () => {
  getNewsList(queryParams)
    .then(res => {
      if (res.code == 200) {
        dataList.value = res.rows
        count.value = res.total
      }
    })
    .catch(e => {})
    .finally(() => {
      loading.value = false
    })
}

// 点击tabs触发的方法
const handleClick = (tab, event) => {
  activeName.value = tab.props.name
  queryParams.newsTagId = tab.props.name
  getNewsListFun()
}

// 点击新闻列表数据查看详情
const handleCurrentQuery = item => {
  proxy.$refs.DialogMoreNewsRef.show(item)
}
// 点击查看更多
const lookMore = () => {
  proxy.$refs.DialogNewsListRef.show()
}
// 动态样式
const dynamicStyle = reactive({
  width: '100%'
})
// 动态更新样式
const updateStyle = () => {
  dynamicStyle.width = tabsWidth.value + 'px'
}
const tabs = ref(null)
const myElement = ref(null)
// const lookmore = ref(null)
// const lookmore2 = ref(null)
const tabsWidth = ref(null)
onMounted(() => {
  // myElement.value = tabs.value.offsetWidth
  // lookmore2.value = lookmore.value.offsetWidth
  // tabsWidth.value = myElement.value - lookmore2.value - 10
  updateStyle()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.search-box {
  width: 100%;
  display: flex;
  justify-content: right;
  margin-bottom: 10px;
}
.empty-box {
  padding-top: 10%;
}
.tabMain {
  width: 100%;
}
.flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.tabsUl {
  list-style: none;
  padding-inline-start: 0;
  padding-top: 0 !important;
  margin-top: 0 !important;
  height: calc(100vh - 500px);
  .right-part {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    .icon-box {
      display: flex;
      align-items: center;
      div {
        min-width: 30px;
        margin-left: 5px;
      }
    }
    .time-box {
      margin-left: 20px;
    }
  }
}
.tabsUl li {
  font-size: 14px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f2f2f2;
  padding: 14px 0;
}
.left-part {
  cursor: pointer;
}
.left-part:hover {
  color: #115e93;
}
.more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #115e93;
  font-size: 16px;
  margin-bottom: 15px;
  margin-right: 20px;
  cursor: pointer;
  .el-icon {
    margin-bottom: 0px;
  }
}
.el-tabs__item {
  font-size: 16px;
  font-weight: 500;
}
.el-tabs__item.is-active,
.el-tabs__item:hover {
  font-weight: bold;
  color: #115e93;
}
.el-tabs__active-bar {
  background-color: #115e93;
}
.pagination-box {
  display: flex;
  justify-content: right;
}
.btn {
  border-radius: 2px;
  width: 90px;
  background-color: $bi-form-button;
}
</style>
