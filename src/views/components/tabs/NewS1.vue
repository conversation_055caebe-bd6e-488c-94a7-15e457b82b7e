<template>
  <div class="root">
    <el-form label-width="0" :inline="true" class="tabs-form">
      <el-row :gutter="16" style="margin-right: unset">
        <el-col :span="6">
          <el-date-picker
            v-model="data.year"
            type="daterange"
            range-separator="~"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%"
            :disabledDate="disabledDate"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="data.searchKey"
            :prefix-icon="Search"
            placeholder="请输入关键字"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2">
          <el-button type="primary" color="#0B5FC5" @click="onSearch" style="width: 100%">
            搜索
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-tabs
      v-model="activeName"
      :class="eltabs ? 'demo-tabs eltabs' : 'demo-tabs'"
      @tab-change="handleClick"
      v-if="tabList?.data?.length"
    >
      <el-tab-pane
        v-for="item in tabList.data"
        :label="item?.newsSection"
        :key="item?.id"
        :name="item?.id"
        class="custom-scrollbar"
      >
        <el-skeleton :rows="5" animated v-if="loading" />
        <div style="height: 100%" v-else>
          <div class="listRoot listRoot1" :style="styleRoot" v-if="count > 0">
            <div class="list">
              <div class="listItem" v-for="(item, index) in newsData.value" :key="index">
                <span
                  class="name"
                  @click="
                    () => {
                      clickName(item)
                    }
                  "
                >
                  <WordHighlighter
                    :query="data.searchKey"
                    :highlightStyle="{ 'font-weight': 'bolder', background: 'transparent' }"
                  >
                    {{ item?.title }}
                  </WordHighlighter>
                </span>
                <div class="views1">
                  <div class="views">
                    <el-icon :size="18" color="#115E93" class="icon"> <View /> </el-icon>
                    <div class="num">{{ item?.clickNum }}</div>
                  </div>
                  <span class="date">{{ item?.time }}</span>
                </div>
              </div>
            </div>
            <el-pagination
              v-if="count > 0"
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[15, 20, 30, 50]"
              :total="count"
              @size-change="sizeChange"
              @prev-click="sizeChange"
              @current-change="sizeChange"
              @next-click="sizeChange"
            />
          </div>
          <el-empty description="暂无数据" v-else />
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-empty description="暂无数据" v-else />

    <!-- <DialogMoreNews ref="DialogMoreNewsRef" /> -->

    <el-dialog
      v-model="dialogVisible"
      :title="detailTitle"
      width="80%"
      class="dialog custom-scrollbar"
      :before-close="handleClose"
    >
      <div>
        <div class="laiyuan"><span>【发布时间】</span>{{ tabList.detail?.time }}</div>
        <div class="laiyuan">
          <span>【来源】</span
          ><a :href="tabList?.detail?.url" target="_blank">{{ tabList?.detail?.url }}</a>
        </div>
        <div class="fontwight">
          <div style="margin-right: 20px">
            <span class="mingTitle">命中功能关键字：</span>
            <span>{{ tabList.detail?.functionKey }}</span>
          </div>
          <div>
            <span class="mingTitle">命中信息种类关键字：</span>
            <span>{{ tabList.detail?.matchKey }}</span>
          </div>
        </div>
        <div v-html="detailText" style="padding: 16px; font-size: 14px"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
// import DialogMoreNews from '@/views/components/tabs/components/news/DialogMoreNews.vue'

import WordHighlighter from 'vue-word-highlighter'
import { getQueryNewsTagById, getViewNewsInfo } from '@/api/ambience/macro'
import { Search } from '@element-plus/icons-vue'
import { onMounted, onUpdated } from 'vue'
import { tabsData, disabledDate } from './commonConfigData'
import BiPagination from '@/views/components/BiPagination.vue'
import { list } from '@/api/database/labels.js'
import { getNewsList, getTabList } from '@/api/common/common.js'

const route = useRoute()
const dictNewsId = route?.meta?.menuId
// console.log(route,'dictNewsId')

const props = defineProps({
  styleRoot: {
    type: Object,
    default: {}
  },
  isTab: {
    type: Boolean
  }
})
// startDate=2023-09-28&endDate
const data = reactive({
  year: '',
  searchKey: ''
})

const currentPage = ref(1)
const pageSize = ref(15)
const newsData = reactive([])
const count = ref(0)
const loading = ref(false)
const dialogVisible = ref(false)
const detailText = ref('')
const detailTitle = ref('')
const activeName = ref('')
const tabShow = ref(false)
const pathname = ref('')
const tabList = reactive({
  data: [],
  detail: {}
})

const eltabs = ref(false)

onMounted(() => {
  // checkPath()
  getList()
})

const getList = () => {
  getTabList({ dictNewsId })
    .then(res => {
      // console.log(res,dictNewsId,'res')
      if (res?.code === 200 && res && res?.rows.length > 0) {
        tabList.data = res?.rows
        getNewList(res?.rows[0]?.id)
        activeName.value = res?.rows[0]?.id
        if (res?.rows.length === 1) {
          eltabs.value = true
        }
      } else {
        tabList.data = []
        activeName.value = ''
        eltabs.value = false
      }
    })
    .catch(() => {
      tabList.data = []
      activeName.value = ''
      eltabs.value = false
    })
}

const checkPath = () => {
  let { hash, pathname: p } = location
  p = hash.indexOf('ambience') > -1 ? hash : p
  const _isTab = p.indexOf('market') > -1

  pathname.value = p

  // console.log(_isTab,p,'00000')
  if (_isTab) {
    tabShow.value = true
  } else {
    tabShow.value = false
  }

  if (p !== pathname && !_isTab) {
    activeName.value = ''
  }
}

const onSearch = () => {
  getNewList(activeName.value)
}

const getNewList = async tagId => {
  // console.log('tagId: ', tagId);
  // console.log(tagId,activeName,'tagId')
  if (!tagId) return
  let params = [...tabList?.data].filter(({ id = '' }) => id === tagId) || []
  let obj = {}
  if (params && params.length) {
    let { id } = params[0]
    obj = {
      newsTagId: id
    }
  }
  // console.log(params,obj,'0000')
  try {
    if (loading.value) return
    loading.value = true
    const res = await getQueryNewsTagById({
      ...data,
      ...obj,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })
    const { rows = [], total = 0 } = res
    //  console.log(rows,'00000')
    newsData.value = rows
    count.value = total
    loading.value = false
  } catch (error) {
    console.log(error)
    loading.value = false
    newsData.value = []
    count.value = 0
  }
}
// const { proxy } = getCurrentInstance()
const clickName = data => {
  // proxy.$refs.DialogMoreNewsRef.show(data)

  const { html: text = '', newsId } = data
  const newsTagId = activeName.value
  getViewNewsInfo({ id: newsId, newsTagId }).then(res => {
    if (res?.code === 200) {
      tabList.detail = res?.data
      detailText.value = res?.data?.html
      detailTitle.value = res?.data?.title
      dialogVisible.value = true
      onSearch()
    }
  })
}

const handleClose = () => {
  dialogVisible.value = false
  // onSearch();
}

const handleClick = async active => {
  getNewList(active)
}

const sizeChange = val => {
  getNewList(activeName.value)
}
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';
.root {
  height: 100%;
  ::v-deep .el-dialog__title {
    font-size: 20px;
    font-weight: 550;
  }
  ::v-deep .el-dialog {
    padding: 20px;
  }
  ::v-deep .demo-tabs {
    &.el-tabs {
      border-radius: 8px;
      overflow: hidden;
      height: calc(100% - 75px);
      background-color: #fff;
      .el-tabs__header {
        background: #fff;
        border: unset;
        .el-tabs__nav-wrap {
          &::after {
            height: 0;
          }
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              padding: 20px;
              .el-tabs__item {
                &.is-active {
                  color: #0b5fc5 !important;
                  background: #fff !important;
                  box-shadow: unset;
                }
              }
            }
          }
        }
      }
    }
  }
  .demo-tabs,
  .el-skeleton {
    // margin-top: 40px;
    // background-color:#fff;
    // padding:10px;
  }
  .search {
    // position: absolute;
    // right: 20px;
    // top: 10px;
    display: flex;
    // justify-content: end;
  }
  .listRoot {
    margin-top: 40px;
    border: 1px solid $border-btn-color;
    padding: 10px;
    padding-bottom: 0px;
    background-color: #fff;
    height: calc(100vh - 300px);

    .list {
      height: calc(100% - 60px);
      overflow: auto;
    }

    .listTop {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid $border-btn-color;
      padding-bottom: 6px;
      .block-title {
        height: 28px;
      }
    }

    .lookMore {
      color: $btn-color;
      min-width: 70px;
      line-height: 20px;
      font-size: 16px;
    }

    .listItem {
      display: flex;
      border-bottom: 1px solid $border-btn-color;
      margin: 0px;
      .name {
        font-size: 16px;
        white-space: nowrap; /* 保证文本在一行内显示 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        padding: 10px;
        flex: 1;
        margin-right: 40px;
      }
      .name:hover {
        color: #115e93;
        cursor: pointer;
      }
    }

    .list .listItem:last-child {
      border-bottom: 0px;
    }
  }

  .listRoot1 {
    margin-top: 0px;
    height: calc(100vh - 340px);
    height: 100%;
    position: reactive;
    overflow: auto;
    ::v-deep .el-pagination--default {
      position: absolute;
      bottom: 20px;
      right: 10px;
    }
  }

  .listRoot2 {
    height: calc(100vh - 230px);
  }

  .date {
    font-size: 16px;
    line-height: 40px;
    // color:#cbd0d3;
    max-width: 100px;
    min-width: 40px;
    margin-left: 60px;
  }

  .el-pagination {
    display: flex;
    justify-content: end;
  }

  ::v-deep .el-dialog:not(.is-fullscreen) {
    height: 92%;
    margin-top: 2vh !important;
    overflow: auto;
  }

  .views1 {
    display: flex;
    // margin-right
  }

  .views {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .icon {
      margin-right: 8px;
    }
    .num {
      color: #051c2c;
      min-width: 20px;
      text-align: right;
    }
  }

  ::v-deep .el-pager .is-active {
    background: #dfedfd;
    color: #0b5fc5;
  }
  .list {
    padding-right: 20px;
  }

  ::v-deep .list::-webkit-scrollbar {
    width: 4px !important;
  }

  ::v-deep .list::-webkit-scrollbar-thumb {
    height: 3px;
    /* 水平滚动条内滑块的高度 */
    width: 2px;
    /* 垂直滚动条内滑块的宽度 */
    background-color: #ccc;
  }

  .eltabs {
    ::v-deep .el-tabs__header {
      display: none;
    }
    .listRoot1 {
      height: calc(100vh - 280px);
      overflow: auto;
    }
  }

  .laiyuan {
    font-size: 18px;
    margin-bottom: 20px;
    span {
      padding-right: 10px;
    }
    a {
      color: #145cbc;
    }
  }

  ::v-deep .el-empty {
    background-color: #fff;
    height: 100%;
  }

  ::v-deep .el-tabs__header {
    background-color: #fff;
    padding: 10px;
    margin-bottom: 0px;
    padding-bottom: 0px;
  }

  ::v-deep .el-dialog__title {
    font-size: 22px;
  }

  ::v-deep .el-dialog__body {
    font-size: 20px;
    color: #000;
    font-weight: 500;
  }

  .fontwight {
    display: flex;

    .mingTitle {
      font-size: 20px;
      font-weight: 550 !important;
    }
  }
}

// ::v-deep .el-tabs {
//   --el-tabs-header-height: 100%;

// }
</style>
