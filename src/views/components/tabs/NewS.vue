<template>
  <div class="content">
    <el-form label-width="0" :inline="true" class="tabs-form">
      <el-row :gutter="16" style="margin-right: unset">
        <el-col :xs="10" :sm="10" :md="6">
          <el-date-picker
            v-model="data.params.dateRange"
            type="daterange"
            range-separator="-"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%"
            clearable
            :disabledDate="disabledDate"
          />
        </el-col>
        <el-col :xs="8" :sm="8" :md="6">
          <el-input
            v-model="data.params.searchKey"
            :suffix-icon="Search"
            placeholder="请输入关键字"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :xs="6" :sm="6" :md="6">
          <el-button type="primary" color="#0B5FC5" @click="handleQuery" style="width: 60px">
            搜索
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-tabs
      v-model="data.active"
      v-loading="loading.sending"
      class="bi-loading bi-tabs-in table-box"
      :class="data.hideTabs ? 'hide-el-tabs' : ''"
    >
      <el-tab-pane v-for="i in data.tabs" :label="i.label" :name="i.value">
        <el-table
          :data="i.list"
          class="table-box"
          height="calc(100% - 48px)"
          :show-header="false"
          @row-click="toggleList"
          style="width: 100%"
        >
          <el-table-column>
            <template #default="{ row }">
              <div class="ellipsis">
                <WordHighlighter
                  :query="data.params.searchKey"
                  :highlightStyle="{ 'font-weight': 'bolder', background: 'transparent' }"
                  >{{ row.title }}</WordHighlighter
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column width="60">
            <template #default="{ row }">
              <div class="views">
                <el-icon :size="12" color="#115E93" class="icon"> <View /> </el-icon>
                <div class="num">{{ row.clickNum }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="time" width="120" show-overflow-tooltip />
        </el-table>
        <BiPagination
          :total="data.tabs[currentIndex].total"
          v-model:page="data.params.pageNum"
          v-model:limit="data.params.pageSize"
          @pagination="getListData"
        />
      </el-tab-pane>
    </el-tabs>
    <infoDialog v-model="data.detail.flag" :query="data.detail.query" />
  </div>
</template>

<script setup>
import { View, Search } from '@element-plus/icons-vue'
import WordHighlighter from 'vue-word-highlighter'
import BiPagination from '@/views/components/BiPagination.vue'
import infoDialog from '@/views/home/<USER>/infoDialog.vue'

import { tabList, queryNewsTagById } from '@/api/intelligence/tag.js'
import { disabledDate } from './config.js'
const route = useRoute()
const dictNewsId = route?.meta?.menuId

const date = new Date()
const year = date.getFullYear()
let month = date.getMonth() + 1
if (month < 10) month = '0' + month
let day = date.getDate()
if (day < 10) day = '0' + day
const currentDay = `${year}-${month}-${day}`
const prevDay = `${year - 1}-${month}-${day}`
const data = reactive({
  params: {
    dateRange: [prevDay, currentDay],
    startDate: '',
    endDate: '',
    searchKey: '',
    newsTagId: '',
    pageNum: 1,
    pageSize: 15
  },
  active: '',
  hideTabs: false,
  detail: {
    flag: false,
    query: {}
  },
  tabs: []
})
const loading = reactive({
  sending: false
})

const currentIndex = computed(() => data.tabs.findIndex(v => v.value === data.active))
watch(
  () => data.active,
  val => {
    data.params.newsTagId = val
    data.params.pageNum = 1
    getListData()
  },
  { deep: true }
)

const toggleList = ev => {
  data.detail.query = {
    searchKey: data.params.searchKey,
    newsInfoId: ev.newsId,
    newsTagId: data.params.newsTagId
  }
  data.detail.flag = true
}

/** 搜索按钮操作 */
function handleQuery() {
  data.params.pageNum = 1
  getListData()
}
const getListData = async () => {
  if (loading.sending) return
  loading.sending = true
  const params = JSON.parse(JSON.stringify(data.params))

  if (params.dateRange && params.dateRange.length > 0) {
    params.startDate = params.dateRange[0]
    params.endDate = params.dateRange[1]
  } else {
    params.startDate = ''
    params.endDate = ''
  }
  const res = await queryNewsTagById(params).catch(e => e)
  if (res.code !== 200) return (loading.sending = false)
  data.tabs[currentIndex.value].list = [...res.rows]
  data.tabs[currentIndex.value].total = res.total
  await nextTick()
  loading.sending = false
}

async function getTabListData() {
  const res = await tabList({ dictNewsId }).catch(e => e)
  if (res?.code !== 200) return
  const rows = res?.rows ?? []
  const tabs = []
  rows.forEach((el, index) => {
    if (index === 0) data.active = el.id
    tabs.push({
      label: el.newsSection,
      value: el.id,
      list: [],
      total: 0
    })
  })
  if (tabs && tabs.length > 1) {
    data.hideTabs = false
  } else {
    data.hideTabs = true
  }
  data.tabs = tabs
}
getTabListData()
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .table-box {
    margin-top: 10px;
    flex: 1;
    :deep(.cell) {
      cursor: pointer;
    }
  }
  .hide-el-tabs {
    :deep(.el-tabs__header) {
      display: none;
    }
  }
  // el-tabs 重写
  :deep(.el-tabs) {
    height: calc(100% - 70px) !important;
    .el-tab-pane {
      height: 100%;
    }
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs--border-card) {
    & > .el-tabs__content {
      padding: 0 !important;
    }
  }
  :deep(.el-table__row) {
    .el-table__cell:last-child {
      .cell {
        text-align: right;
      }
    }
  }
}
.views {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .icon {
    margin-right: 4px;
  }
  .num {
    color: #051c2c;
  }
}
</style>
