import {
  dictsEngineFactory,
  fadongjichangjia,
  dictsFuelType2,
  dictsFuelType
} from '@/utils/common/dicts.js'
import {numberFormat} from '@/utils/format.js'
// import { formatter } from '../../components/echarts/config'
// import { getInitMonth } from '@src/store/modules/macro.js'
export const yAxisName = '单位:（%)'
export const yAxisName2 = '单位:（台)'
export const xAxisData = ['1月',	'2月',	'3月',	'4月','5月',	'6月',	'7月',	'8月',	'9月',	'10月',	'11月',	'12月'];
export const xAxisMonth1 = ['01',	'02',	'03',	'04','05',	'06',	'07',	'08',	'09',	'10',	'11',	'12'];
export const xAxisMonth = xAxisData

import {
    dictsPointerType,
  } from '@/utils/common/dicts.js'
export const getYAxisName = (con = '%')=>{
    return `单位:（${con})`
}

export const getAddTotal = (paramsList,isTotal = true,obj = {})=>{
    const { name = '' } = obj
    const params = paramsList?.sort(({value:b = 0},{value:a = 0})=>{
      return Number(a) - Number(b)
    })
    let total = 0;
    let result = params[0].name + '<br/>';

    let pg = ''
     if(name.indexOf('%') > -1){
      pg = '%'
     }
     
    params.forEach(function (item) {
      if (item.value && item.value !== '') {
        const value = Number(item?.value || 0)
        total += value;
        let _value = `${value}${pg}`
        let newValue = numberFormat(_value)
        result += item.marker +item.seriesName + ' : ' + `${newValue}` +'<br/>';
      }
    });

  if(isTotal){
    return result
  }
  total = Number(total)
  result += '总和 : ' + numberFormat(total);
  return result;
}


export const getInitDate = ()=>{
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    let month = currentDate.getMonth() + 1;
    let oldYear = year-1;
    let oldMonth = month >=10 ? month :`0${month}`

    if((month - 12)===0){
      oldMonth = `01`;
      oldYear = year
    }
   return [ `${oldYear}`,`${year}` ]
  }

  
export const getInitDate2 = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldYear = year-2;
  let oldMonth = month >=10 ? month :`0${month}`

  let newmonth = month-1

  if((month - 12)===0){
    oldMonth = `01`;
    oldYear = year
  }
  if(newmonth===0){
    newmonth = '12';
    year = year-1
  }


 return [ `${oldYear}-${oldMonth}`,`${year}-${newmonth}` ]
}

  
  

  export const getInitMonth = ()=>{
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    let month = currentDate.getMonth() + 1;
    
   return [ `${year}-${month}` ]
  }


export  const disabledDate = (time)=>{
    return time.getTime() > Date.now()
}


export const getPreviousYear = (date)=>{
    if(!date)return
    let [key1,key2] = date.split("-")
    // console.log(date,'date')
    let _key = `${Number(key2)}`
    return {
        year:`${key1}年`,
        month:`${_key}月`,
        lastYear:`${key1-1}年`,
        lastMonth:`${_key}月`
    }
}


export const tabsData = [
    '宏观∙行业趋势∙政策法规',
    '经营动态',
    '高管动态',
    '人事变动',
    '市场活动与订单',
    '供货价格与商务政策',
    '终端返利与服务政策',
    '产品与技术',
    '产品市场表现',
    '战略合作',
    '四化动态',
]


export const filterdictsPointerType = ()=>{
    let { hash:pathname = '',pathname:p } = location
    // console.log(pathname,'pathname')
    pathname = pathname.indexOf('market') > -1 ? pathname : p;

    let _dictsPointerType = dictsPointerType
    if(pathname.indexOf('marketmachine') > -1 || pathname.indexOf('electricity') > -1){
        _dictsPointerType = dictsPointerType.filter(({value})=>(value !== '2'))
    }
    if(pathname.indexOf('marketenergy') > -1){
      _dictsPointerType = dictsPointerType.filter(({value})=>(value !== '1'))
    }
    return _dictsPointerType
}

export const filterSpan = ()=>{

  let { hash:pathname = '',pathname:p } = location
  pathname = pathname.indexOf('ambience') > -1 ? pathname : p;

  if(pathname.indexOf('marketpower') > -1 ||pathname.indexOf('marketenergy') > -1 ||pathname.indexOf('vehicle') > -1){
    return 2
  }
  return 3
}


export const fadongjichangjiaListget = (params)=>{
   let { hash:pathname = '',pathname:p } = location
  pathname = pathname.indexOf('ambience') > -1 ? pathname : p;

  if(pathname.indexOf('electricity') > -1){
    if(params?.dataSource === '7'){
      const data = fadongjichangjia[params?.subMarket1] || fadongjichangjia['船机']
      return data
    }
    return dictsEngineFactory
  }
}

// 导入统一的数据类型处理工具
import { formatDataTypeForAPI } from '@/utils/common/dataTypeUtils'

export const changeDataType = formatDataTypeForAPI


/*
monthKeys:年 ['2022','2024']，或 ['2022-01','2024-02']；

pr：中间用的分割符号
sliceIndex：截取年份后几位数

*/

export const bqMonth = (monthKeys = [],pr="'",sliceIndex=2)=>{


  if(!monthKeys || monthKeys.length===0)return []

  // 传年月
  
  if(monthKeys[0].indexOf('-')>-1){
    // 开始月
    let chushiyue = '';
    let jieshuyue = '';
    // 获取开始年与开始月
    let _monthKeys = monthKeys.map((item,index)=>{
       let [y,m] = item.split('-')
        if(index===0){
          chushiyue = m
        }
        if(index === 1){
          jieshuyue = m
        }
        return y
    })

    // 生成区间年
      let newMonth = []  
      let [m1,m2] = _monthKeys;
      for(let i = m1;i<= m2; i++){
      let _i = `${i}`.slice(sliceIndex)
        newMonth.push(_i)
      }


      let monthData = []
      newMonth.forEach((item,index)=>{
        let monthArr = [...xAxisMonth1]
        if(index ===0){
          // 开始年的月份
          let chushiyueNum = Number(chushiyue)
          monthArr = monthArr.slice(chushiyueNum)

        }else if(index === newMonth.length -1){
          // 结束年的月份
          let jieshuyueNum = Number(jieshuyue)
          monthArr = monthArr.slice(0,jieshuyueNum)
        }

        let _xAxisMonth=  monthArr.map((i)=>{
            return `${item}${pr}${i}`
          })
        monthData.push(..._xAxisMonth)
          
      })

      return monthData

  }


  if(monthKeys[0].length === 4){
    // 只传年的操作 生成区间年
    let [m1,m2] = monthKeys;
    let newMonth = []  
    for(let i = m1;i<= m2; i++){
    let _i = `${i}`.slice(sliceIndex)
      newMonth.push(_i)
    }

    // 根据年份生成年月份
    let monthData = []
      newMonth.forEach((item)=>{
      let _xAxisMonth=  xAxisMonth1.map((i)=>{
          return `${item}${pr}${i}`
        })
      monthData.push(..._xAxisMonth)
        
    })

    return monthData
  }

  

}


export const getDictsFuelType = ()=>{
  let { hash:pathname = '',pathname:p } = location
  pathname = pathname.indexOf('ambience') > -1 ? pathname : p;

  if(pathname.indexOf('marketenergy')>-1){
    return  dictsFuelType
  }else{
   
    return dictsFuelType2
  }
}





  
