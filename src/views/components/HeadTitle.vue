<template>
  <div class="head-title">
    <div class="head-title__text">{{ title }}</div>
  </div>
</template>

<script setup>
// defineOptions({
//   name: "BlockTitle",
// });
defineProps({
  title: {
    type: String,
    required: true
  }
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.head-title {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 50px;
  font-size: 18px;
  font-weight: bold;
  color: #051c2c;
  letter-spacing: 0px;
  background-size: 100% 100%;
  border: 1px solid;
  border-image: linear-gradient(
      270deg,
      rgba(138, 188, 249, 0) 0%,
      #83b5f3 51%,
      rgba(131, 181, 243, 0) 99%,
      rgba(131, 181, 243, 0) 99%
    )
    1;
  &__text {
    position: relative;
    margin-left: 20px;
    z-index: 1;
  }
  &::after {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    width: 204px;
    height: 56px;
    display: block;
    background: url(/src/assets/images/head-title.png) no-repeat;
    background-size: 100% 100%;
  }
}
</style>
