<template>
  <div class="bankuai">
    
    <el-card class="box-card" shadow="hover">
      <template #header> 
        <div class="card-header">
          <div class="header_title">报告区</div>
          <div class="header_title" style="color: #115E93;">查看更多></div>
        </div>
      </template>
      <el-row v-for="o in 7" :key="o" class="text_item" :gutter="20">
        <el-col :span="16" class="text_items">
          <el-row  @click="clickQuery">
            <el-col :span="4" class="report">报告|</el-col>
            <el-col :span="8" class="context"  title="充电比加油方便 天津充电基础设施覆盖率100%">{{ '充电比加油方便 天津充电基础设施覆盖率100%' }}</el-col>
            <el-col :span="3" class="report"><el-button class="PDF" @click="downloadPdf" style="color: #115E93;" type="primary" link >PDF</el-button></el-col>
            <el-col :span="9" class="date" >{{ '2024/11/10 ' }}</el-col>
          </el-row>
          
          
        </el-col>
        <el-col :span="8" class="text_data" style="color:#9BA4AB">
          <el-button type="primary" @click="downloadPreviews" link style="color: #115E93;" >预览</el-button>
          <el-button type="primary" @click="downloadWorld" link style="color: #115E93;">下载</el-button>
        </el-col> 
      </el-row>
     
    </el-card>
    <previewsForm ref="modolForm" />
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();

import { Download } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import previewsForm from './previewsForm'
// const props = defineProps({
//   // 数据
//   moreShow: {
//     type: String,
//     default: '',
//   },
  
// })
function downloadImg(url, filename){
  const a = document.createElement("a"); 
  a.href = url;
  a.download = filename;  // 修改文件名
  a.style.display = "none"; 
  document.body.appendChild(a); 
  a.click(); 
  document.body.removeChild(a); 
} 
function downloadPdf(){
  downloadImg('/static/充电比加油方便.pdf','充电比加油方便.pdf')
} 
function downloadWorld(){
  downloadImg('/static/充电比加油方便.docx','充电比加油方便.docx')
} 
function downloadPreviews(){
  proxy.$refs.modolForm.show()
} 
function clickQuery(){  

}


</script>

<style lang="scss" scoped>
.box-card{ 
  border-radius: 0;
  overflow: hidden;
  height: 100%;

}
.bankuai{  
  height: 360px;
  overflow: auto;
}
.bankuai::-webkit-scrollbar { width: 3px; background: #d0dbeb; }

.card-header{ 
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  .header_title{  
    font-size: 18px;
    line-height: 24px;
    color: #051C2C ;
    font-weight: Regular;
  }
}
.text_item { 
  cursor: pointer;

  display: flex;
  justify-content: space-between;
  .text_items{  
    font-weight: 400;
    font-size: 16px;
    line-height: 40px;
    color: #051C2C;
    font-weight: Regular;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的文本 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .text_data{  
    text-align: right;
    font-weight: 400;
    font-size: 16px;
    line-height: 40px;
    color: #9BA4AB;
    font-weight: Regular;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的文本 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .report{  
    font-weight: 600;

  }
  .context{ 
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的文本 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .date{ 
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的文本 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }

}
</style>
