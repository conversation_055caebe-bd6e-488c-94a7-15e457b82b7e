<template>
    <!-- 详情对话框 -->
    <el-dialog :title="title" v-model="visible" append-to-body>
      <el-row>
         <el-col >
            <h2 class="titles">充电比加油方便 天津充电基础设施覆盖率100%</h2>
         </el-col>
         <el-col>
            <p class="texts">
               天津在新能源汽车充电设施建设方面取得了显著成就，实现了充电基础设施的全覆盖。这一成就意味着，在天津，电动汽车车主可以随时随地找到充电设施，极大地提高了充电的便利性。与传统的加油方式相比，充电设施的广泛分布使得电动汽车的使用更加便捷。这一成果不仅促进了新能源汽车的普及，也体现了天津在推动绿色出行、减少碳排放方面的决心和行动力，为构建更加环保、可持续的城市交通体系奠定了坚实基础。
            </p>
         </el-col>
      </el-row>
      
      
     <template #footer>
        <div class="dialog-footer">
           <el-button @click="cancel">关闭</el-button>
        </div>
     </template>
  </el-dialog>
 </template>
 
 <script setup >
  const visible = ref(false);
  const title = ref("");
  const fnoticeContent = ref("")

 
   /** 详情按钮操作 */
   function show(row) {
    visible.value = true;
    title.value = "";
  }
   /** 取消按钮 */
   function cancel() {
    visible.value = false;
  }
 
  defineExpose({
   show,
 });
  </script>
  <style lang="scss" scoped>
  .texts{  
   text-indent: 20px;
   line-height: 20px;

  }
  .titles{ 
   text-align: center;
  }

</style>
  