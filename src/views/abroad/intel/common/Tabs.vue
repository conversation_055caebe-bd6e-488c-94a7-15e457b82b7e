<template>
  <div class="tabsBox">
    <div style="    float: right;color: #115E93;      margin-right: -14px;
   border-bottom: 2px solid #E6E8EB;margin-top: 1px;
  ">
      <el-button type="primary" style="padding: 0px;    line-height: 35px;

}" @click="downloadWorld" link >查看更多</el-button>
    </div>
 

	<!--
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="item in tabsData" :key="item.value" :label="item.label" :name="item.value">
        <ul class="tabsUl">
          <li v-for="item2 in dataList" :key="item2.id"><div @click="handleCurrentQuery" class="textNav">{{item2.text}}</div><div class="dateNav">{{item2.date}}</div></li>
        </ul>
      </el-tab-pane>
    </el-tabs>
	-->
  </div>
  
</template>

<script setup>
import { handleCurrentChange } from 'element-plus/es/components/tree/src/model/util.mjs';
import { ref, defineEmits, defineProps, defineExpose   } from 'vue'

const props = defineProps({
  tabsData: {
    type: Array,
    required: true,
  },
  activeName:{
    type:String,
    required:true,
  },
  dataList:{
    type:String,
    required:true,
  }
});
// defineExpose({
//   tabsData,
//   activeName,
//   dataList
// })
const emit = defineEmits(['call-parent-method']);
function handleClick(tab, event) {
  console.log(tab.props.name);
  activeName = tab.props.name
  console.log(activeName)
  $emit('call-parent-method')
}
function handleCurrentQuery (){ 
  emit('handleCurrentQuery')
} 
</script>

<style lang="scss" scoped>
.tabsBox {
  padding: 10px;
}
.tabsUl{ list-style: none;padding-inline-start: 0;margin-top: -10px; }
.tabsUl li{ font-size: 16px; display: flex; align-items: center; border-bottom: 1px solid #f2f2f2; padding: 10px 0; }
.textNav{ flex: 1; color: #333;}
.dateNav{ width: 100px; color: #bbb;}


</style>
<style>
.el-tabs__item { 
  font-size: 18px;
  font-weight: 500;

}
.el-card__body {
  padding: 8px 20px 20px 20px !important;
}
</style>
