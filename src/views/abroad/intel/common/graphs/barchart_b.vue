<template>
  <div ref="chart" style="width: 100%; height: 300px;"></div>
</template>
 
<script setup>
import { onMounted, ref ,defineProps} from 'vue';
import * as echarts from 'echarts';

 const animationDuration = 1000
const chart = ref(null);
const props = defineProps({
  modulesData: {
    type: String,
    required: true,
  }
});
onMounted(() => {
  const myChart = echarts.init(chart.value);
  const option = {
    // ECharts 配置项
    title: {
      left: "center",
      text: props.modulesData === 'car'?'柳汽份额结构走势（北斗数）':
      props.modulesData === 'mechanical'?'近三年工程机械之三一动力结构比例走势':
      props.modulesData === 'agro'?'近三年农业机械之常州东风动力结构比例走势':'',
      textStyle: {
       fontSize: 15
     },
    },
    tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  
  legend: {
    left: 'right',      // 放置在右侧
    top: 'bottom',  
    bottom: 0,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
         // 去除背景线
         splitLine: {
            show: false
        }

    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '玉柴',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#00A9F4'
      },
      data: [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      label: {
                show: true,
                position: 'top',
                formatter: '{c} %'
            },
      animationDuration
    },
    
    {
      name: '潍柴',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#5FCEFF'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      label: {
                show: true,
                position: 'top',
                formatter: '{c} %'
            },
      animationDuration
    },
    {
      name: '康明斯',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#C2E7F2'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      animationDuration,
      label: {
                show: true,
                position: 'top',
                formatter: '{c} %'
            }
    },
    {
      name: '其他',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#115E93'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      label: {
                show: true,
                position: 'top',
                formatter: '{c} %'
            },
      animationDuration
    },
  ]
  };
 
  option && myChart.setOption(option);
});
</script>
 
<style>
/* 你的样式 */
</style>