<template>
  <div class="box">
    <div >  
      待补充.....
    </div>
    <!-- <div class="datarea">
     数据区
    </div> -->
    <!-- <el-divider style="margin: 8px 0" /> -->
    <!-- <searchForm @handedataSource="handedataSource" />
    <el-row gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover">
         <barchart :modulesData="modulesData" :dataSource="dataSource" />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover">
          <barchart_b :modulesData="modulesData"/>
        </el-card>
      </el-col>
    </el-row> -->
   
  </div>
</template>

<script setup>
import { defineProps  } from 'vue'
const props = defineProps({
  modulesData: {
    type: String,
    required: true,
  },

});
import searchForm from "./searchForm"
import barchart_b from './barchart_b'
import barchart from './barchart'
const dataSource = ref('')
const handedataSource = (val) =>{  
  console.log("handedataSource==============",val)
   dataSource.value =val
}


</script>

<style lang="scss" scoped>
.el-form--inline .el-form-item{  
  margin-right: 10px;
}
.box{ 
  padding: 15px;
  background: #fff;
 }
 .datarea{ 
  padding: 14px 15px 0px !important;
  font-size: 18px;
  color: #051C2C;
 }
</style>
