<template>
  <el-dialog
    v-model="data.modelValue"
    :title="`${data.param.subMarket1}`"
    :close-on-click-modal="false"
    top="4vh"
    width="50vw"
    append-to-body
    @close="closeDialog"
  >
    <div class="wrap">
      <el-table
        stripe
        v-loading="data.loding"
        :data="data.table"
        class="table-box"
        height="100%"
        style="width: 100%"
        :border="true"
      >
        <el-table-column
          header-align="left"
          align="left"
          prop="submarket"
          label="细分市场"
          min-width="60"
          show-overflow-tooltip
        />
        <el-table-column
          header-align="right"
          align="right"
          prop="sales"
          label="销量(台)"
          min-width="60"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ numberFormat(row.sales, 0) }}
          </template>
        </el-table-column>
        <el-table-column
          header-align="right"
          align="right"
          label="同比"
          min-width="50"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span
              :style="{
                color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
              }"
              >{{ row.sales_prop ? numberFormat(row.sales_prop) : '/' }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          header-align="right"
          align="right"
          label="占有率"
          min-width="60"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span
              :style="{
                color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
              }"
              >{{ row.proportion ? numberFormat(row.proportion) : '/' }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          header-align="right"
          align="right"
          label="占有率同比"
          min-width="70"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span
              :style="{
                color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
              }"
              >{{ row.prop_change ? numberFormat(row.prop_change) : '/' }}</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>
<script setup>
import { numberFormat } from '../../../../../utils/format'
import { exportMarket } from '@/api/abroad/car.js'
const data = reactive({
  modelValue: false,
  param: {},
  loding: false,
  table: []
})
const closeDialog = () => {
  data.modelValue = false
}

const show = param => {
  data.table = []
  data.param = param
  data.modelValue = true

  data.param.subMarket2 = ''
  data.loading = true
  exportMarket(data.param, 'subMarketListThird')
    .then(res => {
      if (res.code == 200) {
        const subMarketList = res.data.subMarketList
        data.table = subMarketList
      }
      data.loading = false
    })
    .catch(e => {
      data.loading = false
    })
}
defineExpose({
  show
})
</script>
<style scoped lang="scss">
@import '@/assets/styles/bi/variables.module.scss';
.wrap {
  height: 50vh;
  min-height: 50vh;
  padding: 0;
}
</style>
