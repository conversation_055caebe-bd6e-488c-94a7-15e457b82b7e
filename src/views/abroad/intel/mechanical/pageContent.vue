<template>
  <div class="wrap page-content">
    <SearchFormResource :params="data.params" @change="getParams" />
    <el-row :gutter="16">
      <el-col :xs="24" :sm="24" :md="15" :lg="15" :xl="15">
        <div class="map-wrap">
          <div class="sales">
            <div class="sales__item">
              <div class="sales__item--name">
                {{ data.params.year }}年总销量
                {{ data.mapTotal.dateRange ? `（${data.mapTotal.dateRange}）` : '' }}
              </div>
              <div
                class="sales__item--value"
                :style="{
                  color: data.mapTotal.totalSales.toString().includes('-') ? '#F16C55' : '#115E93'
                }"
              >
                {{ data.mapTotal.totalSales ? numberFormat(data.mapTotal.totalSales, 0) : '—' }}
              </div>
            </div>
            <div class="sales__item">
              <div class="sales__item--name">同比增长</div>
              <div
                class="sales__item--value"
                :style="{
                  color: data.mapTotal.totalProp.toString().includes('-') ? '#F16C55' : '#115E93'
                }"
              >
                {{ data.mapTotal.totalProp ? numberFormat(data.mapTotal.totalProp) : '—' }}
              </div>
            </div>
          </div>
          <maps
            ref="refMaps"
            :series-data="data.chartData"
            v-loading="loading.chartData"
            @select="changeSelectArea"
          />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="9" :lg="9" :xl="9">
        <el-card style="margin-bottom: 12px">
          <div class="ratio-width" style="padding-bottom: 58.5%">
            <div ref="target" class="ratio-width__wrap">
              <el-table
                stripe
                v-loading="loading.tableA"
                :data="data.tableA"
                class="table-box"
                height="100%"
                style="width: 100%"
                :border="true"
              >
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="submarket"
                  label="细分市场"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div class="area-item" @click="toggleSubmarket(row.submarket)">
                      <a style="color: var(--el-color-primary)">{{ row.submarket }}</a>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  prop="sales"
                  label="销量(台)"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    {{ numberFormat(row.sales, 0) }}
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label="同比"
                  min-width="50"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.sales_prop ? numberFormat(row.sales_prop) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label="占有率"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.proportion ? numberFormat(row.proportion) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label="占有率同比"
                  min-width="70"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.prop_change ? numberFormat(row.prop_change) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label=""
                  min-width="40"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <a
                      style="color: var(--el-color-primary)"
                      @click="openSubmarket2(row.submarket)"
                    >
                      查看
                    </a>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
        <el-card>
          <div class="ratio-width" style="padding-bottom: 58.5%">
            <div ref="target" class="ratio-width__wrap">
              <el-table
                stripe
                v-loading="loading.tableB"
                :data="data.tableB"
                class="table-box"
                height="100%"
                style="width: 100%"
                :border="true"
                :row-style="rowStyle"
              >
                <el-table-column
                  header-align="left"
                  align="left"
                  min-width="70"
                  label="细分区域"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div class="area-item" @click="toggleAreaItem(row.area)">
                      <a style="color: var(--el-color-primary)">{{ row.area }}</a>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  prop="sales"
                  label="销量(台)"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    {{ numberFormat(row.sales, 0) }}
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label="同比"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.sales_prop ? numberFormat(row.sales_prop) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label="占有率"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.proportion ? numberFormat(row.proportion) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="right"
                  align="right"
                  label="占有率同比"
                  min-width="70"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.prop_change ? numberFormat(row.prop_change) : '/' }}</span
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <subMarket2Dialog ref="subMarket2DialogRef" />
  </div>
</template>

<script setup>
import { numberFormat } from '../../../../utils/format'
import maps from './components/maps.vue'
import subMarket2Dialog from './components/subMarket2Dialog.vue'
import SearchFormResource from '../components/SearchFormResource.vue'

import { exportMarket, exportMarketDateRange } from '@/api/abroad/car.js'
const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '海关数据')?.value

const { proxy } = getCurrentInstance()

// 初始化搜索条件
const originParams = {
  // year: '2023', // 年份
  year: new Date().getFullYear().toString(), // 年份
  month: '12', // 月
  pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  quarter: '1', // 季度
  segment: '通机', // 板块
  subMarket1: '', // 细分市场1
  subMarket2: '', //  细分市场2
  manuFacturer: '', // 主机厂
  dataSource: dataSource, // 数据来源（海关数）
  area: '', // 区域
  country: '' //国家
}
const refMaps = ref(null)
const subMarket2DialogRef = ref(null)
const data = reactive({
  params: { ...originParams },
  mapTotal: { totalProp: '', totalSales: '', dateRange: '' },
  chartData: [],
  tableA: [], // 第一个列表
  tableB: [] // 第二个列表
})

const loading = reactive({
  chartData: false,
  tableA: false,
  tableB: false
})
/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = params => {
  initBySubMarketList(params)
  initByAreaList(params)
}
/**
 * @description 点击搜索
 * @param params 搜索参数
 */
function getParams(params) {
  data.params = params
  // isarea.value = true;
  initChartData(params)
}
const initBySubMarketList = params => {
  if (loading.tableA) {
    proxy.$modal.msgError('数据正在处理，请勿重复提交')
    return
  }
  data.tableA = [] // 第一个列表
  params.dataRange = params.year ? '1' : '0'
  loading.tableA = true
  exportMarket(params, 'subMarketList')
    .then(res => {
      if (res.code == 200) {
        const subMarketList = res.data.subMarketList
        data.tableA = subMarketList
      }
      loading.tableA = false
    })
    .catch(e => {
      loading.tableA = false
    })
}
const openSubmarket2 = submarket => {
  console.log(subMarket2DialogRef)
  subMarket2DialogRef.value.show({ ...data.params, subMarket1: submarket })
}

/**
 * 动作类型判断
 * @param type
 * subMarket1：说明是通过细分市场反查细分区域，之刷新细分区域的表
 */
const initByAreaList = (params, type = null) => {
  if (loading.chartData || loading.tableB) {
    proxy.$modal.msgError('数据正在处理，请勿重复提交')
    return
  }
  if (data.params.area === '') {
    data.chartData = []
  }
  data.tableB = [] // 第二个列表

  params.dataRange = params.year ? '1' : '0'
  loading.chartData = true
  loading.tableB = true
  exportMarket(params, 'areaList')
    .then(res => {
      if (res.code == 200) {
        const areaList = res.data.areaList || []
        data.tableB = areaList
        if (data.params.area === '') {
          data.chartData = areaList.map(el => ({ name: el.area, value: el.sales }))
        }
      }
      loading.tableB = false
    })
    .catch(e => {
      loading.tableB = false
    })
  // 如果是切换细分市场，不需要刷新地图区域的统计
  if (type) {
    loading.chartData = false
    return
  }
  // 查询统计参数
  exportMarket(params, 'mapTotal')
    .then(res => {
      if (res.code == 200) {
        data.mapTotal.totalProp = res.data.totalProp ? res.data.totalProp : ''
        data.mapTotal.totalSales = res.data.totalSales ? res.data.totalSales : ''
      } else {
        data.mapTotal.totalProp = ''
        data.mapTotal.totalSales = ''
      }
      loading.chartData = false
    })
    .catch(e => {
      loading.chartData = false
    })
  exportMarketDateRange(params).then(res => {
    if (res.code === 200) {
      let minDate = res?.data?.minDate ?? ''
      let maxDate = res?.data?.maxDate ?? ''
      let dateRange = ''
      if (minDate && maxDate && minDate.indexOf('-') !== -1 && maxDate.indexOf('-') !== -1) {
        minDate = minDate.split('-')[1]
        maxDate = maxDate.split('-')[1]
      }
      dateRange = `${minDate}-${maxDate}月`
      if (minDate === maxDate) dateRange = `${minDate}月`
      if (!minDate && !maxDate) dateRange = ''
      data.mapTotal.dateRange = dateRange
    } else {
      data.mapTotal.dateRange = ''
    }
  })
}

const toggleSubmarket = ev => {
  console.log(ev)
  data.params.subMarket1 = ev
  data.params.country = ''
  initByAreaList(data.params, 'subMarket1')
  initBySubMarketList(data.params)
}

const changeSelectArea = ev => {
  data.params.area = ev
  data.params.country = ''
  data.params.subMarket1 = ''
  data.params.subMarket2 = ''
  initChartData(data.params)
}

const toggleAreaItem = ev => {
  data.params.subMarket1 = ''
  data.params.subMarket2 = ''
  if (data.chartData.some(s => s.name === ev)) {
    data.params.area = ev
    data.params.country = ''
    refMaps.value.selectArea(ev)
    initByAreaList(data.params)
  } else {
    data.params.country = ev
  }
  initBySubMarketList(data.params)
}
const rowStyle = ({ row }) => {
  return {
    'font-weight': row.area == data.params.country ? 'bold' : 'normal',
    'text-decoration': row.area == data.params.country ? 'underline' : '',
    'font-style': row.area == data.params.country ? 'italic' : ''
  }
}
// 首次加载请求处理好数据后发起
// initChartData(data.params)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.page-content {
  padding: 0px 8px;
  min-height: calc(100vh - 1.5rem);
}

.map-wrap {
  position: relative;
}

.sales {
  position: absolute;
  top: 20px;
  left: 30px;
  z-index: 2;
  display: flex;
  &__item {
    position: relative;
    min-width: 200px;

    height: 100px;
    padding: 20px;
    margin-right: 20px;
    border-radius: 8px;
    border: 1px solid #92cdfc;
    background: linear-gradient(288deg, #c6e0f9 2%, #fafbfc 97%);
    backdrop-filter: blur(13.6px);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    &--icon {
      position: absolute;
      top: -25px;
      right: -20px;
      width: 100px;
      height: 100px;
    }
    &--name {
      color: #051c2c;
      font-size: 14px;
    }
    &--value {
      color: #0085ff;
      font-size: 24px;
      font-weight: bold;
    }
  }
}
.area-item {
  min-width: 100%;
  cursor: pointer;
}
</style>
