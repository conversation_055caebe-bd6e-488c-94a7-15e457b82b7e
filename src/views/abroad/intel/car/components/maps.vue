<template>
  <el-card>
    <div class="ratio-width" style="padding-bottom: 71%">
      <div ref="target" class="ratio-width__wrap" />
    </div>
  </el-card>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted } from 'vue'
import echartsResize from '@/utils/hooks/echartsResize.js'
import yuchaiGeo from '@/utils/common/yuchaiGeo.json'
import { numberFormat } from '../../../../../utils/format'

const emit = defineEmits(['select'])

const props = defineProps({
  seriesData: {
    type: Array,
    required: true,
    default: () => []
  }
})
const data = reactive({
  currentSelect: '' // 当前选中的省
})

watch(
  () => props.seriesData,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const target = ref(null)
let currentMapName = 'world'
onMounted(() => {
  echarts.registerMap(currentMapName, yuchaiGeo)
  myChart = echarts.init(target.value)
  renderEcharts()
  // 监听 select 事件
  myChart.on('click', function (params) {
    if (currentMapName === 'world') {
      const countryName = params.name
      let fieldName = ''
      if (countryName === '中国') {
        // 判断点击的是中国
        fieldName = 'china'
      } else if (countryName === '蒙古') {
        fieldName = 'monggolia'
      } else if (countryName === '美国') {
        fieldName = 'America'
      }
      fetch(`src/utils/common/${fieldName}.json`)
        .then(res => res.json())
        .then(chinaData => {
          echarts.registerMap(fieldName, chinaData)
          currentMapName = fieldName
          renderEcharts()
        }) // 加载中国地图
    }
    data.currentSelect = params.name === data.currentSelect ? '' : params.name
    emit('select', data.currentSelect)
  })
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)
})

// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  let visualMapMax = 3000
  if (props.seriesData?.length > 0) {
    const arrayList = JSON.parse(JSON.stringify(props.seriesData))
    arrayList.sort((a, b) => a.value - b.value)
    if (arrayList.pop().value) visualMapMax = arrayList.pop().value + 100
  }
  let option = {
    tooltip: {
      trigger: 'item', // 触发类型，'item' 表示鼠标悬浮到图形上时触发
      formatter: function (params) {
        return `${params.name}:${numberFormat(params.value, 0)}`
      }
    },
    // dataRange
    visualMap: {
      show: true,
      min: 0,
      max: visualMapMax,
      text: ['高', '低'],
      realtime: true,
      calculable: true,
      color: ['#4FA1E9', '#D7F9FE']
    },
    backgroundColor: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: '#D6EBFA' // 0% 处的颜色
        },
        {
          offset: 1,
          color: '#FFFFFF' // 100% 处的颜色
        }
      ],
      global: false // 缺省为 false
    },
    geo: [
      {
        map: currentMapName,
        z: 0,
        top: '21.3%',
        silent: true,
        layoutSize: '100%', //保持地图宽高比
        itemStyle: {
          borderColor: '#66edff',
          borderWidth: 1,
          shadowBlur: 20,
          shadowColor: '#4d99ff',
          areaColor: '#1752ad',
          shadowOffsetX: 0,
          shadowOffsetY: 8
        }
      }
    ],
    series: [
      {
        name: '',
        type: 'map',
        map: currentMapName,
        z: 10,
        layoutSize: '100%', //保持地图宽高比
        label: {
          show: true, // 显示省份名称或数量
          formatter: function (params) {
            return `${params.name}:${numberFormat(params.value, 0)}`
          },
          fontSize: 12,
          color: '#333'
        },
        itemStyle: {
          show: true,
          borderColor: '#9ABAC9', // 省份边界线颜色
          borderWidth: 1 // 省份边界线宽度
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            color: '#fff'
          },
          itemStyle: {
            areaColor: '#00A9F4',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowBlur: 20,
            borderWidth: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        select: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            color: '#fff'
          },
          itemStyle: {
            areaColor: '#00A9F4',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowBlur: 20,
            borderWidth: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: props.seriesData
      }
    ]
  }
  myChart.setOption(option)
}
const selectArea = ev => {
  data.currentSelect = ev
  myChart.dispatchAction({
    type: 'select',
    name: ev
  })
}
defineExpose({
  selectArea
})
</script>
