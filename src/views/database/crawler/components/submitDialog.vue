<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body v-if="visible">
    <el-form
      ref="configRef"
      :model="data.queryParams"
      :rules="data.rules"
      :disabled="dailogstatus === 'query'"
      label-width="100px"
    >
      <el-form-item label="网站地址" style="width: 100%" prop="webUrl">
        <el-input v-model="data.queryParams.webUrl" placeholder="请输入网站地址" clearable />
      </el-form-item>
      <el-form-item label="网站中文名" style="width: 100%" prop="webNameCn">
        <el-input v-model="data.queryParams.webNameCn" placeholder="请输入网站中文名" clearable />
      </el-form-item>
      <el-form-item label="板块" style="width: 100%" prop="sector">
        <el-input v-model="data.queryParams.sector" placeholder="请输入板块" clearable />
      </el-form-item>
      <el-form-item label="关键词" prop="keyword" style="width: 100%">
        <el-input
          v-model="data.queryParams.keyword"
          placeholder="请输入关键词"
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark" style="width: 100%">
        <el-input
          v-model="data.queryParams.remark"
          placeholder="请输入备注"
          type="textarea"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="dailogstatus !== 'query'" @click="submitForm"
          >提交</el-button
        >
        <el-button type="info"  @click="cancel" class="bi-button">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import { apiPost } from '@/api/database/crawlerConfig'
import { toRaw } from 'vue'

const emit = defineEmits()
const { proxy } = getCurrentInstance()
const store = useStore()

const data = reactive({
  dicts: [],
  queryParams: {
    webUrl: '',
    webNameCn: undefined,
    keyword: undefined,
    remark: '',
    sector: '',
  },
  rules: {
    webUrl: [{ required: true, message: '网站地址不能为空', trigger: 'blur' }],
    webNameCn: [{ required: true, message: '网站中文名不能为空', trigger: 'blur' }],
    keyword: [{ required: true, message: '关键词不能为空', trigger: 'blur' }]
  }
})
const visible = ref(false)
const title = ref('')
const dailogstatus = ref('')
const loading = ref(false)
store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})

/** 详情按钮操作 */
function show(params, row) {
  visible.value = true
  title.value = '上传图片'

  dailogstatus.value = params
  if (params === 'add') {
    visible.value = true
    title.value = '新增标签'
    resetForm()
  } else if (params === 'edit') {
    title.value = '修改标签'
    data.queryParams = JSON.parse(JSON.stringify(toRaw(row)))
  } else {
    title.value = '详情标签'
    data.queryParams = JSON.parse(JSON.stringify(toRaw(row)))
  }
}
/** 取消按钮 */
function cancel() {
  visible.value = false
}
function resetForm() {
  data.queryParams = {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    matchKey: undefined,
    sensitiveKey: undefined
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['configRef'].validate(valid => {
    if (valid) {
      loading.value = true
      apiPost(data.queryParams, dailogstatus.value)
        .then(response => {
          loading.value = false
          proxy.$modal.msgSuccess(response.msg)
          visible.value = false
          emit('ok')
        })
        .catch(() => {
          loading.value = false
          proxy.$modal.msgError(response.msg)
        })
    }
  })
}

defineExpose({
  show
})
</script>
