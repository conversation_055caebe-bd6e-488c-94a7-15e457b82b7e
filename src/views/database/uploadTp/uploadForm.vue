<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body v-if="visible">
    <el-form ref="configRef" :model="queryParams" :rules="rules" :disabled="dailogstatus === 'query'" label-width="100px">
      <el-form-item label="信息模块" style="width: 100%" prop="dictModuleId">
        <el-select v-model="queryParams.dictModuleId" placeholder="信息模块" clearable>
          <el-option v-for="dict in dictCode.dictModuleId" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="左侧菜单" style="width: 100%" prop="dictLeftMenuId">
        <el-select v-model="queryParams.dictLeftMenuId" placeholder="左侧菜单" clearable>
          <el-option v-for="dict in dictLeftMenuId_opt" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="新闻种类" style="width: 100%" prop="dictNewsId">
        <el-select v-model="queryParams.dictNewsId" placeholder="新闻种类" clearable>
          <el-option v-for="dict in dictNewsId_opt" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="关键字" prop="keyWord" style="width: 100%">
        <el-input v-model="queryParams.keyWord" placeholder="关键字" clearable />
      </el-form-item>
      <el-form-item label="图片" prop="" style="width: 100%">
        <FileUpload @form_data="handleForm"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="dailogstatus !== 'query'" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup >
import FileUpload from './FileUpload/index.vue'
import * as upload from '@/api/database/upload'
import * as dictCode from './dict'
const emit = defineEmits()
const { proxy } = getCurrentInstance()
const modelValue1 = ref({})

const data = reactive({
  queryParams: {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '01',
  },
  dictLeftMenuId_opt: [],
  dictNewsId_opt: [],

  rules: {
    dictModuleId: [
      { required: true, message: '公告标题不能为空', trigger: 'change' },
    ],
    dictLeftMenuId: [
      { required: true, message: '公告标题不能为空', trigger: 'change' },
    ],
    dictNewsId: [
      { required: true, message: '公告标题不能为空', trigger: 'change' },
    ],
    keyWord: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
  },
})

const visible = ref(false)
const title = ref('')
const dailogstatus = ref('')
const { queryParams, dictLeftMenuId_opt, dictNewsId_opt, rules } = toRefs(data)
const loading = ref(false)
/** 详情按钮操作 */
function show(params, row) {
  visible.value = true
  title.value = '上传图片'

  dailogstatus.value = params
  if (params !== 'add') {
    queryParams.value = JSON.parse(JSON.stringify(row))
  } else {
    resetForm()
  }
}
/** 取消按钮 */
function cancel() {
  visible.value = false
}
function resetForm() {
  queryParams.value = {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType:'01'
  }
}
function handleForm(val){  
  modelValue1.value = val
}
/** 提交按钮 */
function submitForm() {
  if(Object.keys(modelValue1.value).length === 0) { 
    proxy.$modal.msgError('必须上传图片')
    return

  }
  proxy.$refs['configRef'].validate((valid) => {
    if (valid) {
      loading.value = true
      upload
        .addupdate({...queryParams.value,...modelValue1.value}, dailogstatus.value)
        .then((response) => {
          loading.value = false
          proxy.$modal.msgSuccess(response.msg)
          visible.value = false
          emit('ok')
        })
        .catch(() => {
          loading.value = false
          proxy.$modal.msgError(response.msg)
        })
    }
  })
}
watch(
  ()=>queryParams.value,
  (row)=>{
    if(row){
      // console.log('object :>> ', row);
      // console.log('object :>> ', queryParams.value);
      if (row.dictModuleId === '竞争环境') {
      dictLeftMenuId_opt.value = dictCode.dictLeftMenuId1
      // queryParams.value .dictLeftMenuId =  dictCode.dictLeftMenuId1[0].value
      if (row.dictLeftMenuId === '宏观环境') {
        dictNewsId_opt.value = dictCode.dictNewsId1
        // queryParams.value .dictNewsId =  dictCode.dictNewsId1[0].value
      }
      if (row.dictLeftMenuId === '市场环境') {
        dictNewsId_opt.value = dictCode.dictNewsId2
        // queryParams.value.dictNewsId =  dictCode.dictNewsId1[0].value
      }
    }
    if (row.dictModuleId === '竞争对手') {
      dictLeftMenuId_opt.value = dictCode.dictLeftMenuId2
      // queryParams.value .dictLeftMenuId =  dictCode.dictLeftMenuId2[0].value
      if (row.dictLeftMenuId === '商用车' || row.dictLeftMenuId === '新能源') {
        dictNewsId_opt.value = dictCode.dictNewsId3
        // queryParams.value.dictNewsId =  dictCode.dictNewsId3[0].value
      }
      if (row.dictLeftMenuId === '通机' || row.dictLeftMenuId === '船电') {
        dictNewsId_opt.value = dictCode.dictNewsId4
        // queryParams.value .dictNewsId =  dictCode.dictNewsId3[0].value
      }
    }
    if (row.dictModuleId === '主机厂客户' || row.dictModuleId === '海外客户') {
      dictLeftMenuId_opt.value = dictCode.dictLeftMenuId3
      dictNewsId_opt.value = dictCode.dictNewsId
      // queryParams.value .dictLeftMenuId =  dictCode.dictLeftMenuId3[0].value
      // queryParams.value .dictNewsId =  dictCode.dictNewsId[0].value
    }
    }
  },


  {
    immediate: true, // 组件挂载时立即执行
    deep: true, // 深度观察
  }
)

defineExpose({
  show,
})
</script>