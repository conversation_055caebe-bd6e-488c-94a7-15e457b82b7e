<template>
  <div class="table-list">
    <el-form :model="data.queryParams" ref="queryRef" label-width="0" class="table-list__search">
      <el-row :gutter="16">
        <linkage :dicts="data.dicts" :form="data.queryParams" :show-label="false" :span="3" />
        <el-col :span="3">
          <el-form-item prop="keyWord">
            <el-input
              v-model="data.queryParams.keyWord"
              placeholder="关键字"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item prop="daterange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              clearable
              filterable
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placeholder="请输入起始年月"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table-list__control">
      <el-button
        type="primary"
        icon="Plus"
        @click="handleUpload"
        v-hasPermi="['database:uploadTp:Import']"
        >上传</el-button
      >
    </div>
    <el-table v-loading="loading" height="100%" :data="data.list" class="table-list__content">
      <el-table-column type="index" width="40" />
      <el-table-column label="信息模块" min-width="100" prop="dictModuleId" />
      <el-table-column label="左侧菜单" min-width="100" prop="dictLeftMenuId" />
      <el-table-column label="新闻种类" min-width="100" prop="dictNewsId" />
      <el-table-column label="关键字" min-width="100" prop="keyWord" />
      <el-table-column label="图片" min-width="100" prop="fileName">
        <template #default="scope">
          <el-button
            text
            @click="handleExport(scope.row)"
            v-hasPermi="['monitor:operlog:export']"
            >{{ scope.row.fileName }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="创建人" min-width="100" prop="createorId" />
      <el-table-column label="创建时间" min-width="150" prop="createTime" />
      <el-table-column label="修改人" min-width="100" prop="updaterId" />
      <el-table-column label="修改时间" min-width="150" prop="updateTime" />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="scope">
          <div class="table-list__content--control">
            <el-button
              type="primary"
              text
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['database:uploadTp:edit']"
              >修改</el-button
            >
            <el-button
              type="danger"
              text
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['database:uploadTp:remove']"
              >删除</el-button
            >
            <el-button
              type="primary"
              text
              icon="Tickets"
              @click="handleDetail(scope.row)"
              v-hasPermi="['database:uploadTp:detail']"
              >查看</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <BiPagination
      :total="data.total"
      v-model:page="data.queryParams.pageNum"
      v-model:limit="data.queryParams.pageSize"
      @pagination="getList"
    />
    <uploadForm ref="modaluploadForm" @ok="handleQuery" />
  </div>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import BiPagination from '@/views/components/BiPagination.vue'

import * as upload from '@/api/database/upload'
import uploadForm from './uploadForm.vue'

const { proxy } = getCurrentInstance()
const dateRange = ref([])
const loading = ref(true)

const store = useStore()
const data = reactive({
  dicts: [],
  queryParams: {
    pageNum: 1,
    pageSize: 15,
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '01'
  },
  list: [],
  total: 0
})

/** 查询列表 */
function getList() {
  loading.value = true
  if (dateRange.value && dateRange.value.length > 0) {
    var dateRangeDate = Object.assign({}, data.queryParams, {
      s_date: dateRange.value[0],
      e_date: dateRange.value[1]
    })
  } else {
    var dateRangeDate = Object.assign({}, data.queryParams)
  }
  upload
    .list(proxy.addDateRange(dateRangeDate))
    .then(response => {
      loading.value = false
      if (response.code === 200) {
        data.list = response.rows
        data.total = response.total
      } else {
        proxy.$modal.msgError(response.msg)
      }
    })
    .catch(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  data.queryParams.pageNum = 1
  getList()
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除该数据项？')
    .then(function () {
      upload.del(row.id).then(response => {
        loading.value = false
        if (response.code === 200) {
          getList()
          proxy.$modal.msgSuccess(response.msg)
        } else {
          proxy.$modal.msgError(response.msg)
        }
      })
    })
    .then(() => {
      // proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {})
}
/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  data.queryParams = {
    pageNum: 1,
    pageSize: 15,
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '01'
  }
  handleQuery()
}

/** 上传按钮操详情作 */
function handleUpload() {
  proxy.$refs['modaluploadForm'].show('add')
}
/** 详情按钮操作 */
function handleDetail(row) {
  upload.detail(row.id).then(response => {
    loading.value = false
    if (response.code === 200) {
      proxy.$refs['modaluploadForm'].show('query', response.data)
    } else {
      proxy.$modal.msgError(response.msg)
    }
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  proxy.$refs['modaluploadForm'].show('edit', row)
}
/** 导出按钮操作 */
function handleExport(row) {
  proxy.download(
    'upload/download',
    {
      fileName: row.fileName,
      fileKey: row.fileKey
    },
    row.fileName
  )
}

store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})
getList()
</script>
