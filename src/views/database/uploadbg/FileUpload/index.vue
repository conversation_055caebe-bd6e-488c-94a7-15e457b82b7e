<template>
  <el-upload
    :action="uploadFileUrl"
    :before-upload="handleBeforeUpload"
    :file-list="fileList"
    :limit="props.limit"
    :on-error="handleUploadError"
    :on-exceed="handleExceed"
    :on-success="handleUploadSuccess"
    :headers="headers"
    :show-file-list="true"
    class="upload-file-uploader"
    ref="upload"
    style="width: 100%"
  >
    <el-button type="primary" style="width: 100%">上传</el-button>
  </el-upload>
</template>

<script setup>
import { genFileId } from 'element-plus'
import { getToken } from '@/utils/auth'
import { watch } from 'vue'

const props = defineProps({
  // 数量限制
  limit: {
    type: Number,
    default: 1
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5000
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['doc', 'txt', 'docx', 'pdf']
  },
  fileList: {
    type: Array,
    default: () => []
  }
})
const upload = ref(null)
const { proxy } = getCurrentInstance()
const emit = defineEmits()
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/upload/upload') // 上传的图片服务器地址
const headers = ref({ Authorization: 'Bearer ' + getToken() })
const fileList = ref([])
watch(
  props.fileList,
  val => {
    fileList.value = val
  },
  {
    immediate: true,
    deep: true
  }
)
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    let fileExtension = ''
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
    }
    const isTypeOk = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true
      if (fileExtension && fileExtension.indexOf(type) > -1) return true
      return false
    })
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`)
      return false
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  return true
}

// 文件个数超出
function handleExceed(files) {
  if (props.limit > 1) {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
    return
  }
  upload.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  upload.value.handleStart(file)
  upload.value.submit()
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError('上传失败')
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code == '200') {
    proxy.$modal.msgSuccess('上传成功')
    if (props.limit > 1) {
      fileList.value.push({ name: res.data.url, url: res.data.url })
    } else {
      fileList.value = [{ name: res.data.url, url: res.data.url }]
    }

    emit('form_data', { fileKey: res.data.name, url: res.data.url, fileName: file.name })
  } else {
    proxy.$modal.msgError(res.msg)
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
