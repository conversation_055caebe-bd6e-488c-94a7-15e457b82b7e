<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body v-if="visible">
    <el-form
      ref="configRef"
      :model="data.queryParams"
      :rules="data.rules"
      :disabled="dailogstatus === 'query'"
      label-width="100px"
    >
      <linkage :dicts="data.dicts" :form="data.queryParams" :xs="24" :sm="24" :md="24" />
      <el-form-item label="关键字" prop="keyWord" style="width: 100%">
        <el-input v-model="data.queryParams.keyWord" placeholder="关键字" clearable />
      </el-form-item>
      <!-- <el-form-item label="选择人员" prop="userIdList" style="width: 100%">
        <el-select
          v-model="data.queryParams.userIdList"
          multiple
          placeholder="请搜索人员"
          :remote-method="remoteMethod"
          :loading="loading"
          remote-show-suffix
          @change="handleChange"
        >
          <template #header>
            <el-input v-model="input"  placeholder="请输入人员" @input="debouncedRemoteMethod" />
          </template>
          <el-option
            v-for="item in userOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="报告" prop="" style="width: 100%">
        <FileUpload @form_data="handleForm" :fileList="data.fileList" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="dailogstatus !== 'query'" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import FileUpload from './FileUpload/index.vue'
import * as upload from '@/api/database/upload'
import { listUser } from '@/api/system/user'
import { debounce } from 'lodash';

const emit = defineEmits()
const { proxy } = getCurrentInstance()
const store = useStore()
const modelValue1 = ref({})
const userOptions = ref([])
const data = reactive({
  dicts: [],
  queryParams: {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '02',
    userIdList: []
  },
  fileList: [],
  rules: {
    dictModuleId: [{ required: true, message: '信息模块不能为空', trigger: 'blur' }],
    dictLeftMenuId: [
      { required: true, message: '左侧菜单不能为空，请先选择信息模块', trigger: 'blur' }
    ],
    dictNewsId: [
      { required: true, message: '新闻种类不能为空，请先选择左侧菜单', trigger: 'blur' }
    ],
    keyWord: [{ required: true, message: '关键字不能为空', trigger: 'blur' }],
    // userIdList: [{ required: true, message: '请选择人员', trigger: 'change' }]
  }
})
store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})
const visible = ref(false)
const title = ref('')
const dailogstatus = ref('')
const loading = ref(false)
const input = ref('')
/** 详情按钮操作 */
function show(params, row) {
  visible.value = true
  title.value = '上传报告'

  dailogstatus.value = params
  if (params !== 'add') {
    const queryParams = JSON.parse(JSON.stringify(row))
    data.queryParams = queryParams
    // { fileKey: res.data.name, url: res.data.url, fileName: file.name }

    data.fileList = [
      { name: queryParams.fileKey, url: queryParams.url ?? '', fileName: queryParams.fileName }
    ]
    modelValue1.value = [
      { name: queryParams.fileKey, url: queryParams.url ?? '', fileName: queryParams.fileName }
    ]
  } else {
    data.fileList = []
    modelValue1.value = []
    resetForm()
    input.value = ''
  }
}
/** 取消按钮 */
function cancel() {
  visible.value = false
}
function resetForm() {
  data.queryParams = {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '02',
    userIdList: []
  }
}
function handleForm(val) {
  modelValue1.value = val
}
/** 提交按钮 */
function submitForm() {
  if (Object.keys(modelValue1.value).length === 0) {
    proxy.$modal.msgError('必须上传报告!')
    return
  }
  proxy.$refs['configRef'].validate(valid => {
    if (valid) {
      loading.value = true
      upload
        .addupdate({ ...data.queryParams, ...modelValue1.value }, dailogstatus.value)
        .then(response => {
          loading.value = false
          proxy.$modal.msgSuccess(response.msg)
          visible.value = false
          emit('ok')
        })
        .catch(() => {
          loading.value = false
          proxy.$modal.msgError(response.msg)
        })
    }
  })
}
// 移除人员
const handleChange = (value) => {
  input.value = '';
}


defineExpose({
  show
})

</script>
