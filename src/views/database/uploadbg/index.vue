<template>
  <div class="table-list">
    <el-form :model="data.queryParams" ref="queryRef" label-width="0" class="table-list__search">
      <el-row :gutter="16">
        <linkage :dicts="data.dicts" :form="data.queryParams" :show-label="false" :span="3" />
        <el-col :span="3">
          <el-form-item prop="keyWord">
            <el-input
              v-model="data.queryParams.keyWord"
              placeholder="关键字"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item prop="daterange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              clearable
              filterable
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placeholder="请输入起始年月"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <div class="search-form__button">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table-list__control">
      <el-button type="primary" icon="Plus" @click="handleUpload">上传</el-button>
    </div>
    <el-table v-loading="loading" :data="data.list" class="table-list__content">
      <el-table-column type="index" width="40" />
      <el-table-column label="信息模块" min-width="100" prop="dictModuleName" />
      <el-table-column label="左侧菜单" min-width="100" prop="dictLeftMenuName" />
      <el-table-column label="新闻种类" min-width="100" prop="dictNewsName" />
      <el-table-column label="关键字" min-width="100" prop="keyWord" />
      <el-table-column label="报告" min-width="140" prop="fileName">
        <template #default="scope">
          <el-button text @click="handleExport(scope.row)">{{ scope.row.fileName }}</el-button>
        </template>
        <!--<template #default="scope">
          <el-button
            text
            @click="handleExport(scope.row)"
            v-hasPermi="['database:uploadbg:export']"
            >{{ scope.row.fileName }}</el-button
          >
        </template>-->
      </el-table-column>
      <!--<el-table-column label="创建人" min-width="100" prop="createorId" />-->
      <el-table-column label="创建时间" min-width="150" prop="createTime" />
      <!-- <el-table-column label="修改人" min-width="100" prop="updaterId" />
      <el-table-column label="修改时间" min-width="150" prop="updateTime" /> -->

      <el-table-column label="操作" fixed="right" width="350">
        <template #default="scope">
          <div class="table-list__content--control">
            <el-button type="primary" text icon="Edit" @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button type="danger" text icon="Delete" @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button type="primary" text icon="Tickets" @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button type="primary" text icon="User" @click="handleUser(scope.row)"
              >分配</el-button
              
            >
            
          </div>
        </template>
      </el-table-column>
    </el-table>
    <BiPagination
      :total="data.total"
      v-model:page="data.queryParams.pageNum"
      v-model:limit="data.queryParams.pageSize"
      @pagination="getList"
    />
    <uploadForm ref="modaluploadForm" @ok="handleQuery" />
    <authUser  ref="modalauthUser" :drawer="showDrawer" :row="nowRow" :id="nowRow.id" @handleClose="handleClose"/>
  </div>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import BiPagination from '@/views/components/BiPagination.vue'
import * as upload from '@/api/database/upload'
import uploadForm from './uploadForm.vue'
// 导入分配用户信息组件
import authUser from './authUser.vue'

const store = useStore()

const { proxy } = getCurrentInstance()
const loading = ref(true)
const showDrawer = ref(false);
const nowRow = ref({});
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 15,
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '02'
  },
  list: [],
  total: 0
})
const dateRange = ref([])

/** 查询列表 */
function getList() {
  loading.value = true
  if (dateRange.value && dateRange.value.length > 0) {
    var dateRangeDate = Object.assign({}, data.queryParams, {
      s_date: dateRange.value[0],
      e_date: dateRange.value[1]
    })
  } else {
    var dateRangeDate = Object.assign({}, data.queryParams)
  }
  upload
    .list(proxy.addDateRange(dateRangeDate))
    .then(response => {
      loading.value = false
      if (response.code === 200) {
        data.list = response.rows
        data.total = response.total
      } else {
        proxy.$modal.msgError(response.msg)
      }
    })
    .catch(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  data.queryParams.pageNum = 1
  getList()
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除该数据项？')
    .then(function () {
      upload.del(row.id).then(response => {
        loading.value = false
        if (response.code === 200) {
          getList()
          proxy.$modal.msgSuccess(response.msg)
        } else {
          proxy.$modal.msgError(response.msg)
        }
      })
    })
    .then(() => {
      // proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {})
}
/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  data.queryParams = {
    pageNum: 1,
    pageSize: 15,
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    keyWord: undefined,
    fileType: '02'
  }
  handleQuery()
}

/** 上传按钮操详情作 */
function handleUpload() {
  proxy.$refs['modaluploadForm'].show('add')
}
/** 详情按钮操作 */
function handleDetail(row) {
  upload.detail(row.id).then(response => {
    loading.value = false
    if (response.code === 200) {
      proxy.$refs['modaluploadForm'].show('query', response.data)
    } else {
      proxy.$modal.msgError(response.msg)
    }
  })
}
/** 分配按钮操作 */
function handleUser(row) {
  console.log('row',row)
    // console.log(proxy.$refs['modalauthUser'])
    // proxy.$refs['modalauthUser'].show('edit', row)
    showDrawer.value = true;
    nowRow.value = row;
}
function handleClose() {
  console.log('handleClose')
    showDrawer.value = false;
    // getList();
}
/** 修改按钮操作 */
function handleUpdate(row) {
  proxy.$refs['modaluploadForm'].show('edit', row)
}
/** 导出按钮操作 */
function handleExport(row) {
  proxy.download(
    'upload/download',
    {
      fileName: row.fileName,
      fileKey: row.fileKey
    },
    row.fileName
  )
}

store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})
getList()
</script>
