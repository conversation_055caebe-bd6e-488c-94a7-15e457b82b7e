<template>
  <div class="table-list">
    <el-form :model="queryParams" ref="queryRef" label-width="0" class="table-list__search">
      <el-row :gutter="16">
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="functionKey">
            <el-input v-model="queryParams.filterKeyword" placeholder="功能关键字"  />
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item>
            <div class="search-form__button">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table-list__control">
      <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['database:label:add']"
        >新增
      </el-button>
    </div>
    <el-table v-loading="loading" height="100%" :data="data.list" class="table-list__content">
      <el-table-column type="index" width="40" />
      <el-table-column
        label="过滤关键词"
        min-width="140"
        prop="filterKeyword"
        show-overflow-tooltip
      />
      <!-- <el-table-column
        label="备注"
        min-width="140"
        prop="remark"
        show-overflow-tooltip
      /> -->
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="scope">
          <div class="table-list__content--control">
            <el-button
              type="primary"
              text
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['database:label:edit']"
              >修改</el-button
            >
            <el-button
              type="danger"
              text
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['database:label:remove']"
              >删除</el-button
            >
            <el-button
              type="primary"
              text
              icon="Tickets"
              @click="handleDetail(scope.row)"
              v-hasPermi="['database:label:detail']"
              >查看</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <BiPagination
      :total="data.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <submitDialog ref="modaluploadForm" @ok="handleQuery" />
  </div>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import BiPagination from '@/views/components/BiPagination.vue'
import submitDialog from './components/submitDialog.vue'

import * as label from '@/api/database/crawlerFilter'

const { proxy } = getCurrentInstance()
const dateRange = ref([])

const store = useStore()

const data = reactive({
  dicts: [],
  queryParams: {
    pageNum: 1,
    pageSize: 15,
    filterKeyword: '',
    fileType: '02'
  },
  list: [],
  total: 0
})
const loading = ref(true)

const { queryParams } = toRefs(data)

store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})

/** 查询列表 */
function getList() {
  loading.value = true
  // if(dateRange.value && dateRange.value.length >0){
  //   var dateRangeDate = Object.assign({},queryParams.value,{
  //     s_date:dateRange.value[0],  e_date :dateRange.value[1]
  //   })

  // }else{
  //   var dateRangeDate = Object.assign({},queryParams.value)
  // }
  label
    .list(proxy.addDateRange(queryParams.value))
    .then(response => {
      loading.value = false
      if (response.code === 200) {
        data.list = response.rows
        data.total = response.total
      } else {
        proxy.$modal.msgError(response.msg)
      }
    })
    .catch(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除该数据项？')
    .then(function () {
      label.del(row.id).then(response => {
        loading.value = false
        if (response.code === 200) {
          getList()
          proxy.$modal.msgSuccess(response.msg)
        } else {
          proxy.$modal.msgError(response.msg)
        }
      })
    })
    .then(() => {
      // proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {})
}
/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    filterKeyword: '',
    fileType: '02'
  }
  handleQuery()
}

/** 新增按钮操详情作 */
function handleAdd() {
  proxy.$refs['modaluploadForm'].show('add')
}
/** 详情按钮操作 */
function handleDetail(row) {
  label.detail(row.id).then(response => {
    loading.value = false
    if (response.code === 200) {
      proxy.$refs['modaluploadForm'].show('query', response.data)
    } else {
      proxy.$modal.msgError(response.msg)
    }
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  proxy.$refs['modaluploadForm'].show('edit', row)
}
/** 导出按钮操作 */
function handleExport(row) {
  proxy.download(
    'tag/export',
    {
      fileName: row.fileName,
      fileKey: row.fileKey
    },
    row.fileName
  )
}

getList()
</script>
