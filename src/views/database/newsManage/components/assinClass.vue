<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`分配栏目,以选择${ids.length || 0}项`"
    width="500"
    :before-close="handleClose"
  >
    <el-select
      v-model="iisSysNewsTagIds"
      placeholder="所属栏目"
      clearable
      multiple
      style="width: 100%"
      filterable
    >
      <el-option
        v-for="item in props.newTypeDicts"
        :key="item?.value"
        :label="item?.label"
        :value="item?.value"
      />
    </el-select>
    <template #footer>
      <div class="dialog-footer" >
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleBatchAssignKind(false)"
          :loading="loading.assign"
        >
          仅分配栏目
        </el-button>
        <el-button 
          type="primary" 
          @click="handleBatchAssignKind(true)"
          :loading="loading.publish"
        >
          分配栏目发布
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- <div class="assinClass">
    <div class="title">
      <span>已选{{ props.ids.length }} 项</span>
      <span class="right">
        <el-button 
          type="primary" 
          size="mini" 
          @click="dialogVisible = true"
          :disabled="props.ids.length === 0"
        >
          添加
        </el-button>
      </span>
    </div>
  </div> -->
</template>

<script setup>
import { ref } from 'vue';
import { batchAssignKind } from '@/api/intelligence/newsinfo.js'
import { ElMessage } from 'element-plus'

const props = defineProps({
  newTypeDicts: {
    type: Array,
    default: () => []
  },
  ids: {
    type: Array,
    default: () => []
  }
})

const iisSysNewsTagIds = ref([])
const dialogVisible = defineModel({ require: true })

const loading = ref({
  assign: false,
  publish: false
})

const handleClose = (done) => {
  dialogVisible.value = false
}

// 处理分配栏目
const handleBatchAssignKind = (isPublish) => {
  if (iisSysNewsTagIds.value.length === 0) {
    ElMessage.warning('请选择栏目')
    return
  }

  const loadingKey = isPublish ? 'publish' : 'assign'
  loading.value[loadingKey] = true
  
  batchAssignKind({
    iisSysNewsTagId: iisSysNewsTagIds.value.join(','),
    ids: props.ids.map(item => item.id),
    handType: isPublish ? 1 : 0 // 1表示发布，0表示不发布
  }).then(() => {
    ElMessage.success(isPublish ? '分配栏目并发布成功' : '分配栏目成功')
    emit('success')
    handleCancel();
  }).catch(() => {
    ElMessage.error('操作失败')
  }).finally(() => {
    loading.value[loadingKey] = false
  })
}

const emit = defineEmits(['success'])

const handleCancel = () => {
  iisSysNewsTagIds.value = []
  dialogVisible.value = false
}
</script>
