<template>
  <el-dialog
    v-model="modelValue"
    :close-on-click-modal="false"
    :title="isEdit ? `新增新闻` : `编辑新闻`"
    top="2vh"
    width="80vw"
    append-to-body
  >
    <el-form :model="data.params" :rules="data.rules" ref="formRef" label-width="80">
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item prop="title" label="标题">
            <el-input v-model="data.params.title" placeholder="标题" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="text" label="文本内容">
            <el-input
              v-model="data.params.text"
              :rows="10"
              type="textarea"
              placeholder="文本内容"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="html" label="页面内容">
            <!-- <wangeditor v-model="data.params.html"  /> -->
            <div style="width: 100%; height: 160px; overflow: auto;border: 1px solid #c0c4cc; border-radius: 8px;padding: 6px 10px;" v-html="data.params.html"></div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="time" label="发布时间">
            <el-date-picker
              v-model="data.params.time"
              type="date"
              placeholder="发布时间"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="url" label="来源">
            <el-input v-model="data.params.url" placeholder="来源" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="iisSysNewsTagId" label="所属栏目">
            <el-select
              v-model="data.params.iisSysNewsTagId"
              placeholder="所属栏目"
              clearable
              multiple
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in props.newTypeDicts"
                :key="item?.value"
                :label="item?.label"
                :value="item?.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="publishStatus" label="状态">
            <el-select v-model="data.params.publishStatus" placeholder="状态" style="width: 100%">
              <el-option label="草稿" :value="0" />
              <el-option label="已发布" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="isEdit" type="primary" @click="addNewsInfo" :loading="loading.submit"
          >提交</el-button
        >
        <el-button v-if="!isEdit" type="primary" @click="updateNewsInfo" :loading="loading.submit"
          >修改</el-button
        >
        <el-button @click="modelValue = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessageBox } from 'element-plus'
import wangeditor from '@/views/components/wangeditor/index.vue'
import { addNews, updateNews } from '@/api/intelligence/newsinfo.js'
import { getNewsById } from '@/api/intelligence/newsinfo.js'
const emit = defineEmits(['success'])
const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      id: ''
    })
  },
  newTypeDicts: {
    type: Array,
    default: () => []
  }
})
const modelValue = defineModel({ require: true })
const formRef = ref(null)
const data = reactive({
  params: {
    id: '',
    publishStatus: 0 // 默认设置为草稿状态
  },
  rules: {
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    text: [{ required: true, message: '文本内容不能为空', trigger: 'blur' }],
    time: [{ required: true, message: '发布时间不能为空', trigger: 'change' }],
    url: [{ required: true, message: '来源不能为空', trigger: 'blur' }],
    iisSysNewsTagId: [{ required: true, message: '所属栏目不能为空', trigger: 'change' }],
    publishStatus: [{ required: true, message: '请选择状态', trigger: 'change' }]
  },
  newTypeDicts: []
})
const loading = reactive({
  submit: false
})
const isEdit = computed(() => {
  return !props.data.id
})
watch(
  () => props.data,
  val => {
    data.params = {
      id: '',
      url: '',
      title: '',
      text: '',
      html: '',
      dictModuleId: '', // 前端展示用
      dictLeftMenuId: '', // 前端展示用
      iisSysNewsTagId: undefined,
      time: '',
      ...val
    }
    if (val.id) initDetails(val.id)
    if (formRef.value && formRef.value.resetFields) formRef.value.resetFields()
  },
  {
    immediate: true,
    deep: true
  }
)
async function initDetails(id) {
  const { code, data: resData } = await getNewsById({ id }).catch(e => e)
  if (code !== 200) return
  if (resData.iisSysNewsTagId) resData.iisSysNewsTagId = resData.iisSysNewsTagId.split(',')

  data.params = { ...resData }
}
async function addNewsInfo() {
  const valid = await formValidate(formRef)
  if (!valid) return
  if (loading.submit) return
  loading.submit = true
  const params = JSON.parse(JSON.stringify(data.params))
  params.iisSysNewsTagId = params.iisSysNewsTagId.join()
  const res = await addNews(params).catch(e => e)
  loading.submit = false
  ElMessageBox.alert(res.msg, '提示', {
    showClose: false,
    callback: () => {
      modelValue.value = false
      emit('success', true)
    }
  })
}

async function updateNewsInfo() {
  const valid = await formValidate(formRef)
  if (!valid) return
  if (loading.submit) return
  loading.submit = true
  const params = JSON.parse(JSON.stringify(data.params))
  params.iisSysNewsTagId = params.iisSysNewsTagId.join()
  const res = await updateNews(params).catch(e => e)
  loading.submit = false
  ElMessageBox.alert(res.msg, '提示', {
    showClose: false,
    callback: () => {
      modelValue.value = false
      emit('success', true)
    }
  })
}
function formValidate(ref) {
  return new Promise(resolve => {
    ref.value.validate(valid => {
      resolve(valid)
    })
  })
}
</script>
<style scoped lang="scss">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
