<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body v-if="visible">
    <el-form ref="configRef" :model="queryParams" :rules="rules" :disabled="dailogstatus === 'query'" label-width="110px">
      <el-form-item label="年月"  prop="yearMonths">
        <el-date-picker
            v-model="queryParams.yearMonths"
            type="month"
            style="width: 100%"
            clearable filterable
            value-format="YYYY-MM"
            placeholder=""
          />
      </el-form-item>
      <el-form-item label="GDP总量" style="width: 100%" prop="macroType">
        <el-select v-model="queryParams.macroType" placeholder="" clearable>
          <el-option
             v-for="dict in dictCode.macroType"
             :key="dict.value"
             :label="dict.label"
             :value="dict.value"
          />
       </el-select>
      </el-form-item>
      <el-form-item label="GDP同比" style="width: 100%" prop="macroSubType">
        <el-input v-model="queryParams.macroSubType" placeholder="" clearable />
      </el-form-item>
      <el-form-item label="GDP累计增速" prop="Number" style="width: 100%">
        <el-input v-model="queryParams.Number" placeholder="" clearable />
      </el-form-item>
     
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="dailogstatus === 'addByMacroaType' || dailogstatus === 'editByMacroaType'" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup >
import * as macro from "@/api/database/macro";
import * as dictCode from './dict'
const emit = defineEmits()
const { proxy } = getCurrentInstance()

const data = reactive({
  queryParams: {
    yearMonths: undefined,
    macroType: undefined,
    macroSubType: undefined,
    Number: undefined
  },
  dictLeftMenuId_opt: [],
  dictNewsId_opt: [],

  rules: {

    macroSubType: [
      { required: true, message: 'GDP同比不能为空', trigger: 'change' },
    ],
    Number: [{ required: true, message: 'GDP累计增速不能为空', trigger: 'blur' }],
  },
})

const visible = ref(false)
const title = ref('')
const dailogstatus = ref('')
const { queryParams,  rules } = toRefs(data)
const loading = ref(false)
/** 详情按钮操作 */
function show(params, row) {
  visible.value = true
  
  dailogstatus.value = params

  if (dailogstatus.value === 'addByMacroaType') {
    title.value = '新增GDP'
    resetForm()
  } else if(dailogstatus.value === 'editByMacroaType') {
    title.value = '修改GDP'
    queryParams.value = JSON.parse(JSON.stringify(row))
  }else{ 
    title.value = '详情GDP'
    queryParams.value = JSON.parse(JSON.stringify(row))
  }
}
/** 取消按钮 */
function cancel() {
  visible.value = false
}
function resetForm() {
  queryParams.value = {
    yearMonths: undefined,
    macroType: undefined,
    macroSubType: undefined,
    Number: undefined
  }
}

/** 提交按钮 */
function submitForm() {

  proxy.$refs['configRef'].validate((valid) => {
    if (valid) {
      loading.value = true
      macro
        .apiPost([queryParams.value], dailogstatus.value)
        .then((response) => {
          loading.value = false
          proxy.$modal.msgSuccess(response.msg)
          visible.value = false
          emit('ok')
        })
        .catch(() => {
          loading.value = false
          proxy.$modal.msgError(response.msg)
        })
    }
  })
}


defineExpose({
  show,
})
</script>