<template>
  <!-- 详情对话框 -->
  <el-dialog title="导入" v-model="visible" append-to-body :before-close="beforeClose">
    <el-form ref="refForm" :model="data.queryParams" :rules="data.rules" label-width="110px">
      <el-form-item label="宏观数据分类" style="width: 100%" prop="macroType">
        <el-select
          v-model="data.queryParams.macroType"
          @change="toggleMacroType"
          placeholder="请选择宏观数据分类"
        >
          <el-option
            v-for="dict in dictCode.macroType"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="小类分类" style="width: 100%" prop="macroSubType">
        <el-select v-model="data.queryParams.macroSubType" placeholder="请选择小类分类">
          <el-option
            v-for="dict in data.dictsMacroSubType"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
          <template #empty>请先选择宏观数据分类</template>
        </el-select>
      </el-form-item>
      <el-form-item prop="fileList" style="width: 100%">
        <template #label>
          <el-upload
            ref="refUpload"
            :action="data.upload.url"
            v-model:file-list="data.queryParams.fileList"
            :limit="1"
            :headers="data.upload.headers"
            :auto-upload="false"
            :show-file-list="false"
            :data="data.queryParams"
            :on-exceed="handleExceed"
            :on-success="onSuccess"
          >
            <template #trigger>
              <el-button type="primary">上传Excel</el-button>
            </template>
          </el-upload>
        </template>
        <div class="el-select__wrapper" style="width: 100%">
          <div v-for="i in data.queryParams.fileList">
            {{ i.name }}
          </div>
          <div
            v-if="data.queryParams.fileList.length === 0"
            style="color: #a8abb2; cursor: default"
          >
            请点击右侧上传按钮上传文件
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessageBox, ElMessage, genFileId } from 'element-plus'
import { getToken } from '@/utils/auth'
import * as dictCode from './dict'

import { getSubTypeBymacroType } from '@/api/database/macro'
import { nextTick } from 'vue'

const emit = defineEmits()
const refUpload = ref(null) // upload实例
const refForm = ref(null)
const data = reactive({
  dictsMacroSubType: [], // 小类分类值
  upload: {
    url: import.meta.env.VITE_APP_BASE_API + '/intelligence/macroData/importData',
    headers: { Authorization: 'Bearer ' + getToken() }
  },
  queryParams: {
    updateSupport: 1,
    macroSubType: '',
    macroType: '',
    fileList: []
  },
  rules: {
    macroType: [{ required: true, message: '宏观数据分类不能为空', trigger: 'change' }],
    macroSubType: [{ required: true, message: '小类分类不能为空', trigger: 'change' }],
    fileList: [{ required: true, message: '上传Excel不能为空', trigger: 'change' }]
  }
})
const loading = reactive({
  submit: false
})
const visible = ref(false)

/** 详情按钮操作 */
async function show() {
  visible.value = true
}
/** 取消按钮 */
function cancel() {
  beforeClose()
  visible.value = false
}

const beforeClose = async () => {
  refUpload.value.clearFiles()
  refForm.value.resetFields()
  await nextTick()
}

/** 提交按钮 */
function submitForm() {
  if (loading.submit) return
  refForm.value.validate(valid => {
    if (valid) {
      loading.submit = true
      refUpload.value.submit()
    }
  })
}

// 设置只能上传一个
const handleExceed = files => {
  refUpload.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  refUpload.value.handleStart(file)
}
const onSuccess = res => {
  if (res.code === 200) {
    cancel()
    ElMessageBox.alert(`${res.msg}`, {
      dangerouslyUseHTMLString: true
    })
  } else {
    beforeClose()
    ElMessage({
      message: `${res.code}-${res.msg}`,
      type: 'warning'
    })
  }
  loading.submit = false
}

// 获取小类分类
const toggleMacroType = async () => {
  const keys = await getSubTypeBymacroType({ macroType: data.queryParams.macroType }).catch(e => e)
  data.dictsMacroSubType = []
  data.queryParams.macroSubType = ''
  refForm.value.resetFields('macroSubType')
  Object.keys(keys.data).forEach(el => {
    data.dictsMacroSubType.push({
      label: keys.data[el],
      value: el
    })
  })
}
defineExpose({
  show
})
</script>
