<template>
  <div class="table-list">
    <el-form :model="data.queryParams" ref="queryRef" label-width="0" class="table-list__search">
      <el-row :gutter="16">
        <el-col :span="3">
          <el-form-item>
            <el-select v-model="data.queryParams.macroType" placeholder="宏观数据分类" clearable>
              <el-option
                v-for="dict in dictCode.macroType"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item prop="daterange">
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              clearable
              filterable
              range-separator="-"
              value-format="YYYY-MM"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placeholder="请输入起始年月"
              :disabledDate="disabledFeatureDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <div class="search-form__button">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table-list__control">
      <!-- <el-button
        type="primary"
        icon="Plus"
        @click="handleUpload"
        v-hasPermi="['database:cooperate:macro:Import']"
        >导入</el-button
      > -->
      <el-button
        type="primary"
        icon="Download"
        @click="handleExport"
        v-hasPermi="['database:cooperate:macro:export']"
        >下载</el-button
      >
    </div>
    <el-table v-loading="loading.table" height="100%" :data="data.list" class="table-list__content">
      <el-table-column label="年月" min-width="100" prop="yearMonths" />
      <el-table-column
        :label="item?.label"
        min-width="100"
        :prop="item?.prop"
        v-for="(item, index) in data.columns"
        :key="index"
      />
    </el-table>
    <BiPagination
      :total="data.total"
      v-model:page="data.queryParams.pageNum"
      v-model:limit="data.queryParams.pageSize"
      @pagination="getList"
    />
    <uploadForm ref="modaluploadForm" @ok="handleQuery" />
    <updateForm ref="modalForm" @ok="handleQuery" />
  </div>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import uploadForm from './uploadForm'
import updateForm from './updateForm.vue'
import { getSubTypeBymacroType, macroDateStatisticsList } from '@/api/database/macro'

import * as dictCode from './dict'
import formValidate from '@/utils/hooks/formValidate.js'
const { disabledFeatureDate } = formValidate()

const { proxy } = getCurrentInstance()
const dateRange = ref(dictCode?.initYearMonth)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    macroType: 'GDP'
  },
  list: [],
  total: 0,
  columns: []
})

const loading = reactive({
  table: false
})

// 设置表头
const setTableHeader = async () => {
  const keys = await getSubTypeBymacroType({ macroType: data.queryParams.macroType }).catch(e => e)
  data.columns = []
  Object.keys(keys.data).forEach(el => {
    data.columns.push({
      prop: el,
      label: keys.data[el]
    })
  })
}
/** 查询列表 */
const getList = async () => {
  if (loading.table) return
  loading.table = true
  await setTableHeader()
  if (dateRange.value && dateRange.value.length > 0) {
    var dateRangeDate = Object.assign({}, data.queryParams, {
      s_yearMonth: dateRange.value[0],
      e_yearMonth: dateRange.value[1]
    })
  } else {
    var dateRangeDate = Object.assign({}, data.queryParams)
  }
  macroDateStatisticsList(proxy.addDateRange(dateRangeDate))
    .then(response => {
      loading.table = false
      if (response.code === 200) {
        data.list = response.rows
        data.total = response.total
      } else {
        proxy.$modal.msgError(response.msg)
      }
    })
    .catch(() => {
      loading.table = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  data.queryParams.pageNum = 1

  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  data.dateRange = []
  data.queryParams = {
    pageNum: 1,
    pageSize: 10,
    macroType: 'GDP'
  }
  handleQuery()
}

/** 上传按钮操详情作 */
function handleUpload() {
  proxy.$refs['modaluploadForm'].show()
}

/** 导出按钮操作 */
function handleExport() {
  if (dateRange.value && dateRange.value.length > 0) {
    var dateRangeDate1 = Object.assign(
      {},
      { macroType: data.queryParams.macroType },
      {
        beginDate: dateRange.value[0],
        endDate: dateRange.value[1]
      }
    )
  } else {
    proxy.$modal.msgError('年月必须输入')
    return
  }

  let time = dateRange?.value.join('至') || ''
  proxy.downloadJSON(
    'intelligence/macroData/export',
    {
      ...dateRangeDate1
    },
    `${time}_${data.queryParams?.macroType || ''}_${new Date().getTime()}.xlsx`
  )
}

getList()
</script>
