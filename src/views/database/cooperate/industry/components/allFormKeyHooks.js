import { ref } from 'vue'
export default function useFormKeyHooks() {
  // 中内协
  const znx = ref([
    {
      label: '日期范围',
      key: 'yearMonth',
      databaseKey: '',
      disabled: false
    },
    {
      label: '企业简称',
      key: 'companyAbbr',
      databaseKey: '',
      disabled: false
    },
    {
      label: '板块',
      key: 'sector',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销量/本月完成',
      key: 'salesVolume',
      databaseKey: 'salesVolume',
      disabled: false
    },
    {
      label: '缸数',
      key: 'cylinderCount',
      databaseKey: '',
      disabled: false
    },
    {
      label: '细分市场/用途',
      key: 'segmentedMarket',
      databaseKey: '',
      disabled: false
    },

    {
      label: '发动机厂家/企业简称2',
      key: 'engineManufacturer',
      databaseKey: '',
      disabled: false
    }
  ])

  // 海关数
  const hgs = ref([
    {
      label: '日期范围',
      key: 'dataRange',
      disabled: false
    },
    {
      label: '商品编码',
      key: 'productCodeArr',
      databaseKey: 'productCode',
      disabled: false
    },
    {
      label: '商品编码释义',
      key: 'productDescArr',
      databaseKey: 'productDesc',
      disabled: false
    },
    {
      label: '商品名称',
      key: 'productName',
      disabled: false
    },
    {
      label: '二手车',
      key: 'usedCar',
      disabled: false
    },
    {
      label: '法定数量',
      key: 'legalQuantity',
      disabled: false
    },
    {
      label: '法定单位',
      key: 'legalUnit',
      disabled: false
    },
    {
      label: '制造商',
      key: 'manufacturerArr',
      databaseKey: 'manufacturer',
      disabled: false
    },
    {
      label: '收发货企业名称',
      key: 'consignorArr',
      databaseKey: 'consignor',
      disabled: false
    },
    {
      label: '经营单位企业名称',
      key: 'operatorArr',
      databaseKey: 'operator',
      disabled: false
    },
    {
      label: '申报单位名称',
      key: 'declarant',
      disabled: false
    },
    {
      label: '车辆型号',
      key: 'vehicleModel',
      disabled: false
    },
    {
      label: '规格型号',
      key: 'specification',
      disabled: false
    },
    {
      label: '发动机厂商',
      key: 'engineManufacturer',
      disabled: false
    },
    {
      label: '发动机型号（可配）',
      key: 'engineModel',
      disabled: false
    },
    {
      label: '企业性质',
      key: 'enterpriseNature',
      disabled: false
    },
    {
      label: '成交方式',
      key: 'transactionMethod',
      disabled: false
    },
    {
      label: '单价',
      key: 'unitPrice',
      disabled: false
    },
    {
      label: '总价',
      key: 'totalPrice',
      disabled: false
    },
    {
      label: '境内货源地',
      key: 'sourceLocation',
      disabled: false
    },
    {
      label: '包装形式',
      key: 'packageType',
      disabled: false
    },
    {
      label: '贸易国别',
      key: 'tradeCountry',
      disabled: false
    },
    {
      label: '最终目的国',
      key: 'finalDestinationArr',
      databaseKey: 'finalDestination',
      disabled: false
    },
    {
      label: '贸易方式',
      key: 'tradeMode',
      disabled: false
    },
    {
      label: '运输方式',
      key: 'transportMode',
      disabled: false
    },
    {
      label: '主管海关',
      key: 'customs',
      disabled: false
    },
    {
      label: '直属海关',
      key: 'directCustoms',
      disabled: false
    },
    {
      label: '指运港',
      key: 'destinationPort',
      disabled: false
    },
    {
      label: '进出口岸',
      key: 'importExportPort',
      disabled: false
    },
    {
      label: '车辆类别',
      key: 'vehicleCategory',
      disabled: false
    },
    {
      label: '车辆类别2',
      key: 'vehicleCategory2',
      disabled: false
    },
    {
      label: '排量（Ml）',
      key: 'displacementMl',
      disabled: false
    },
    {
      label: '总质量（Kg）',
      key: 'totalWeightKg',
      disabled: false
    },
    {
      label: '区域',
      key: 'region',
      disabled: false
    },
    {
      label: '海外办事处',
      key: 'overseasOffice',
      disabled: false
    },
    {
      label: '新大区',
      key: 'newBigRegionArr',
      databaseKey: 'newBigRegion',
      disabled: false
    },
    {
      label: '新国区',
      key: 'newLocalRegionArr',
      databaseKey: 'newLocalRegion',
      disabled: false
    },
    {
      label: '区域细分',
      key: 'regionSubdivision',
      disabled: false
    },
    {
      label: '区域细分2',
      key: 'regionSubdivision2Arr',
      databaseKey: 'regionSubdivision2',
      disabled: false
    },
    {
      label: '板块',
      key: 'sectorArr',
      databaseKey: 'sector',
      disabled: false
    },
    {
      label: '销售结构1',
      key: 'salesStructure1',
      disabled: false
    },
    {
      label: '玉柴相关',
      key: 'ycRelated',
      disabled: false
    },
    {
      label: '板块2',
      key: 'businessSegment2',
      disabled: false
    },
    {
      label: '销售结构2',
      key: 'salesStructure2Arr',
      databaseKey: 'salesStructure2',
      disabled: false
    },
    {
      label: '板块3',
      key: 'businessSegment3',
      disabled: false
    },
    {
      label: '销售结构3',
      key: 'salesStructure3Arr',
      databaseKey: 'salesStructure3',
      disabled: false
    },
    {
      label: '制造商整合',
      key: 'manufacturerNameArr',
      databaseKey: 'manufacturerName',
      disabled: false
    }
  ])

  // 装机数
  const zjs = ref([
    {
      label: '日期范围',
      key: 'dataRange',
      databaseKey: '',
      disabled: false
    },
    {
      label: '旬度',
      key: 'decade',
      databaseKey: '',
      disabled: false
    },
    {
      label: '所属大区',
      key: 'region',
      databaseKey: '',
      disabled: false
    },
    {
      label: '主机厂',
      key: 'manuArr',
      databaseKey: 'manufacturer',
      disabled: false
    },
    {
      label: '用途',
      key: 'purposeArr',
      databaseKey: 'purpose',
      disabled: false
    },
    {
      label: '整车车型',
      key: 'vehiclemodel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机厂家',
      key: 'engineArr',
      databaseKey: 'enginemanufacturer',
      disabled: false
    },
    {
      label: '燃料种类',
      key: 'fueltypeMain',
      databaseKey: '',
      disabled: false
    },
    {
      label: '机型',
      key: 'enginemodelArr',
      databaseKey: 'enginemodel',
      disabled: false
    },
    {
      label: '排放',
      key: 'emission',
      databaseKey: '',
      disabled: false
    },
    {
      label: '出口/国内',
      key: 'exportdomestic',
      databaseKey: '',
      disabled: false
    },
    {
      label: '板块',
      key: 'sectorArr',
      databaseKey: 'sector',
      disabled: false
    },
    {
      label: '装机数',
      key: 'installationcount',
      databaseKey: '',
      disabled: false
    },
    {
      label: '是否剔除统计',
      key: 'excludefromstatistics',
      databaseKey: '',
      disabled: false
    },
    {
      label: '季度',
      key: 'quarterArr',
      databaseKey: 'quarter',
      disabled: false
    },
    {
      label: '半年度',
      key: 'semiannualArr',
      databaseKey: 'semiannual',
      disabled: false
    },
    {
      label: '板块1',
      key: 'sector1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '细分市场二',
      key: 'submarket2Arr',
      databaseKey: 'submarket2',
      disabled: false
    },
    {
      label: '新用途1',
      key: 'newpurpose1Arr',
      databaseKey: 'newpurpose1',
      disabled: false
    },
    {
      label: '装机数量',
      key: 'installedquantity',
      databaseKey: '',
      disabled: false
    },
    {
      label: '自产/社会',
      key: 'selfproducedsocial',
      databaseKey: '',
      disabled: false
    },
    {
      label: '主机厂2',
      key: 'chassisActual2Arr',
      databaseKey: 'chassisActualProducer2',
      disabled: false
    },
    {
      label: '细分市场一',
      key: 'submarket1Arr',
      databaseKey: 'submarket1',
      disabled: false
    },
    {
      label: '燃料种类1',
      key: 'fueltype1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '机型1',
      key: 'enginemodel1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '机型系列2',
      key: 'enginemodelseries2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量',
      key: 'displacement',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围',
      key: 'displacementrange',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机厂家1',
      key: 'engineFacturer1Arr',
      databaseKey: 'engineManufacturer',
      disabled: false
    }
  ])

  // 上险数
  const sxs = ref([
    {
      label: '日期范围',
      key: 'dataRange',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销售区县',
      key: 'salesCounty',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆型号',
      key: 'vehicleModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '整车企业(公告)',
      key: 'manufacturerPublic',
      databaseKey: '',
      disabled: false
    },
    {
      label: '整车品牌',
      key: 'brand',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆类型',
      key: 'vehicleType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆名称',
      key: 'vehicleName',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车型俗称',
      key: 'modelNickname',
      databaseKey: '',
      disabled: false
    },
    {
      label: '驾驶室型号',
      key: 'cabModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车型大类',
      key: 'vehicleCategory',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车型分类',
      key: 'vehicleClassification',
      databaseKey: '',
      disabled: false
    },
    {
      label: '轴数',
      key: 'axleCount',
      databaseKey: 'axleCount',
      disabled: false
    },
    {
      label: '驱动轴数',
      key: 'driveAxleCount',
      databaseKey: 'driveAxleCount',
      disabled: false
    },
    {
      label: '轴距(mm)',
      key: 'wheelbase',
      databaseKey: 'wheelbase',
      disabled: false
    },
    {
      label: '车长(mm)',
      key: 'vehicleLength',
      databaseKey: 'vehicleLength',
      disabled: false
    },
    {
      label: '车宽(mm)',
      key: 'vehicleWidth',
      databaseKey: 'vehicleWidth',
      disabled: false
    },
    {
      label: '车高(mm)',
      key: 'vehicleHeight',
      databaseKey: 'vehicleHeight',
      disabled: false
    },
    {
      label: '货箱长(mm)',
      key: 'cargoLength',
      databaseKey: 'cargoLength',
      disabled: false
    },
    {
      label: '货箱宽(mm)',
      key: 'cargoWidth',
      databaseKey: 'cargoWidth',
      disabled: false
    },
    {
      label: '货箱高(mm)',
      key: 'cargoHeight',
      databaseKey: 'cargoHeight',
      disabled: false
    },
    {
      label: '总质量(kg)',
      key: 'totalWeight',
      databaseKey: 'totalWeight',
      disabled: false
    },
    {
      label: '整备质量(kg)',
      key: 'curbWeight',
      databaseKey: 'curbWeight',
      disabled: false
    },
    {
      label: '额定载质量(kg)',
      key: 'ratedLoadWeight',
      databaseKey: 'ratedLoadWeight',
      disabled: false
    },
    {
      label: '准牵引质量(kg)',
      key: 'maxTowingWeight',
      databaseKey: 'maxTowingWeight',
      disabled: false
    },
    {
      label: '底盘型号',
      key: 'chassisModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业(公告)',
      key: 'chassisManufacturerPublic',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业(公告)简称',
      key: 'chassisManufacturerShort',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业(实际生产）',
      key: 'chassisManufacturerActual',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机型号',
      key: 'engineModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '燃料种类',
      key: 'fuelType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量(ml)',
      key: 'displacement',
      databaseKey: 'displacement',
      disabled: false
    },
    {
      label: '气缸数',
      key: 'cylinderCount',
      databaseKey: 'cylinderCount',
      disabled: false
    },
    {
      label: '功率(kW)',
      key: 'power',
      databaseKey: 'power',
      disabled: false
    },
    {
      label: '变速器厂家',
      key: 'transmissionManufacturer',
      databaseKey: '',
      disabled: false
    },
    {
      label: '变速器型号',
      key: 'transmissionModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '变速器类型',
      key: 'transmissionType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '变速器速比',
      key: 'transmissionRatio',
      databaseKey: '',
      disabled: false
    },
    {
      label: '主减速比',
      key: 'mainReductionRatio',
      databaseKey: 'mainReductionRatio',
      disabled: false
    },
    {
      label: '微改区分',
      key: 'microModification',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销量',
      key: 'salesVolume',
      databaseKey: 'salesVolume',
      disabled: false
    },
    {
      label: '驱动形式',
      key: 'driveForm',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机厂全称',
      key: 'engineFactoryFullName',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排放',
      key: 'emission',
      databaseKey: '',
      disabled: false
    },
    {
      label: '欧系轻客',
      key: 'europeanLightTruck',
      databaseKey: '',
      disabled: false
    },
    {
      label: '柴气其他',
      key: 'dieselGasOthers',
      databaseKey: '',
      disabled: false
    },
    {
      label: '马力',
      key: 'horsepower',
      databaseKey: 'horsepower',
      disabled: false
    },
    {
      label: '系列',
      key: 'series',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机厂名称2',
      key: 'engineFactoryName2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '动力结构1',
      key: 'powerStructure1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '动力结构2',
      key: 'powerStructure2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销售省份-简',
      key: 'salesProvinceShort',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销售城市-简',
      key: 'salesCityShort',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新省份',
      key: 'newProvince',
      databaseKey: '',
      disabled: false
    },
    {
      label: '随改厂',
      key: 'modifiedFactory',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新场景3级',
      key: 'newScene3Level',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新场景2级',
      key: 'newScene2Level',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新场景1级',
      key: 'newScene1Level',
      databaseKey: '',
      disabled: false
    },
    {
      label: '品系',
      key: 'breed',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘实际生产企业2',
      key: 'chassisActualProducer2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '细分市场维度1.3',
      key: 'subMarketDimension13',
      databaseKey: '',
      disabled: false
    },
    {
      label: '品系2',
      key: 'breed2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '品系1.3',
      key: 'breed13',
      databaseKey: '',
      disabled: false
    },
    {
      label: '重中轻1.3',
      key: 'weightMidLight',
      databaseKey: '',
      disabled: false
    },
    {
      label: '细分市场维度1.4',
      key: 'subMarketDimension14',
      databaseKey: '',
      disabled: false
    }
  ])

  // 货运新增
  const hyxz = ref([
    {
      label: '日期范围',
      key: 'yearMonthDay',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销售区县',
      key: 'salesCounty',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆型号',
      key: 'vehicleModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '整车企业(公告)',
      key: 'manufacturerPublic',
      databaseKey: '',
      disabled: false
    },
    {
      label: '整车品牌',
      key: 'brand',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆类型',
      key: 'vehicleType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆名称',
      key: 'vehicleName',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车型俗称',
      key: 'modelNickname',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车型大类',
      key: 'vehicleCategory',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车型分类',
      key: 'vehicleClassification',
      databaseKey: '',
      disabled: false
    },
    {
      label: '轴数',
      key: 'axleCount',
      databaseKey: 'axleCount',
      disabled: false
    },
    {
      label: '驱动轴数',
      key: 'driveAxleCount',
      databaseKey: '',
      disabled: false
    },
    {
      label: '轴距(mm)',
      key: 'wheelbase',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车长(mm)',
      key: 'vehicleLength',
      databaseKey: 'vehicleLength',
      disabled: false
    },
    {
      label: '车宽(mm)',
      key: 'vehicleWidth',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车高(mm)',
      key: 'vehicleHeight',
      databaseKey: '',
      disabled: false
    },
    {
      label: '货箱长(mm)',
      key: 'cargoLength',
      databaseKey: 'cargoLength',
      disabled: false
    },
    {
      label: '货箱宽(mm)',
      key: 'cargoWidth',
      databaseKey: 'cargoWidth',
      disabled: false
    },
    {
      label: '货箱高(mm)',
      key: 'cargoHeight',
      databaseKey: '',
      disabled: false
    },
    {
      label: '总质量(kg)',
      key: 'totalWeight',
      databaseKey: 'totalWeight',
      disabled: false
    },
    {
      label: '整备质量(kg)',
      key: 'curbWeight',
      databaseKey: 'curbWeight',
      disabled: false
    },
    {
      label: '额定载质量(kg)',
      key: 'ratedLoadWeight',
      databaseKey: 'ratedLoadWeight',
      disabled: false
    },
    {
      label: '准牵引质量(kg)',
      key: 'maxTowingWeight',
      databaseKey: 'maxTowingWeight',
      disabled: false
    },
    {
      label: '轮胎数',
      key: 'tireCount',
      databaseKey: 'tireCount',
      disabled: false
    },
    {
      label: '底盘型号',
      key: 'chassisModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业(公告)',
      key: 'chassisManufacturerPublic',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业(公告)简称',
      key: 'chassisManufacturerShort',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业(实际生产）',
      key: 'chassisManufacturerActual',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机型号',
      key: 'engineModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '燃料种类',
      key: 'fuelType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量(ml)',
      key: 'displacement',
      databaseKey: 'displacement',
      disabled: false
    },
    {
      label: '气缸数',
      key: 'cylinderCount',
      databaseKey: 'cylinderCount',
      disabled: false
    },
    {
      label: '功率(kW)',
      key: 'power',
      databaseKey: 'power',
      disabled: false
    },
    {
      label: '马力',
      key: 'horsepower',
      databaseKey: 'horsepower',
      disabled: false
    },
    {
      label: '销量',
      key: 'salesVolume',
      databaseKey: 'salesVolume',
      disabled: false
    },
    {
      label: '日',
      key: 'day',
      databaseKey: '',
      disabled: false
    },
    {
      label: '驱动形式',
      key: 'driveForm',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机厂全称',
      key: 'engineFactoryFullName',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排放',
      key: 'emission',
      databaseKey: '',
      disabled: false
    },
    {
      label: '欧系轻客',
      key: 'europeanLightTruck',
      databaseKey: '',
      disabled: false
    },
    {
      label: '柴气其他',
      key: 'dieselGasOthers',
      databaseKey: '',
      disabled: false
    },
    {
      label: '系列',
      key: 'series',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机厂名称2',
      key: 'engineFactoryName2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '动力结构1',
      key: 'powerStructure1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '动力结构2',
      key: 'powerStructure2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销售省份-简',
      key: 'salesProvinceShort',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销售城市-简',
      key: 'salesCityShort',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新省份',
      key: 'newProvince',
      databaseKey: '',
      disabled: false
    },
    {
      label: '随改厂',
      key: 'modifiedFactory',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新场景3级',
      key: 'newScene3Level',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新场景2级',
      key: 'newScene2Level',
      databaseKey: '',
      disabled: false
    },
    {
      label: '新场景1级',
      key: 'newScene1Level',
      databaseKey: '',
      disabled: false
    },
    {
      label: '品系',
      key: 'breed',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘实际生产企业2',
      key: 'chassisActualProducer2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '细分市场维度1.3',
      key: 'subMarketDimension13',
      databaseKey: '',
      disabled: false
    },
    {
      label: '品系2',
      key: 'breed2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '品系1.3',
      key: 'breed13',
      databaseKey: '',
      disabled: false
    },
    {
      label: '重中轻1.3',
      key: 'weightMidLight',
      databaseKey: '',
      disabled: false
    },
    {
      label: '细分市场维度1.4',
      key: 'subMarketDimension14',
      databaseKey: '',
      disabled: false
    }
  ])

  // 船电数
  const cds = ref([
    {
      label: '日期范围',
      key: 'dataRange',
      disabled: false
    },
    {
      label: '发动机厂家',
      key: 'engineArr',
      databaseKey: 'engineManufacturer',
      disabled: false
    },
    {
      label: '细分市场一',
      key: 'subMarketArr',
      databaseKey: 'segmentedMarket',
      disabled: false
    },
    {
      label: '销量',
      key: 'salesVolume',
      databaseKey: 'salesVolume',
      disabled: false
    },
    {
      label: '板块',
      key: 'sector',
      databaseKey: '',
      disabled: false
    }
  ])

  // 流向数
  const lxs = ref([
    {
      label: '日期范围',
      key: 'dataRange',
      disabled: false
    },
    {
      label: '省',
      key: 'province',
      databaseKey: '',
      disabled: false
    },
    {
      label: '市',
      key: 'city',
      databaseKey: '',
      disabled: false
    },
    {
      label: '经销商',
      key: 'dealer',
      databaseKey: '',
      disabled: false
    },
    {
      label: '车辆名称',
      key: 'vehicleName',
      databaseKey: '',
      disabled: false
    },
    {
      label: '发动机',
      key: 'engine',
      databaseKey: '',
      disabled: false
    },
    {
      label: '缸数',
      key: 'cylinderCount',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量',
      key: 'displacement',
      databaseKey: 'displacement',
      disabled: false
    },
    {
      label: '马力',
      key: 'horsepower',
      databaseKey: 'horsepower',
      disabled: false
    },
    {
      label: '燃料种类',
      key: 'fuelType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '底盘企业',
      key: 'chassisManufacturer',
      databaseKey: '',
      disabled: false
    },
    {
      label: '系列',
      key: 'series',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排放',
      key: 'emissionStandard',
      databaseKey: '',
      disabled: false
    },
    {
      label: '总质量',
      key: 'totalWeight',
      databaseKey: 'totalWeight',
      disabled: false
    },
    {
      label: '发动机厂家',
      key: 'engineManufacturer',
      databaseKey: '',
      disabled: false
    },
    {
      label: '产品用途',
      key: 'productUse',
      databaseKey: '',
      disabled: false
    },
    {
      label: '厂家名称统一',
      key: 'manufacturerName',
      databaseKey: '',
      disabled: false
    }
  ])


  // 友商数
  const yss = ref([
    {
      label: '日期范围',
      key: 'dataRange',
      databaseKey: '',
      disabled: false
    },
    {
      label: '厂家',
      key: 'engineManufacturer',
      databaseKey: '',
      disabled: false
    },
    {
      label: '季度',
      key: 'quarter',
      databaseKey: '',
      disabled: false
    },
    {
      label: '机型',
      key: 'engineModel',
      databaseKey: '',
      disabled: false
    },
    {
      label: '机型1',
      key: 'engineModel1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '机型N',
      key: 'engineModelN',
      databaseKey: '',
      disabled: false
    },
    {
      label: '板块2',
      key: 'sector2',
      databaseKey: '',
      disabled: false
    },
    {
      label: '板块1',
      key: 'sector',
      databaseKey: '',
      disabled: false
    },
    {
      label: '销量',
      key: 'salesVolume',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围1',
      key: 'displacementRange1',
      databaseKey: '',
      disabled: false
    },
    {
      label: '功率范围-船电',
      key: 'powerRangeShipElectric',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围-四段',
      key: 'displacementRange4Segments',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围-四段3.8L',
      key: 'displacementRange38l',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围-升段',
      key: 'displacementRangeUp',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围-四段3.7L',
      key: 'displacementRange4Segments37l',
      databaseKey: '',
      disabled: false
    },
    {
      label: '排量范围-四段3.7L及机组',
      key: 'displacementRange37lUnits',
      databaseKey: '',
      disabled: false
    },
    {
      label: '实销/预测',
      key: 'actualSalesForecast',
      databaseKey: '',
      disabled: false
    },
    {
      label: '出口标识',
      key: 'exportIndicator',
      databaseKey: '',
      disabled: false
    },
    {
      label: '出口手工分',
      key: 'manualExportSplit',
      databaseKey: '',
      disabled: false
    },
    {
      label: '柴汽',
      key: 'fuelType',
      databaseKey: '',
      disabled: false
    },
    {
      label: '缸数2',
      key: 'cylinderCount2',
      databaseKey: '',
      disabled: false
    }])
  return { znx, hgs, zjs, sxs, hyxz, cds, lxs, yss }
}
