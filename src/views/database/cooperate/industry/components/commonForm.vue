<template>
  <el-form :model="table.queryParams" ref="refForm" label-width="0" class="table-list__search">
    <el-row :gutter="16" style="margin-right: unset">
      <template v-for="(value, key) in table.formJson">
        <el-col v-if="key === 'dataRange' || key === 'yearMonth'" :xs="16" :sm="16" :md="6">
          <el-form-item :prop="key">
            <el-date-picker
              v-model="table.queryParams[key]"
              type="monthrange"
              range-separator="至"
              start-placeholder="起始年月"
              end-placeholder="结束年月"
              value-format="YYYY-MM"
              :disabledDate="disabledFeatureDate"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col v-else :xs="8" :sm="8" :md="3">
          <el-form-item :prop="key">
            <el-input v-model="table.queryParams[key]" :placeholder="value" clearable />
          </el-form-item>
        </el-col>
      </template>
      <el-col :xs="8" :sm="8" :md="4">
        <el-form-item>
          <div class="search-form__button">
            <el-button type="primary" icon="Setting" circle @click="emit('setting', true)" />
            <el-button type="primary" icon="Search" @click="emit('search', true)">搜索</el-button>
            <el-button icon="Refresh" @click="emit('reset', true)">重置</el-button>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import formValidate from '@/utils/hooks/formValidate.js'

const emit = defineEmits(['reset', 'search', 'setting'])
const refForm = ref(null) // form对象
const table = inject('table')
table.refForm = refForm
const { disabledFeatureDate } = formValidate()
</script>
<style scoped lang="scss">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
