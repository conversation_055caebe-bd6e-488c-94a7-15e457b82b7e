<template>
  <!-- 装机数 -->
  <div class="table-list">
    <commonForm
      @reset="resetSearch"
      @search="handleSearch"
      @setting="table.select.flag = !table.select.flag"
    />
    <div class="table-list__control">
      <el-button type="primary" icon="Plus" @click="handleImport">导入</el-button>
      <el-button type="primary" icon="Download" @click="toggleDownloadList">下载</el-button>
    </div>
    <el-table
      v-loading="loading.table"
      height="100%"
      :data="table.list"
      class="table-list__content"
    >
      <template v-for="i in table.listColumn">
        <el-table-column :label="i.label" :prop="i.prop" show-overflow-tooltip />
      </template>
    </el-table>
    <BiPagination
      :total="table.total"
      v-model:page="table.queryParams.pageNum"
      v-model:limit="table.queryParams.pageSize"
      @pagination="getList"
    />
    <importExcel ref="refImport" url="/intelligence/install/asyncImportData" @success="getList" />
    <formSelectDialog v-model="table.select.flag" :allFormData="zjs" />
    <downloadListDialog
      v-model="table.download.flag"
      :params="{ module: '1' }"
      downloadUrl="intelligence/fileDownload/download"
    />
  </div>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import importExcel from './importExcel.vue'
import formSelectDialog from './formSelectDialog.vue'
import commonForm from './commonForm.vue'
import downloadListDialog from './downloadListDialog.vue'
// 数据库管理-行业数据-装机数列表
import { installList } from '@/api/database/business'

import useTableData from './tablehooks.js'
import useFormKeyHooks from './allFormKeyHooks.js'

const refImport = ref(null) // 上传对象
// 搜索参数
const defaultFormJson = {
  dataRange: '日期范围'
}
const { zjs } = useFormKeyHooks()

const {
  table,
  loading,
  getList,
  handleSearch,
  resetSearch,
  toggleDownloadList,
  downloadExcel,
  mixinsParamsJson,
  setListColumn
} = useTableData({
  api: installList,
  download: {
    url: 'intelligence/install/export',
    name: '装机数'
  }
})

/** 上传按钮操详情作 */
function handleImport() {
  refImport.value.show()
}

mixinsParamsJson(defaultFormJson)
setListColumn(zjs)
getList()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.table-list {
  // height: calc($bi-main-height - 60px);
  height: 100%;
  padding: 0;
  .table-list__content {
    border-radius: 8px;
  }
}
</style>
