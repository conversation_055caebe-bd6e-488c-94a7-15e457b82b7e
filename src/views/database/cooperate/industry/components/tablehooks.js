import { reactive, provide } from 'vue'
export default function useTableData({ api, download }) {
  const { proxy } = getCurrentInstance()
  // formJson = {
  //   dataRange: '日期范围'
  // }
  // select: {
  //     value: ['dataRange'] // 选中的搜索
  // }
  const yearMonthColumn = ['dataRange', 'yearMonth', 'yearMonthDay'] // 替换成年月显示列表的值
  const table = reactive({
    refForm: null,
    defaultParams: {
      pageNum: 1,
      pageSize: 10
    }, // 默认的搜索条件
    isArrayParamsKey: ['dataRange', 'yearMonth', 'yearMonthDay'], // 是数组的值
    formJson: {},
    queryParams: {},
    listColumn: [], // 列表展示的列
    list: [],
    total: 0,
    select: {
      flag: false,
      data: [],
      value: [] // 选中的搜索
    },
    download: {
      flag: false,
    }
  })
  provide('table', table)
  const loading = reactive({
    table: false
  })
  /** 查询列表 */
  const getList = async () => {
    if (loading.table) return
    loading.table = true
    const params = JSON.parse(JSON.stringify(table.queryParams))
    if (params.dataRange) {
      const val = params.dataRange
      if (val && val.length > 0) {
        const start = val[0].split('-')
        params.startYear = start[0]
        params.startMonth = start[1]
        const end = val[1].split('-')
        params.endYear = end[0]
        params.endMonth = end[1]
      } else {
        params.startYear = ''
        params.startMonth = ''
        params.endYear = ''
        params.endMonth = ''
      }
      delete params.dataRange
    }
    if (params.yearMonth) {
      const val = params.yearMonth
      if (val && val.length > 0) {
        params.s_yearMonth = val[0].replace('-', '')
        params.e_yearMonth = val[1].replace('-', '')
      } else {
        params.s_yearMonth = ''
        params.e_yearMonth = ''
      }
      delete params.yearMonth
    }
    if (params.yearMonthDay) {
      const val = params.yearMonthDay
      if (val && val.length > 0) {
        params.s_date = val[0]
        params.e_date = val[1]
      } else {
        params.s_date = ''
        params.e_date = ''
      }
      delete params.yearMonthDay
    }
    console.log('params', params)
    // const cleanObjParams = Object.fromEntries(
    //   Object.entries(params).filter(([key, value]) => value != null && value !== "")
    // );
    const res = await api(params).catch(e => e)
    if (res.code !== 200) {
      loading.table = false
      return
    }
    table.list = res.rows
    table.total = res.total
    loading.table = false
  }

  /** 搜索按钮操作 */
  const handleSearch = async () => {
    table.queryParams.pageNum = 1
    await getList()
  }

  /** 重置按钮操作 */
  function resetSearch() {
    if (table.refForm && table.refForm.resetFields) table.refForm.resetFields()
    if (table.queryParams.startYear) table.queryParams.startYear = ''
    if (table.queryParams.startMonth) table.queryParams.startMonth = ''
    if (table.queryParams.endYear) table.queryParams.endYear = ''
    if (table.queryParams.endMonth) table.queryParams.endMonth = ''
    if (table.queryParams.s_yearMonth) table.queryParams.s_yearMonth = ''
    if (table.queryParams.e_yearMonth) table.queryParams.e_yearMonth = ''
    handleSearch()
  }
  // 导出文件
  const downloadExcel = () => {
    if (table.queryParams.dataRange && table.queryParams.dataRange.length === 0) {
      proxy.$modal.msgError('年月必须输入')
      return
    }
    const params = JSON.parse(JSON.stringify(table.queryParams))
    let time = table.queryParams.dataRange.join('至') || ''
    proxy.downloadJSON(
      download.url,
      params,
      `${time}_${download.name}_${new Date().getTime()}.xlsx`
    )
  }

  // 点击下载显示下载列表
  function toggleDownloadList(ev) {
    table.download.flag = true
  }
  // 合并搜索参数进入queryParams
  function mixinsParams(params) {
    table.queryParams = { ...table.defaultParams, ...params }
  }
  function mixinsParamsJson(formJson) {
    const isArrayParamsKey = table.isArrayParamsKey
    const params = {}
    const selectValue = []
    for (let i in formJson) {
      if (isArrayParamsKey.indexOf(i) !== -1) {
        params[i] = []
      } else {
        params[i] = ''
      }
      selectValue.push(i)
    }
    table.queryParams = { ...table.defaultParams, ...params }
    table.formJson = formJson
    table.select.value = selectValue
  }
  // 设置列表展示的数据
  function setListColumn(list) {
    console.log('list', list)
    const listColumn = []
    list.value.forEach(el => {
      if (yearMonthColumn.indexOf(el.key) !== -1) {
        listColumn.push(
          ...[
            {
              label: '年份',
              prop: 'year'
            },
            {
              label: '月份',
              prop: 'month'
            }
          ]
        )
      } else {
        const prop = el.databaseKey ? el.databaseKey : el.key
        listColumn.push({
          label: el.label,
          prop
        })
      }
    })
    table.listColumn = listColumn
  }

  return {
    table,
    loading,
    getList,
    handleSearch,
    resetSearch,
    toggleDownloadList,
    downloadExcel,
    mixinsParams,
    mixinsParamsJson,
    setListColumn
  }
}
