<template>
  <!-- 详情对话框 -->
  <el-dialog :title="title" v-model="visible" append-to-body v-if="visible">
    <el-form
      ref="configRef"
      :model="data.queryParams"
      :rules="data.rules"
      :disabled="dailogstatus === 'query'"
      label-width="100px"
    >
      <el-form-item label="栏目名称" style="width: 100%" prop="newsSection">
        <el-input v-model="data.queryParams.newsSection" placeholder="请输入信息模块" clearable />
      </el-form-item>
      <linkage :dicts="data.dicts" :form="data.queryParams" :xs="24" :sm="24" :md="24" />

      <el-form-item label="排序" style="width: 100%" prop="orderNum">
        <el-input-number
          v-model="data.queryParams.orderNum"
          :step="1"
          step-strictly
          :min="0"
          :controls="false"
          placeholder="请输入数字，从小到大排序"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="信息种类关键字" prop="matchKey" style="width: 100%">
        <el-input
          v-model="data.queryParams.matchKey"
          placeholder="请输入信息种类关键字"
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="功能关键字" prop="functionKey" style="width: 100%">
        <el-input
          v-model="data.queryParams.functionKey"
          placeholder="请输入功能关键字"
          type="textarea"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-if="dailogstatus !== 'query'" @click="submitForm"
          >提交</el-button
        >
        <el-button type="info" @click="cancel" class="bi-button">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import linkage from '@/views/components/linkage.vue'
import { apiPost } from '@/api/database/labels'
import { toRaw } from 'vue'

const emit = defineEmits()
const { proxy } = getCurrentInstance()
const store = useStore()

const data = reactive({
  dicts: [],
  queryParams: {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    matchKey: undefined,
    sensitiveKey: undefined,
    newsSection: '',
    orderNum: undefined,
    functionKey: ''
  },
  rules: {
    newsSection: [{ required: true, message: '栏目名称不能为空', trigger: 'blur' }],
    orderNum: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
    dictModuleId: [{ required: true, message: '信息模块不能为空', trigger: 'blur' }],
    dictLeftMenuId: [
      { required: true, message: '左侧菜单不能为空，请先选择信息模块', trigger: 'blur' }
    ],
    dictNewsId: [
      { required: true, message: '新闻种类不能为空，请先选择左侧菜单', trigger: 'blur' }
    ],
    matchKey: [{ required: true, message: '关键字不能为空', trigger: 'blur' }]
  }
})
const visible = ref(false)
const title = ref('')
const dailogstatus = ref('')
const loading = ref(false)
store.dispatch('getNewsLinkageData').then(res => {
  data.dicts = res
})

/** 详情按钮操作 */
function show(params, row) {
  visible.value = true
  title.value = '上传图片'

  dailogstatus.value = params
  if (params === 'add') {
    visible.value = true
    title.value = '新增标签'
    resetForm()
  } else if (params === 'edit') {
    title.value = '修改标签'
    data.queryParams = JSON.parse(JSON.stringify(toRaw(row)))
  } else {
    title.value = '详情标签'
    data.queryParams = JSON.parse(JSON.stringify(toRaw(row)))
  }
}
/** 取消按钮 */
function cancel() {
  visible.value = false
}
function resetForm() {
  data.queryParams = {
    dictModuleId: undefined,
    dictLeftMenuId: undefined,
    dictNewsId: undefined,
    matchKey: undefined,
    sensitiveKey: undefined
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['configRef'].validate(valid => {
    if (valid) {
      loading.value = true
      apiPost(data.queryParams, dailogstatus.value)
        .then(response => {
          loading.value = false
          proxy.$modal.msgSuccess(response.msg)
          visible.value = false
          emit('ok')
        })
        .catch(() => {
          loading.value = false
          proxy.$modal.msgError(response.msg)
        })
    }
  })
}

defineExpose({
  show
})
</script>
