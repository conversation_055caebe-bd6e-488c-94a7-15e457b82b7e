<template>
  <div ref="chart" style="width: 100%; height: 300px;"></div>
</template>
 
<script setup>
import {ref, onMounted ,defineProps} from 'vue';
import * as echarts from 'echarts';
const animationDuration = 1000
const chart = ref(null);
const dataSource00 = ref('')
const props = defineProps({
  modulesData: {
    type: String,
    required: true,
  },
  dataSource: {
    type: String,
    required: true,
  }
});


watch(() => props.dataSource, (newVal, oldVal) => {
      // console.log('DOM 已更新',newVal);
      // 这里可以根据需要对父组件传递的值做出相应的处理
      dataSource00.value = newVal
      optionBiao(newVal)
      // onMounted()
    });

    const  optionBiao = (newVal) =>{
      const myChart = echarts.init(chart.value);
  const option = {
    // ECharts 配置项
    title: {
      left: "center",
      text: props.modulesData === 'car'?'柳汽卡车份额结构走势'+'('+newVal+')':
      props.modulesData === 'mechanical'?'近三年工业动力之杭叉动力结构走势'+'('+newVal+')':
      props.modulesData === 'agro'?'近三年农业机械之常州东风动力结构走势'+'('+newVal+')':''
      
      ,
      textStyle: {
       fontSize: 15
     },
    },
    tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  
  legend: {
    left: 'right',      // 放置在右侧
    top: 'bottom',  
    bottom: 0,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
         // 去除背景线
         splitLine: {
            show: false
        }

    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '玉柴',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#00A9F4'
      },
      data: [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      animationDuration
    },
    
    {
      name: '潍柴',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#5FCEFF'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      animationDuration
    },
    {
      name: '康明斯',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
          color: '#C2E7F2'
      },
      data:  [10, 23, 43, 44, 33, 44, 33,10, 23, 43, 44, 33],
      label: {
                show: true,
                position: 'top',
                formatter: function(params) {
                    // 计算总和
                    let total = 0;
                    for (let i = 0; i < option.series.length; i++) {
                        total += option.series[i].data[params.dataIndex];
                    }
                    return total.toFixed(2); // 保留两位小数
                }
            },
      animationDuration
    },
  ]
  };
 
  option && myChart.setOption(option);
     }
onMounted(() => {
  optionBiao(props.dataSource)
});
</script>
 
<style>
/* 你的样式 */
</style>