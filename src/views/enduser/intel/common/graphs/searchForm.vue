<template>
  <div>
      <el-form :model="queryParams" ref="queryRef" :inline="true">
          <el-form-item label="" style="width: 220px">
            <el-date-picker
              v-model="queryParams.daterange"
              type="monthrange"
              clearable filterable
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placeholder="请输入起始年月"
            />
          </el-form-item>
        <el-form-item label="" prop="quarter">
          <el-select v-model="queryParams.quarter" clearable filterable placeholder="季度" style="width: 100px">
            <el-option
              v-for="item in optionsquarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="" prop="manufacturer">
            <el-select v-model="queryParams.car" filterable placeholder="商用车" style="width: 100px">
              <el-option
                v-for="item in optionsCar"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="carvalue">
            <el-select v-model="queryParams.carvalue" placeholder="卡车" style="width: 100px">
              <el-option
                v-for="item in optionsCars"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
    
          <el-form-item label="" prop="value2">
            <el-select v-model="queryParams.value2" placeholder="细分市场二" style="width: 100px">
              <el-option
                v-for="item in optionsvalue2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
    
          <el-form-item label="" prop="dataSource">
            <el-select v-model="queryParams.dataSource" filterable placeholder="数据来源" style="width: 100px">
              <el-option
                v-for="item in optionsdataSource"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="motor ">
            <el-select v-model="queryParams.motor" clearable filterable placeholder="发动机厂家" style="width: 100px">
              <el-option
                v-for="item in optionsmotor"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="mainCar">
            <el-select v-model="queryParams.mainCar" clearable filterable placeholder="主机厂" style="width: 100px">
              <el-option
                v-for="item in optionsFacturer"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="fuel">
            <el-select v-model="queryParams.fuel" clearable filterable placeholder="燃料" style="width: 100px">
              <el-option
                v-for="item in optionsfuel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
    
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery" 
              >查询</el-button
            >
          </el-form-item>
        </el-form>
  </div>
</template>

<script setup>
import * as select from "./select"
import { ref,watch } from "vue";
const { proxy } = getCurrentInstance();
const router = useRouter();
const emit = defineEmits(['call-parent-method']);

const data = reactive({
queryParams: {
  daterange: undefined,
  car: '',
  mainCar: '东风柳汽',
  motor :undefined,
  fuel:undefined,
  // 数据来源
  dataSource:'上险数',
  // 季度
  quarter:undefined,
  carvalue:undefined,
  value2:undefined
},
});

const { queryParams } = toRefs(data);
// 板块
const optionsCar =  ref(select.optionsCar)
// 发动机厂家
const optionsmotor  =  ref(select.optionsmotor)
const optionsFacturer =  ref(select.optionsFacturer)
const optionsfuel =  ref(select.optionsfuel)
let optionsvalue2 = ref(select.optionsfuel2_1) 
// 数据来源
const optionsdataSource =  ref(select.optionsdataSource)
const optionsCars =  ref(select.optionsCars)
// 季度
const optionsquarter =  ref(select.optionsquarter)
let carname = ref('')
emit('handedataSource',queryParams.value.dataSource)
/** 搜索按钮操作 */
const handleQuery= () => {
}

const stopWatch = watch(queryParams.value,(newVal,oldVal)=>{
if(newVal.car === '商用车'){
    if(newVal.carvalue === '卡车'){  
      optionsvalue2.value = [
        {value:'牵引车',label:'牵引车'},
        {value:'中重载货',label:'中重载货'},
        {value:'中重自卸',label:'中重自卸'},
        {value:'中重专用',label:'中重专用'},
        {value:'轻卡',label:'轻卡'},
        {value:'皮卡',label:'皮卡'},

      ]


    }
    if(newVal.carvalue === '客车'){  
      optionsvalue2.value = select.optionsfuel2_1
    } 

}
if(newVal.car === '工程机械'){ 

}

if(newVal.car === '农业装备'){ 

}

})
const Tree = () =>{ 
let optTree = select.optTree
optTree.forEach(element => {
  // 商用车
  if(element.value === '0'){  
    element.children.forEach(element2 => {
      // 客车
      if(element2.value === '01'){  
        // 细分市场三
        console.log(element2.children,'客车',optionsvalue2)
        optionsvalue2 = element2.children
        // element2.children.forEach(element3 => {
          
        // });

      }
      // 卡车
      if(element2.id === '02'){  
        
      }
    });

  }
  
  
});
 


}
queryParams.value.car = localStorage.getItem('carname') || '';
watch(queryParams.value, (vals) => {
 emit('handedataSource',queryParams.value.dataSource)
 console.log(vals)
 const val = vals.car
if(val === '商用车'){  
  router.push("car");
  localStorage.setItem('carname', '商用车');

}
if(val === '工程机械'){  
  router.push("mechanical");
  localStorage.setItem('carname', '工程机械');
}
if(val === '农业装备'){  
  router.push("agro");
  localStorage.setItem('carname', '农业装备');
}
if(val === '船舶'){  
  router.push("ship");
  localStorage.setItem('carname', '船舶');
}
if(val === '发电动力'){  
  router.push("power");
  localStorage.setItem('carname', '发电动力');
}
if(val === '新能源'){  
  router.push("energy");
  localStorage.setItem('carname', '新能源');
}
  
});

</script>

<style lang="scss" scoped>
.card-header {
display: flex;
justify-content: space-between;
.header_title {
  font-size: 18px;
  line-height: 24px;
  color: #051c2c;
  font-weight: Regular;
}
}
.text_item {
display: flex;
justify-content: space-between;
.text_items {
  font-weight: 400;
  font-size: 16px;
  line-height: 40px;
  color: #051c2c;
  font-weight: Regular;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}
.text_data {
  text-align: right;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
  color: #9ba4ab;
  font-weight: Regular;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}
}
.el-form--inline .el-form-item{  
margin-right: 10px;
}
</style>
