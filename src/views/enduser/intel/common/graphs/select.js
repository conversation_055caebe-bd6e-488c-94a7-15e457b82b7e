// 板块
export const optionsCar = [
    {
      value: "商用车",
      label: "商用车",
    },
    {
      value: "工程机械",
      label: "工程机械",
    },
    {
      value: "农业装备",
      label: "农业装备",
    },
    {
      value: "船舶",
      label: "船舶",
    },
    {
      value: "发电动力",
      label: "发电动力",
    },
    {
      value: "新能源",
      label: "新能源",
    },
  ]
  // 主机厂
export const optionsFacturer = [
  {value:'重汽集团',label:'重汽集团'},
{value:'一汽解放（青岛）',label:'一汽解放（青岛）'},
{value:'福田戴姆勒',label:'福田戴姆勒'},
{value:'陕西重汽',label:'陕西重汽'},
{value:'一汽本部',label:'一汽本部'},
{value:'东风商用',label:'东风商用'},
{value:'东风柳汽',label:'东风柳汽'},
{value:'山西大运',label:'山西大运'},
{value:'北汽重型',label:'北汽重型'},
{value:'上汽红岩',label:'上汽红岩'},
{value:'东风新疆',label:'东风新疆'},
{value:'山西新能源',label:'山西新能源'},
{value:'江淮重卡',label:'江淮重卡'},
{value:'陕汽商用车',label:'陕汽商用车'},
{value:'联合卡车',label:'联合卡车'},
{value:'包头北奔',label:'包头北奔'},
{value:'三一重卡',label:'三一重卡'},
{value:'徐工汽车',label:'徐工汽车'},
{value:'吉利四川',label:'吉利四川'},
{value:'安徽华菱',label:'安徽华菱'},
{value:'重汽王牌',label:'重汽王牌'},
{value:'广汽日野',label:'广汽日野'},
{value:'东风华神',label:'东风华神'},
{value:'东风随专',label:'东风随专'},
{value:'现代商用',label:'现代商用'},
{value:'北汽福田',label:'北汽福田'},
{value:'江淮轻商',label:'江淮轻商'},
{value:'东风股份',label:'东风股份'},
{value:'庆铃汽车',label:'庆铃汽车'},
{value:'湖北大运',label:'湖北大运'},
{value:'湖北三环',label:'湖北三环'},
{value:'重汽海西',label:'重汽海西'},
{value:'山东五征',label:'山东五征'},
{value:'四川江淮',label:'四川江淮'},
{value:'程力汽车',label:'程力汽车'},
{value:'江铃汽车',label:'江铃汽车'},
{value:'四川南骏',label:'四川南骏'},
{value:'山东汽车',label:'山东汽车'},
{value:'成都大运',label:'成都大运'},
{value:'上汽大通',label:'上汽大通'},
{value:'潍柴新能源',label:'潍柴新能源'},
{value:'山东凯马',label:'山东凯马'},
{value:'中联重科',label:'中联重科'},
{value:'三一重起',label:'三一重起'},
{value:'郑州宇通',label:'郑州宇通'},
{value:'三一专汽',label:'三一专汽'},
{value:'安徽柳工',label:'安徽柳工'},
{value:'徐工重型',label:'徐工重型'},
{value:'襄阳旅行车',label:'襄阳旅行车'},
{value:'江西五十铃',label:'江西五十铃'},
{value:'唐骏欧铃',label:'唐骏欧铃'},
{value:'河南骏通',label:'河南骏通'},
{value:'南京金龙',label:'南京金龙'},
{value:'湖北三江万山',label:'湖北三江万山'},
{value:'钦州力顺',label:'钦州力顺'},
{value:'神河汽车',label:'神河汽车'},
{value:'福建金霸龙',label:'福建金霸龙'},
{value:'福田雷萨',label:'福田雷萨'},
{value:'南京汽车',label:'南京汽车'},
{value:'吉利江西',label:'吉利江西'},
{value:'福田多功能',label:'福田多功能'},
{value:'一汽红塔',label:'一汽红塔'},
{value:'长安跨越',label:'长安跨越'},
{value:'山东时风',label:'山东时风'},
{value:'北汽青岛',label:'北汽青岛'},
{value:'重庆长安',label:'重庆长安'},
{value:'北汽黄骅',label:'北汽黄骅'},
{value:'北京欧辉',label:'北京欧辉'},
{value:'北汽有限',label:'北汽有限'},
{value:'东风云汽',label:'东风云汽'},
{value:'长城汽车',label:'长城汽车'},
{value:'郑州日产',label:'郑州日产'},
{value:'河北中兴',label:'河北中兴'},
{value:'河北长安',label:'河北长安'},
{value:'丹东黄海',label:'丹东黄海'},
{value:'福建新龙马',label:'福建新龙马'},
{value:'航天凌河',label:'航天凌河'},
{value:'广东福迪',label:'广东福迪'},
{value:'山西成功',label:'山西成功'},
{value:'安徽猎豹',label:'安徽猎豹'},
{value:'洛阳中收',label:'洛阳中收'},
{value:'上海申沃',label:'上海申沃'},
{value:'安徽安凯',label:'安徽安凯'},
{value:'厦门金龙',label:'厦门金龙'},
{value:'上海万象',label:'上海万象'},
{value:'江淮客车',label:'江淮客车'},
{value:'中车时代',label:'中车时代'},
{value:'苏州金龙',label:'苏州金龙'},
{value:'厦门金旅',label:'厦门金旅'},
{value:'中通客车',label:'中通客车'},
{value:'江铃晶马',label:'江铃晶马'},
{value:'江西大乘',label:'江西大乘'},
{value:'江苏九龙',label:'江苏九龙'},
{value:'扬州亚星',label:'扬州亚星'},
{value:'亚星新能源',label:'亚星新能源'},
{value:'上海申龙',label:'上海申龙'},
{value:'河北长征',label:'河北长征'},
{value:'奇瑞万达客车',label:'奇瑞万达客车'},
{value:'东风特种车',label:'东风特种车'},
{value:'桂林客车',label:'桂林客车'},
{value:'少林客车',label:'少林客车'},
{value:'佛山飞驰',label:'佛山飞驰'},
{value:'上驰汽车',label:'上驰汽车'},
{value:'上饶客车',label:'上饶客车'},
{value:'上汽通用五菱',label:'上汽通用五菱'},
{value:'华晨鑫源',label:'华晨鑫源'},

]
export const optionsCars = [
  {
    value: "卡车",
    label: "卡车",
  },{
    value: "客车",
    label: "客车",
  }
]
export const optionsfuel = [
  {
    value: "柴油",
    label: "柴油",
  },{
    value: "气体",
    label: "气体",
  },{
    value: "其他",
    label: "其他",
  },
]
// 商用车-二级细分市场-查询条件
export const optionsfuel2_1 = [
  {
    value: "轻卡",
    label: "轻卡",
  },{
    value: "公路",
    label: "公路",
  },{
    value: "公交",
    label: "公交",
  },{
    value: "校车",
    label: "校车",
  }
]
// 商用车-二级细分市场-数据源
export const optionsdataSource = [
  {
    value: "上险数",
    label: "上险数",
  },{
    value: "货运新增",
    label: "货运新增",
  },
  // {
  //   value: "装机数",
  //   label: "装机数",
  // },{
  //   value: "海关数",
  //   label: "海关数",
  // },{
  //   value: "船电数",
  //   label: "船电数",
  // },{
  //   value: "中内协",
  //   label: "中内协",
  // },{
  //   value: "友商数",
  //   label: "友商数",
  // },{
  //   value: "发动机厂家与主机厂家",
  //   label: "发动机厂家与主机厂家",
  // }
]
export const optionsquarter = [
  {
    value: "月度",
    label: "月度",
  },{
    value: "月累",
    label: "月度",
  },{
    value: "季度",
    label: "季度",
  }

]
// 发动机厂
export const optionsmotor = [
  {value:'云内',label:'云内'},
  {value:'全柴',label:'全柴'},
  {value:'潍柴',label:'潍柴'},
  {value:'江铃',label:'江铃'},
  {value:'康明斯',label:'康明斯'},
  {value:'江淮汽车',label:'江淮汽车'},
  {value:'锡柴',label:'锡柴'},
  {value:'福田股份',label:'福田股份'},
  {value:'五十铃（中国）',label:'五十铃（中国）'},
  {value:'江西五十铃',label:'江西五十铃'},
  {value:'东风轻发',label:'东风轻发'},
  {value:'上柴',label:'上柴'},
  {value:'南京汽车',label:'南京汽车'},
  {value:'玉柴',label:'玉柴'},
  {value:'东安动力',label:'东安动力'},
  {value:'重庆小康',label:'重庆小康'},
  {value:'庆铃',label:'庆铃'},
  {value:'吉利四川',label:'吉利四川'},
  {value:'玉动',label:'玉动'},
  {value:'绵阳瑞擎',label:'绵阳瑞擎'},
  {value:'柳州五菱',label:'柳州五菱'},
  {value:'保定长城',label:'保定长城'},
  {value:'沈阳三菱',label:'沈阳三菱'},
  {value:'贵州航天圆通',label:'贵州航天圆通'},
  {value:'凯瑞动力',label:'凯瑞动力'},
  {value:'绵阳新晨',label:'绵阳新晨'},
  {value:'重汽',label:'重汽'},
  {value:'东风商用',label:'东风商用'},
  {value:'德国奔驰',label:'德国奔驰'},
  {value:'华菱汽车',label:'华菱汽车'},
  {value:'上菲红',label:'上菲红'},
  {value:'三一动力',label:'三一动力'},
  {value:'上海日野',label:'上海日野'},
  {value:'日本日野',label:'日本日野'},
  {value:'朝柴',label:'朝柴'},
  {value:'五十铃',label:'五十铃'},
  {value:'洛柴',label:'洛柴'},
  {value:'新柴',label:'新柴'},
  {value:'其他',label:'其他'},
  {value:'久保田',label:'久保田'},
  {value:'雷沃动力',label:'雷沃动力'},
  {value:'解放动力',label:'解放动力'},
  {value:'常柴',label:'常柴'},
  {value:'中国重汽',label:'中国重汽'},
  {value:'洋马',label:'洋马'},
  {value:'四达',label:'四达'},
  {value:'三菱',label:'三菱'},
  {value:'莱动',label:'莱动'},
  {value:'扬动',label:'扬动'},
  {value:'汉马',label:'汉马'},
  {value:'常发',label:'常发'},
  {value:'三一道依茨',label:'三一道依茨'},
  {value:'华丰',label:'华丰'},
  {value:'东康',label:'东康'},
  {value:'重康',label:'重康'},
  {value:'MTU',label:'MTU'},
  
]




// 上险数------商用车
export const optTree  = [
  // 第一次数据来源
  {
    value: "0",
    label: "商用车",
    // 板块
    children: [
      {
        value: "01",
        label: "客车",
        pId: "0",
        children: [
          {
            value: "001",
            label: "轻卡222",
          },
          {
            value: "002",
            label: "皮卡",
          },
          {
            value: "003",
            label: "公路",
          },
          {
            value: "004",
            label: "公交",
          },
          {
            value: "005",
            label: "校车",
          }
        ]
      },
      {
        value: "02",
        label: "卡车",
        pId: "0",
        children: [
          {
            value: "021",
            label: "牵引车",
          },
          {
            value: "022",
            label: "中重载货",
          },
          {
            value: "023",
            label: "中重自卸",
          },
          {
            value: "024",
            label: "中重专用",
          },
          {
            value: "025",
            label: "轻卡",
          },
          {
            value: "026",
            label: "皮卡",
          }
        ]
      }
    ]

  },
  // 工程机械
  {
    value: "1",
    label: "工程机械",
    // 板块
    children: [
      {
        value: "11",
        label: "客车",
        pId: "1", 
      },
      {
        value: "12",
        label: "客车",
        pId: "1", 
      },
      {
        value: "13",
        label: "客车",
        pId: "1", 
      },
      {
        value: "14",
        label: "客车",
        pId: "1", 
      },
      {
        value: "15",
        label: "客车",
        pId: "1", 
      }
    ]

  },
  // 农业机械
  {
    value: "2",
    label: "农业机械",
    // 板块
    children: [
      {
        value: "21",
        label: "花生机",
        pId: "2", 
      },
      {
        value: "22",
        label: "农机（其他）",
        pId: "2", 
      },
      {
        value: "23",
        label: "水稻机",
        pId: "2", 
      },
      {
        value: "24",
        label: "拖拉机",
        pId: "2", 
      },
      {
        value: "25",
        label: "小麦机",
        pId: "2", 
      },
      {
        value: "26",
        label: "玉米机",
        pId: "2", 
      }
    ]

  },
]



  export default { optTree,
    optionsCar,optionsFacturer,optionsfuel,optionsfuel2_1,optionsdataSource,optionsquarter
  }