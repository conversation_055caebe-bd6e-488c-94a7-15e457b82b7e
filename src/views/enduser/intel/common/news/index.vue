<template>
  <div class="bankuai">

    <el-card class="box-card" body-class="custom-body-class" shadow="hover" >
      <Tabs :tabsData="tabsData" :activeName="activeName" :dataList="dataList" @call-parent-method="parentMethod" @click="handleCurrentQuery"  />
      
    </el-card>
  </div>
</template>

<script setup>
import Tabs from "@/views/components/Tabs.vue";
const showTab = ref('showTab')
const emit = defineEmits(['call-parent-method']);
const handleMore  = () =>  {  
  showTab.value = 'showTab1'
  emit('handleEmit','more')
}
const handleClose = () =>{  
  showTab.value = 'showTab1'
   showTab.value = 'showTab'
}

const tabsData = reactive([
  {
    label:'宏观动态',
    value:'0'
  },{
    label:'行业趋势与政策法规',
    value:'1'
  },{
    label:'战略合作',
    value:'2'
  },{
    label:'经营动态',
    value:'3'
  },{
    label:'高管动态',
    value:'4'
  },{
    label:'人事变动',
    value:'5'
  },{
    label:'市场活动与订单',
    value:'6'
  },{
    label:'供货价格及商务政策',
    value:'7'
  },{
    label:'终端返利与服务政策',
    value:'8'
  },{
    label:'产品与技术',
    value:'9'
  },{
    label:'产品市场表现',
    value:'10'
  },{
    label:'四化动态',
    value:'11'
  },
])
const activeName = reactive('0')
const dataList = reactive(
  [
    {
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'0'
    },
    {
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'1'
    },{
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'0'
    },
    {
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'1'
    },
     {
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'1'
    },
    {
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'1'
    },
     {
      text:'财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
      date:'2024-12-13',
      id:'1'
    },
]
)

const parentMethod = () => {
  console.log('This is a parent method');
}
const handleCurrentQuery = ()=>{ 
  console.log('This is a parent method');
}


</script>

<style lang="scss" scoped>
.box-card{ 
  border-radius: 0;
  overflow: hidden;
  padding: 0;
  height: 100%;


}
.custom-body-class {  
  .el-card__footer {
    border-top:0 !important;
    padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding);
  }
}
.tabsBox{
  padding: 0
}
.bankuai{  
  height: 360px;
  overflow: auto;
  
}
.bankuai::-webkit-scrollbar { width: 3px; background: #d0dbeb; }

.card-header{ 
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  .header_title{  
    font-size: 18px;
    line-height: 24px;
    color: #051C2C ;
    font-weight: Regular;
  }
}
.text_item { 
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  .text_items{  
    font-weight: 400;
    font-size: 14px;
    line-height: 40px;
    color: #051C2C;
    font-weight: Regular;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的文本 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .text_data{  
    text-align: right;
    font-weight: 400;
    font-size: 14px;
    line-height: 40px;
    color: #9BA4AB;
    font-weight: Regular;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏超出容器的文本 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }

}
.comeback{  
  text-align: right;;
  font-size: 14px;
}
.titleflex{ 
  display: flex
}
.el-card__footer {
  border-top:0 !important;
  padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding);
}
</style>
<style>
.el-card__footer {
  border-top:0 !important;
  padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding);
  color:#2970da;
  cursor: pointer;
  
}
</style>
