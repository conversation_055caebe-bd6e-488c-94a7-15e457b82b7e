<template>
  <CommonTabs active="1">
    <div>
      <SearchFormResource :params="data.params" @change="getParams" />
      <el-row :gutter="16" style="margin-left: 0; margin-right: 0">
        <el-col :xs="24" :sm="24" :md="15">
          <el-card
            class="bi-card"
            style="background: linear-gradient(180deg, #d6ebfa 0%, rgba(210, 230, 252, 0.5) 100%)"
            v-loading="loading.sendingMap"
          >
            <div class="content">
              <div class="sales">
                <div class="sales__item">
                  <img src="@/assets/images/icon/starMap.png" class="sales__item--icon" />
                  <div
                    class="sales__item--value"
                    :style="{
                      color: data.mapTotal.manufacturer.toString().includes('-')
                        ? '#0085FF'
                        : '#0B5FC5'
                    }"
                  >
                    {{ data.mapTotal.manufacturer }}
                  </div>
                  <div class="sales__item--name">主机厂</div>
                </div>
                <div class="sales__item">
                  <img
                    src="@/assets/images/icon/chartMap.png"
                    class="sales__item--icon"
                    style="top: -16px; right: -10px"
                  />
                  <div
                    class="sales__item--value"
                    :style="{
                      color: data.mapTotal.dealer.toString().includes('-') ? '#0085FF' : '#0B5FC5'
                    }"
                  >
                    {{ data.mapTotal.dealer }}
                  </div>
                  <div class="sales__item--name">经销商</div>
                </div>
                <div class="sales__item">
                  <img
                    src="@/assets/images/icon/chartMap.png"
                    class="sales__item--icon"
                    style="top: -16px; right: -10px"
                  />
                  <div
                    class="sales__item--value"
                    :style="{
                      color: data.mapTotal.totalSale.toString().includes('-')
                        ? '#0085FF'
                        : '#0B5FC5'
                    }"
                  >
                    {{ data.mapTotal.totalSale }}
                  </div>
                  <div class="sales__item--name">总销量</div>
                </div>
                <div class="sales__item">
                  <img
                    src="@/assets/images/icon/chartMap.png"
                    class="sales__item--icon"
                    style="top: -16px; right: -10px"
                  />
                  <div
                    class="sales__item--value"
                    :style="{
                      color: data.mapTotal.yuchaiSale.toString().includes('-')
                        ? '#0085FF'
                        : '#0B5FC5'
                    }"
                  >
                    {{ data.mapTotal.yuchaiSale }}
                  </div>
                  <div class="sales__item--name">玉柴销量</div>
                </div>
              </div>
              <maps
                ref="refMaps"
                :series-data="data.areaData"
                @select="changeSelectArea"
                :params="data.params"
                :height="data.chartHeight"
                :queryType="data.params.queryType"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="9">
          <el-card>
            <div class="ratio-width" :style="{ paddingBottom: data.chartHeight }">
              <div ref="target" class="ratio-width__wrap">
                <el-table
                  stripe
                  :data="data.areaData"
                  class="table-box"
                  height="100%"
                  style="width: 100%"
                  :border="true"
                  v-loading="loading.sendingCompany"
                  scrollbar-always-on
                >
                  <el-table-column
                    header-align="center"
                    align="left"
                    prop="province"
                    :label="data.params.province === '' ? '省份' : '市'"
                    min-width="70"
                    show-overflow-tooltip
                  >
                    <template #default="{ row }">
                      <el-button
                        type="primary"
                        text
                        class="button-toggle"
                        @click="toggleAreaItem(row)"
                        >{{ data.params.province === '' ? row.province : row.city }}</el-button
                      >
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="dealer"
                    label="经销商数量"
                    min-width="70"
                    header-align="center"
                    align="right"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="totalSale"
                    header-align="center"
                    align="right"
                    label="行业"
                    min-width="70"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    v-if="data.params.province === ''"
                    prop="yoy"
                    label="行业同比"
                    header-align="center"
                    align="right"
                    min-width="70"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="yuchai"
                    label="玉柴"
                    min-width="70"
                    header-align="center"
                    align="right"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="weichai"
                    label="潍柴"
                    min-width="70"
                    header-align="center"
                    align="right"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="yunnei"
                    label="云内"
                    min-width="70"
                    header-align="center"
                    align="right"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="quanchai"
                    label="全柴"
                    header-align="center"
                    align="right"
                    min-width="70"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="kangms"
                    label="康明斯"
                    min-width="70"
                    header-align="center"
                    align="right"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="prop"
                    label="玉柴占比"
                    min-width="70"
                    header-align="center"
                    align="right"
                    show-overflow-tooltip
                  />
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <companyDialog v-model="data.dialogFlag" :list="data.dialogList"></companyDialog>
  </CommonTabs>
</template>

<script setup>
import { useWindowSize } from '@vueuse/core'
import CommonTabs from '@/views/components/tabs/CommonTabs'
import SearchFormResource from './components/SearchFormResource.vue'
import maps from './components/maps.vue'
import companyDialog from './components/companyDialog.vue'
import cityJian2Full from '@/utils/common/map/cityJian2Full.json'
import provinceJian2Full from '@/utils/common/map/provinceJian2Full.json'
import { dealerSaleList } from '@/api/intelligence/terminalMarket.js'
const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '流向数')?.value
// 初始化搜索条件
const originParams = {
  pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  quarter: '', // 季度
  dataSource: dataSource,
  segment: '商用车', // 板块
  province: '',
  city: '',
  manuFacturer: '', // 主机厂
  weightMidLight: '',
  engineFactory: '', // 发动机厂
  subMarket1: '',
  breed: '' // 品系
}
const refMaps = ref(null)
const data = reactive({
  chartHeight: 'calc(100vh - 288px)',
  dialogFlag: false,
  dialogList: [],
  params: { ...originParams },
  areaData: [], // 地图数据
  // "dealer" 经销商数 "totalSale" 总销量 "yuchaiSale" 玉柴销量 "manufacturer" 主机厂数量
  mapTotal: { dealer: '—', manufacturer: '—', totalSale: '—', yuchaiSale: '—' },
  tableA: [], // 第一个列表
  tableB: [] // 第二个列表
})
const loading = reactive({
  sendingMap: false,
  sendingCompany: false
})
const { width } = useWindowSize()
watchEffect(() => {
  console.log('width.value ', width.value)
  if (width.value >= 1918) {
    data.chartHeight = 'calc(100vh - 380px)'
  } else if (width.value >= 1534) {
    data.chartHeight = 'calc(100vh - 306px)'
  } else if (width.value >= 1439) {
    data.chartHeight = 'calc(100vh - 288px)'
  } else if (width.value >= 1279) {
    data.chartHeight = 'calc(100vh - 254px)'
  } else {
    data.chartHeight = 'calc(100vh - 254px)'
  }
})
/**
 * @description 点击搜索
 * @param params 搜索参数
 */
function getParams(params) {
  data.params = params
  initChartData(params)
}
/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = async param => {
  // if (loading.sendingMap) return
  loading.sendingMap = true
  const params = JSON.parse(JSON.stringify(param))
  if (params.province === '内蒙') params.province = '内蒙古'
  if (params.city) getCompanyData(params)
  params.city = ''
  dealerSaleList(params)
    .then(({ data: resData, code }) => {
      if (code !== 200) return
      const { saleList: sList, stats } = resData
      data.mapTotal = {
        dealer: stats.dealer ? stats.dealer.toString() : '—',
        manufacturer: stats.manufacturer ? stats.manufacturer.toString() : '—',
        totalSale: stats.totalSale ? stats.totalSale.toString() : '—',
        yuchaiSale: stats.yuchaiSale ? stats.yuchaiSale.toString() : '—'
      }
      // 地图数据
      let saleList = sList.map(el => ({
        ...el,
        name: data.params.province === '' ? el.province : el.city,
        value: el.totalSale,
        yoy: el.yoy ?? ''
      }))
      const areaData = []
      for (let i = 0; i < saleList.length; i++) {
        const el = saleList[i]
        if (!el.name || el.name === 'NULL') {
          continue
        }
        areaData.push(el)
      }
      const finallyAreaData = []
      // 地图只能显示内蒙优化内蒙古为内蒙;去除非省份的数据
      areaData.forEach(el => {
        if (provinceJian2Full[el.province]) {
          finallyAreaData.push(el)
          if (el.province === '内蒙古') {
            el.name = '内蒙'
            el.province = '内蒙'
          }
        } else {
          finallyAreaData.push(el)
        }
      })
      console.log('finallyAreaData', finallyAreaData)
      data.areaData = finallyAreaData
    })
    .finally(() => {
      loading.sendingMap = false
    })
}
const getCompanyData = param => {
  // if (loading.sendingCompany) return
  loading.sendingCompany = true
  const params = JSON.parse(JSON.stringify(param))
  params.city = cityJian2Full[params.city] ?? params.city ?? ''
  dealerSaleList(params)
    .then(({ data: resData, code }) => {
      if (code !== 200) return
      const { saleList } = resData
      data.dialogList = saleList
      data.dialogFlag = true
    })
    .finally(() => {
      loading.sendingCompany = false
    })
}

const changeSelectArea = ev => {
  data.params.province = ev.province
  data.params.city = ev.city
  initChartData(data.params)
}

const toggleAreaItem = ev => {
  if (data.params.province === '') {
    refMaps.value.selectArea(ev.province)
    data.params.province = ev.province
    initChartData(data.params)
  }
  if (ev.city) {
    refMaps.value.selectArea(data.params.province)
    data.params.city = ev.city
    initChartData(data.params)
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.content {
  position: relative;
  width: 100%;
  height: 100%;
}

.sales {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  display: flex;
  &__item {
    position: relative;
    width: 160px;
    height: 80px;
    padding: 20px;
    margin-right: 20px;
    border-radius: 8px;
    border: 1px solid #92cdfc;
    background: linear-gradient(288deg, #c6e0f9 2%, #fafbfc 97%);
    backdrop-filter: blur(13.6px);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    &--icon {
      position: absolute;
      top: -25px;
      right: -20px;
      width: 80px;
      height: 80px;
    }
    &--name {
      color: #051c2c;
      font-size: 12px;
    }
    &--value {
      color: #0085ff;
      font-size: 30px;
      font-weight: bold;
    }
  }
}
.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}
:deep(.el-col) {
  margin-bottom: 0;
}
:deep(.el-button--large.button-toggle) {
  padding: 0;
  text-decoration: underline;
}
</style>
