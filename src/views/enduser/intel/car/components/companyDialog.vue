<template>
  <el-dialog
    v-model="modelValue"
    :close-on-click-modal="false"
    top="4vh"
    width="80vw"
    append-to-body
  >
    <el-table
      stripe
      :data="props.list"
      class="table-box"
      height="100%"
      style="width: 100%"
      :border="true"
    >
      <el-table-column
        header-align="left"
        align="left"
        prop="dealer"
        label="经销商"
        min-width="60"
        show-overflow-tooltip
      />

      <el-table-column
        prop="manufacturerName"
        label="经销商数量"
        min-width="70"
        show-overflow-tooltip
      />
      <el-table-column prop="productUse" label="产品用途" min-width="50" show-overflow-tooltip />
      <el-table-column prop="model" label="车型" min-width="60" show-overflow-tooltip />
      <el-table-column
        prop="engineManufacturer"
        label="发动机企业"
        min-width="50"
        show-overflow-tooltip
      />
      <el-table-column prop="engine" label="发动机型号" min-width="50" show-overflow-tooltip />
      <el-table-column prop="cylinderCount" label="缸数" min-width="50" show-overflow-tooltip />
      <el-table-column prop="displacement" label="排量" min-width="50" show-overflow-tooltip />
      <el-table-column prop="horsepower" label="马力" min-width="50" show-overflow-tooltip />
      <el-table-column prop="drive" label="驱动" min-width="50" show-overflow-tooltip />
      <el-table-column prop="sale" label="销量" min-width="50" show-overflow-tooltip />
    </el-table>
  </el-dialog>
</template>
<script setup>
const props = defineProps({
  list: {
    type: Object,
    default: () => []
  }
})

const modelValue = defineModel({ require: true })
</script>
<style scoped lang="scss">
.wrap {
  width: calc(80vw - 36px);
  height: 86vh;
  min-height: 86vh;
  padding: 0;
  margin: 0;
  overflow: auto;
}
</style>
