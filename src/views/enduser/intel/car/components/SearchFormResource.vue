<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            :clearable="false"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="pointerType">
          <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            disabled: true,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment',
            disabled: true
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            hide: true,
            key: 'subMarket2',
            disabled: data.disabledSubMarket2
          }
        ]"
        :propsFuelType="{ name: '燃料', key: 'fuelType', show: false, type: 'B' }"
        :propsBreed="{ name: '品系', key: 'breed', show: true, disabled: data.disabledBreed }"
        :propsWeightMidLight="{
          name: '重中轻',
          key: 'weightMidLight',
          show: true,
          disabled: false
        }"
        :xs="8"
        :sm="8"
        :md="3"
      />
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="province">
          <el-select v-model="params.province" placeholder="省" clearable style="width: 100%">
            <el-option
              v-for="item in cityData"
              :key="item.code"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="city">
          <el-select v-model="params.city" placeholder="市" clearable style="width: 100%">
            <el-option
              v-for="item in data.cityArray"
              :key="item.code"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item>
          <el-button type="primary" @click="toggleSearch">查询</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType, dictDataType } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'
import provinceFull2Jian from '@/utils/common/map/provinceFull2Jian.json'
import provinceJian2Full from '@/utils/common/map/provinceJian2Full.json'
provinceFull2Jian['内蒙古自治区'] = '内蒙古'
provinceJian2Full['内蒙古'] = '内蒙古自治区'
delete provinceJian2Full['内蒙']
const store = useStore()

const emit = defineEmits(['change'])

const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      year: '', // 年份
      month: '', // 月
      quarter: '', // 季度
      dataSource: '',
      segment: '', // 板块
      province: '',
      city: '',
      manuFacturer: '', // 主机厂
      weightMidLight: '',
      engineFactory: '', // 发动机厂
      subMarket1: '',
      subMarket2: '',
      breed: '' // 品系
    })
  }
})
const data = reactive({
  disabledSubMarket2: false,
  disabledBreed: false,
  cityArray: [],
  linkageData: [] // 多级联动数据
})
const params = reactive({ ...JSON.parse(JSON.stringify(toRaw(props.params))) })
const cityData = computed(() => store.state.dicts.cityData)
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)

watch(
  () => props.params.province,
  val => {
    params.province = provinceJian2Full[val] ?? val
  }
)
watch(
  () => props.params.dataSource,
  val => {
    if (val === '10') {
      // 上险数
      initDateRange('上险数')
      params.segment = '商用车'
    }
  }
)

watch(
  () => params.dataSource,
  val => {
    if (val === '10') {
      // 上险数
      initDateRange('上险数')
      params.segment = '商用车'
    }
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)

watch(
  () => params.province,
  val => {
    const city = cityData.value.find(el => el.name === val)
    const cityArray = city ? city.children : []
    data.cityArray = toRaw(cityArray)
    params.city = ''
  }
)

watch([() => params.subMarket2, () => params.breed], val => {
  if (val[0] && !val[1]) {
    data.disabledSubMarket2 = false
    data.disabledBreed = true
  } else if (!val[0] && val[1]) {
    data.disabledSubMarket2 = true
    data.disabledBreed = false
  } else {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
  }
})

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  data.province = provinceFull2Jian[data.province] ?? data.province ?? ''
  delete data.subMarket2
  emit('change', data)
}
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['流向数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}

initDateRange('流向数', true)
getDictsData()
</script>
