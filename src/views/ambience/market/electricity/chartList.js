import { getYAxisName,xAxisData } from '@/views/ambience/components/commonConfigData'
const xAxisDataQ2= ["2022年Q1",	"2022年Q2",	"2022年Q3",	"2022年Q4",	"2023年Q1",	"2023年Q2",	"2023年Q3"	,"2023年Q4",	"2024年Q1",	"2024年Q2",	"2024年Q3",	"2024年Q4"
]

export const chartList = [
    {
      title:{
        text:'船电销量走势（台）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        type: "value",
        name:getYAxisName('台')
      },
      series: [
        {
          type: "line",
          data: [336,	1249,	1335,	1716,	1567	,1164,	1305	,819,	633,	503	,470,	622,
          ],
          name: "船机",
          symbolSize:1,
        },
        {
          type: "line",
          data: [3589,	9666,	10070,	8920,	8483,	2771,	7973,	8265,	7672,	6206,	5217,	4317
          ],
          name: "单机",
          symbolSize:1,
        },
      ],
    },
    {
      title:{
        text:'船电占比走势（%）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        name:getYAxisName('%'),
      },
      series: [
        {
          type: "line",
          data: [80, 90, 84, 88, 85, 87, 80, 83, 76, 87, 84, 85],
          name: "船机",
          symbolSize:1,
        },
        {
          type: "line",
          data: [20, 30, 34, 28, 35, 37, 30, 33, 36, 37, 34, 35],
          name: "单机",
          symbolSize:1,
        },
      ],
    },

    {
      title:{
        text:'船电动力厂家占比（%）'
      },
      yAxis:{
        name:getYAxisName('%'),
      },
      xAxis:{
        data:xAxisData
      },
      series: [
  
        {
          type: "bar",
          data: [30, 20, 40, 40, 40, 40, 50, 30, 20, 40, 50, 40],
          name: "玉柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
  
        {
          type: "bar",
          data: [40, 40, 20, 30, 20, 30, 10, 30, 10, 40, 30, 30],
          name: "潍柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [30, 40, 40, 30, 40, 30, 40, 40, 70, 20, 20, 30],
          name: "东康",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
          // label: {
          //   show: true,
          //   position: 'top',
          //   formatter:()=>{
          //     return `100%`
          //   }
          // },
        },
      ],
    }
  ]

export default chartList