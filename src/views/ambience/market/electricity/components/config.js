// echarts 通用配置

import { numberFormat } from '../../../../../utils/format'

/**
 * 各种分类线条柱形图颜色
 */
export const color = [
  '#115E93',
  '#87AEC9',
  '#3A76FF',
  '#9CBAFF',
  '#00A9F4',
  '#7FD3F9',
  '#9BA4AB',
  '#CDD1D5',
  '#3BDBD6',
  '#9DEDEA',
  '#C280FF',
  '#E0BFFF',
  '#7248DB',
  '#B8A3ED',
  '#EE824B',
  '#F6C0A5',
  '#9B7A01'
]

export const lineColor = ['#00A9F4', '#0033CC', '#115E93', '#808080', '#99CCFF', '#DDEBF7']

export const colorMap = {
  '其他': '#DDDDDD',
  '其它': '#DDDDDD'
  // '潍柴': '#FF0000'
  // '北汽福田': '#00FF00'
}

/**
 * tooltip提示框设置
 */
export const tooltip = {
  trigger: 'axis',
  appendToBody: true,
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  borderWidth: 0,
  textStyle: {
    //提示框自己的样式
    fontSize: 14,
    // color: '#fff'
    color: '#1D2129'
  },
  axisPointer: {
    label: {
      precision: 2,
      show: true,
      margin: 5,
      backgroundColor: '#0b1f56',
      color: '#fff',
      fontSize: 14
    }
  }
}
/**
 * legend提示框设置
 */
export const legend = {
  type: 'scroll',
  // bottom: 0,
  left: 'center',
  bottom: '5px'
  // right: 32
}

const pieTooltip = params => {
  if (params.value && parseFloat(params.value)) {
    const { units = '', precision = 1, data = {} } = params
    const { nowProp = '' } = data

    const value = `${params.value}` ? `${numFormat(params.value, 0)}${units}` : ''
    const _nowProp = `${nowProp}` ? `${numFormat(nowProp, precision)} %` : ''

    let tipsHtml =
      "<div class='tipItem tipItem1'>" +
      params.marker +
      params.name +
      ' : ' +
      value +
      ` , ` +
      _nowProp +
      '</div>'
    return tipsHtml
  }
  return ''
}

const getStringLength = str => {
  let arr = (str + '').split('')
  // 将数组中的字符串转为Unicode编码，并计算长度
  let length = arr
    .map(char => {
      let code = char.charCodeAt(0)
      // 对于多字节字符，charCodeAt返回的是字节的首字节
      if (0xd800 <= code && code <= 0xdbff) {
        // 可能是高代理字符（高代理字符应该与下一个字符组合成代理对）
        let low = arr[arr.indexOf(char) + 1]
        if (low) {
          let lowCode = low.charCodeAt(0)
          if (0xdc00 <= lowCode && lowCode <= 0xdfff) {
            // 是一个代理对，返回4（2个字节）
            return 4
          }
        }
      }
      // 对于单字节字符和基本多语言面的字符，返回1
      return 1
    })
    .reduce((a, b) => a + b, 0)

  return length
}

export const formatter = (params, totalData, units, otherData = {}, addTooltipTotalPercent) => {
  let { precision = 1 } = otherData || {}
  const { seriesType = '' } = params[0]
  // console.log(params,otherData,'params')
  const length = (params || []).length
  let { _yearMonth = '' } = params[0]?.data || {}

  let yearMonth = ''
  if (_yearMonth) {
    yearMonth = _yearMonth
  }
  if (length > 0) {
    // 展示顺序改为与堆叠一致，由下至上
    // params = params.reverse()

    // 展示顺序由大到小，其他放最后
    params.sort((a, b) => {
      return (b.value || 0) - (a.value || 0)
    })

    const tb = params.findIndex(f => f.seriesName === '同比' && f.seriesType === 'line')
    if (tb > -1) {
      const save = JSON.parse(JSON.stringify(params[tb]))
      params.splice(tb, 1) // 移除元素
      params.push(save) // 将元素添加到末尾
    }

    var list = []
    var other = []
    for (var i = 0; i < params.length; i++) {
      if (params[i].seriesName !== '其他') {
        list.push(params[i])
      } else {
        other = params[i]
      }
    }
    list.push(other)
    params = list
  }

  var chartTipsHtml = `
    <div class="chartTips">
      <div style="margin-bottom: 5px;" class='tipTitle'>${yearMonth} ${params && params[0] ? params[0].name : ''}</div>`

  // 超出10行，按两列展示
  var needWrap = length > 12
  var tipsHtml = `<div class="tipItems ${needWrap ? 'wrap' : ''}">`
  if (seriesType === 'pie') {
    tipsHtml += pieTooltip({ ...params, units, precision })
  }
  var totalHtml = ''
  for (var i = 0; i < length; i++) {
    if (params[i].seriesName === '总计') {
      totalHtml = `
        <div style='margin-bottom: 5px'>
          ${params[i].marker}${params[i].seriesName}:${numFormat(totalData.value[params[i].dataIndex], precision)}${`${params[i].value}` ? (units ? units : '') : ''}
        </div>
      `
    } else {
      params[i].value = parseFloat(params[i].value) || 0
      // 有总计添加每个分类占总计的百分比
      let oemPercent = ''
      if (addTooltipTotalPercent) {
        oemPercent = params[i].value / totalData.value[params[i].dataIndex]
        if (!isNaN(oemPercent))
          oemPercent = '| ' + numFormat(oemPercent * 100, otherData?.precision ?? 1) + '%'
      }

      // if (params[i].value && parseFloat(params[i].value)) {
      var tipDiv = "<div class='tipItem'>"
      var tipText =
        `
      <span>${params[i].marker}${params[i].seriesName}&emsp;</span>
      ` +
        '<span>' +
        numFormat(params[i].value, precision) +
        (params[i].value !== '' || params[i].value == '0' ? (units ? units : '') : '') +
        ' ' +
        oemPercent +
        '</span>'

      if (needWrap) {
        // 针对换行后，文本超出长度后的处理
        var x = getStringLength(tipText)
        var totalWidht = 10 * x + 10
        if (totalWidht < 145) {
          totalWidht = 145
        } else {
          totalWidht = 300
        }
        tipDiv = `<div class='tipItem' style='width:${totalWidht / 192}rem'>`
      }
      // tipsHtml += tipDiv + (i + 1) + '. ' + tipText + '</div>'
      tipsHtml += tipDiv + tipText + '</div>'
      // }
    }
  }
  tipsHtml += '</div>'
  return chartTipsHtml + tipsHtml + totalHtml + '</div>'
}

export const numFormat = (num, precision) => {
  return numberFormat(num, precision)
}

export const titleTipObj = {
  '物流景气指数走势':
    '中国物流业景气指数反映物流业经济发展变化情况，以50%作为经济强弱的分界点，高于50%表示物流业经济扩张，低于50%则表示物流业经济收缩。',
  '制造业采购经理人指数月度走势':
    '制造业PMI指数反映制造业经济总体变化趋势，50%为荣枯分水线，高于50%处于扩张区，低于50%处于萎缩水平。',
  '公路物流运价指数走势':
    '中国公路物流运价指数直接反映了市场供需关系的动态。基数为100%，高于100%运力趋紧，低于100%运力过剩。',
  '消费者信心指数月度走势':
    '消费者信心指数是反映消费者信心强弱的指标，指数等于100表示消费者信心处于强弱临界点，超过100时，表明消费者信心处于强信心区，小于100时，表示消费者信心处于弱信心区。',
  '小松挖掘机开工小时数':
    '小松挖掘机开工小时数是反映房地产和基建施工景气度的重要指标‌，开工小时数的增加通常被视为经济活动回暖的信号‌'
}

export const girtAndlenged = props => {
  let grid = props?.grid || {}

  let series = [...props?.series]

  let maxArr = series.map(({ data = [] }) => {
    let _data = data.map(({ value = '' }) => (value ? value : 0))
    return Math.max(..._data)
  })

  let maxArr1 = series.map(({ name = [] }) => {
    return name?.length || 0
  })
  let max = Math.max(...maxArr)
  let maxName = Math.max(...maxArr1) || 1

  let left = 46
  let right = 100

  // 大于1000的做调整左边距
  if (max && max > 10000) {
    left = 60
  }

  if (maxName) {
    right = 40 + 17 * maxName
  }

  grid = { left: left, bottom: 40, right, top: 36, ...grid }

  return { grid }
}
