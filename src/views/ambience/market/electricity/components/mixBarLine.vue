<template>
  <el-card>
    <template #header>
      <block-title :title="props.title" :icon="props.titleIcon" />
    </template>
    <div class="ratio-width" :style="{ paddingBottom: props.height }">
      <div ref="target" class="ratio-width__wrap" />
    </div>
  </el-card>
</template>

<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import * as echarts from 'echarts'
import { onMounted ,onUnmounted} from 'vue'
import echartsResize from '@/utils/hooks/echartsResize.js'
import { color, colorMap, formatter, tooltip, numFormat } from './config.js'
const props = defineProps({
  title: {
    // 是否展示标题
    type: String,
    required: false,
    default: ''
  },
  titleRotate: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  series: {
    // series数据
    type: Array,
    required: true,
    default: () => []
  },
   // x轴配置
   xAxis:{
    type: Object,
    required: false,
    default: () => ({})
  },
  stack: {
    type: String,
    required: false,
    default: ''
  },
  yAxisName: {
    type: String,
    required: false,
    default: ''
  },
  yAxisLabelFormate: {
    type: String,
    required: false,
    default: ''
  },
  color: {
    // 每条折线的颜色
    type: Array,
    required: false,
    default: () => color
  },
  grid: {
    // grid数据
    type: Object,
    required: false,
    default: () => ({ left: 56, bottom: 56, right: 150, top: 46 })
  },
  height: {
    // 是否展示标题
    type: String,
    required: false,
    default: '45%'
  },
  showTotal: {
    // 是否展示标题
    type: Boolean,
    required: false,
    default: false
  },
  tooltip:{
    type: Object,
    required: false
  }
})
watch(
  () => props.series,
  () => {
    renderEcharts()
  },
  { deep: true }
)
// 初始化实例
let myChart = null
const target = ref(null)
const totalData = ref([]) // 柱形图总计值
let legendListener = null
onMounted(() => {
  myChart = echarts.init(target.value)
  // renderEcharts()
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)
  // 监听lenged的选择
  legendListener = myChart.on('legendselectchanged', event => {
    let dataSeries = initSeries(props.series, event?.selected)
    myChart.setOption({
      series: dataSeries
    })
  })
})
// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  myChart.clear()
  if (props.series.length === 0) return
  const options = {
    tooltip: JSON.parse(JSON.stringify(tooltip)),
    barCategoryGap: '0%',
    barGap: '0%',
    legend: {
      type: 'scroll',
      orient: 'vertical',
      // bottom: '25px',
      top: 'middle',
      right: 0,
      itemHeight: '6',
      textStyle: { fontSize: 10 },
      data: props.series.map(v => v.name)
    },
    grid: props.grid,
    xAxis: [
      {
        type: 'category',
        data: props.xAxis.data || getXAxisData(props.series),
        axisTick: {
          alignWithLabel: true,
          show: false,
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisLabel: {
          fontSize: 10,
          color: '#44546A',
          interval: 0
        }
      }
    ],
    yAxis: [
      {
        name: props.yAxisName || '单位：(万台)',
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(95, 187, 235, 1)',
          fontSize: 14,
          color: '#44546A',
          lineHeight: 16
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: 'rgba(28, 130, 197, .3)',
            type: 'dashed'
          }
        }
      },
      {
        name: '同比：(%)',
        splitNumber: 5,
        type: 'value',
        nameTextStyle: {
          color: '#44546A',
          fontSize: 12,
          align: 'center',
          padding: [0, 0, 0, 25]
        },
        axisLabel: {
          show: true,
          formatter: '{value}%',
          fontSize: 12,
          color: '#44546A'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#9BA4AB'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: initSeries(props.series)
  }

  // tooltip添加总计
  options.tooltip.formatter =props.tooltip?.formatter
  // 通过实例.setOption(options)
  myChart.setOption(options)
}
// 生成符合视图的数据
function initSeries(data, selected) {
  const itemColor = props.color
  const series = []
  if (selected) {
    // 纯电渗透率
    selected['纯电渗透率'] = false
  }
  let filterData = selected ? data?.filter(({ name: n = '' }) => selected[n]) : data
  const allDataLength = filterData && filterData.length > 0 ? filterData[0]?.data?.length : 0 // 总计长度
  const allData = Array.from({ length: allDataLength }).map(() => 0) // 总计
  data.forEach((el, index) => {
    const json = {
      name: el.name,
      type: el.type,
      data: el.data,
      itemStyle: {
        color: colorMap[el.name] ? colorMap[el.name] : itemColor[index % color.length]
      }
    }
    if (el.stack) json.stack = el.stack
    if (el.type === 'bar') {
      json.barWidth = el.barWidth || '40%'
    } else if (el.type === 'line') {
      json.yAxisIndex = 1
      json.showSymbol = true
      json.symbolSize = 2
      json.lineStyle = {
        color: itemColor[index]
      }
      json.label = {
        show: true,
        position: 'right',
        formatter: '{c}%'
      }
    }
    series.push(json)
  })
  filterData?.forEach((el, index) => {
    el.data.forEach((v, vdx) => {
      let _value = Number(v.value || 0)
      // 是否列入统计范围
      // 默认这个值不设定，则列入统计范围
      // 如果这个值设定了，则进入统计范围
      if (!el.disTotal) {
        allData[vdx] = allData[vdx] + _value
      }
    })
  })
  // allData.forEach(v => {
  //   if (countDecimalPlaces(v) > 2) {
  //     v = v.toFixed(2)
  //   }
  //   return v
  // })
  // 添加总计
  if (props.showTotal) {
    series.push({
      name: '总计',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        position: 'top',

        formatter: function (p) {
          return numFormat(allData[p.dataIndex],0)
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: Array.from({ length: allDataLength }).map(() => 0)
    })
    totalData.value = allData
  }

  return series
}
function getXAxisData(value) {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  return data.map(v => v.name)
}

onUnmounted(() => {
  // 取消监听legend事件
  if (legendListener) {
    myChart.off('legendselectchanged', legendListener)
  }
  // 清理图表资源
  myChart && myChart.dispose()
})
</script>

<style lang="scss" scoped>
.ratio-width {
  padding-bottom: 18%;
}
</style>
