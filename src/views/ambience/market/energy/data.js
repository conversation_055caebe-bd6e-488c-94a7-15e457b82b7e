import { getCommonOption } from '../power/data'
export const tabsData = [
    {
        name:'经营动态',
        key:'1'
    },
    {
        name:'战略合作',
        key:'2'
    },
    {
        name:'产品技术',
        key:'3'
    },
    {
        name:'市场活动',
        key:'4'
    },
    {
        name:'终端服务',
        key:'5'
    },
    {
        name:'商务政策',
        key:'6'
    },
    {
        name:'高管动态',
        key:'7'
    },
    {
        name:'人事变动',
        key:'8'
    },
    {
        name:'成本',
        key:'9'
    },
    {
        name:'四化动态',
        key:'10'
    },
    {
        name:'报告下载',
        key:'11'
    },
    {
        name:'超级档案',
        key:'12'
    }
]


export const mode = [
    '客车',
    '卡车'
]

export const modeCar = [
        '轻卡',
        '公路',
        '公交',
        '校车',
        '牵引车',
        '中重载货',
        '中重载货',
        '中重自卸',
        '轻卡',
        '皮卡'
]


export const car2 = [
    '云内',
    '全柴',
    '潍柴',
    '江铃',
    '康明斯',
    '江淮汽车',
    '锡柴',
    '福田股份',
    '五十铃（中国）',
    '江西五十铃',
    '东风轻发'
]


export const car3 = [
'北汽福田',
'江淮轻商',
'重汽集团',
'江铃汽车',
'一汽解放（青岛）',
'东风股份',
'四川江淮',
'庆铃汽车',
'上汽大通',
'山东五征',
'重汽海西'

]


export const rio = [
    '柴油',
    '气体' ,
    '其他'

]

export const soure = ['上险数','货运新增数']

export const selectData = {
    mode,
    modeCar,
    car2,
    car3,
    rio,
    soure
}


const data1 = [50000, 50000, 54000, 58000, 55000, 57000, 50000, 53000, 56000, 57000, 54000, 55000];
const data2 = [40000, 40000, 44000, 38000, 45000, 47000, 40000, 45000,46000, 47000, 44000, 45000];
const data3 = [31000, 31000, 24000, 33000, 38000, 22000, 21000, 34000, 28000, 39000, 22000, 35000];

const data4 = [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000];
const data5 = [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000];



export const energyNum = {
    ...getCommonOption('台'),
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
              var total = 0;
          var result = params[0].name + '<br/>';
          params.forEach(function (item) {
              if (item.value != null) {
                  total += item.value;
              }
          });
          params.forEach(function (item) {
            if (item.value != null) {
                result += item.marker +item.seriesName + ' : ' + item.value.toLocaleString() +'<br/>';
            }
        });
        result += '总和 : ' + total.toLocaleString();
        return result;
        }

},
      series: [
        {
          type: "bar",
          data: data1,
          name: "燃料电池",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: data2,// [40000, 40000, 44000, 38000, 45000, 47000, 40000, 45000,46000, 47000, 44000, 45000],
          name: "纯动电池",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: data3,//[31000, 31000, 24000, 33000, 38000, 22000, 21000, 34000, 28000, 39000, 22000, 35000],
          name: "混合动力",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: data4,//[3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000],
          name: "气体",
          stack: "total",
          barWidth: "40%",
        },
        {
            type: "bar",
            data:data5,// [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000],
            name: "柴油",
            stack: "total",
            barWidth: "40%",
            label: {
              show: true,
              position: 'top',
              formatter:(params)=>{
                const text = data1[params.dataIndex]+data2[params.dataIndex]+data3[params.dataIndex]+data4[params.dataIndex]+data5[params.dataIndex]
                return text.toLocaleString()
              }
            },
          },
      ],
    };
  
  export const commonOption = {
    yAxis:{
      type: "value",
      name:`单位:（%）`,
      // axisLabel:{
      //   formatter: function (value) {
      //       return value + '%';
      //   }
      // }
    },
    tooltip:{
      show: true,
      trigger: "axis",
      // valueFormatter:(value) => `${value}%`
    },
  }
  
  
  
export const barOptionsCar = {
    ...commonOption,
    series: [
    {
        type: "bar",
        data: [10, 20, 30, 20, 30, 15, 20, 20, 15, 20, 10, 20],
        name: "燃料电池",
        stack: "total",
        barWidth: "40%",
    },
    {
        type: "bar",
        data: [40, 20, 20, 20, 10, 15, 30, 20, 15, 15, 30, 20],
        name: "纯动电池",
        symbolSize:1,
        stack: "total",
        barWidth: "40%",
    },
    {
        type: "bar",
        data: [20, 30, 20, 20, 20, 20, 10, 30, 20, 15, 10, 30],
        name: "混合动力",
        symbolSize:1,
        stack: "total",
        barWidth: "40%",
    },
    {
        type: "bar",
        data: [10, 20, 15, 10, 20, 30, 20, 20, 25, 25, 20, 20],
        name: "气体",
        symbolSize:1,
        stack: "total",
        barWidth: "40%",
    },
    {
        type: "bar",
        data: [20, 10, 15, 30, 20, 20, 20, 10, 25, 25, 30, 10],
        name: "柴油",
        symbolSize:1,
        stack: "total",
        barWidth: "40%",
        // label: {
        //   show: true,
        //   position: 'top',
        //   formatter:()=>{
        //     return `100%`
        //   }
        // },
    },
    ],
};


  
export const pieOptionsCar = {
    tooltip:{
        show: true,
        trigger: "axis",
        // valueFormatter:(value) => `${value}%`,
        
      },
    legend: {
        orient: 'vertical',
        // left: 'right',
        right:'60px',
        textStyle: {
          fontSize: 12,
        },
      },
    xAxis:{
        show:false,
    },
    yAxis:{
        show:false,
    },
    series: [
        {
        name: '新能源',
        type: 'pie',
        radius: '50%',
        label: {
            formatter: (params)=>{
              const { value,name ,percent} = params
              const result = percent.toFixed(1);
              return `${name},${value},${Math.round(result)}%`

            },
            rich: {
              time: {
                fontSize: 10,
                color: '#999'
              }
            }
          },
        data: [
            { value: 1584, name: '吉利' },
            { value: 428, name: '福田' },
            { value: 375, name: '徐工' },
            { value: 351, name: '东风' },
            { value: 299, name: '南京金龙' },
            { value: 299, name: '宇通' },
            { value: 286, name: '大通' },
            { value: 274, name: '广西汽车' },
            { value: 270, name: '三一' },
            { value: 247, name: '瑞驰' },
            { value: 2630, name: '其他' }
        ],
        }
    ]
};



export const tableData = [
    { value: 1584, name: '吉利',value2022:372,percent2023:'22.5%',percent2022:'3.8%',sales:'325.8', sales1:'18'},
    { value: 428, name: '福田',value2022:364,percent2023:'6.1%',percent2022:'3.7%' ,sales:'17.6', sales1:'2.4'},
    { value: 375, name: '徐工' ,value2022:485,percent2023:'5.3%',percent2022:'4.9%',sales:'-22.7', sales1:'0.4'},
    { value: 351, name: '东风',value2022:583,percent2023:'5.0%',percent2022:'5.9%',sales:'-39.8' , sales1:'-0.9'},
    { value: 299, name: '南京金龙',value2022:413 ,percent2023:'4.3%',percent2022:'4.2%',sales:'27.6', sales1:'0.1'},
    { value: 299, name: '宇通',value2022:983,percent2023:'4.3%' ,percent2022:'10.0%',sales:'69.6', sales1:'-5.7'},
    { value: 286, name: '大通' ,value2022:379,percent2023:'4.1%',percent2022:'3.9%',sales:'24.5', sales1:'0.2'},
    { value: 274, name: '广西汽车',value2022:265,percent2023:'3.9%',percent2022:'2.7%',sales:'3.4', sales1:'1.2'},
    { value: 270, name: '三一',value2022:153 ,percent2023:'3.8%',percent2022:'1.6%',sales:'76.5', sales1:'2.3'},
    { value: 247, name: '瑞驰',value2022:448 ,percent2023:'3.5%',percent2022:'4.6%',sales:'-44.9', sales1:'-1.0'},
    { value: 2630, name: '其他' ,value2022:5408,percent2023:'37.3%',percent2022:'54.9%',sales:'-51.4', sales1:'-17.5'},
    { value: 7043, name: '合计' ,value2022:9853,percent2023:'100%',percent2022:'100%',sales:'-28.5', sales1:'0.0'}

]

