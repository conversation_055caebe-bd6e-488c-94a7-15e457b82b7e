<template>
  <CommonTabs>
    <template #searchArea>
      <CommonFrom :params="formInline" @change="onSubmit" :dictsResourceValue="['1']"
        :showProp="['breed', 'fuelType', 'weightMidLight']" :iscike="true" :igoneShow="['pointerType']" />

    </template>
    <!-- <div class="el-row-sclorl"> -->
    <el-row class="transportRoot" v-loading="loading" style="margin-left: 0; margin-right: 0">
      <el-col :span="24">
        <Chart v-bind="{
          ...sliceChartData.value,
          // rotate:40,
          barTotal: true,
          subMarket1Name: subMarket1Name,
          heightKey: 'vehicle',
          selectedName: '燃料结构走势'
        }" />
      </el-col>
      <el-col :span="24">
        <Chart v-bind="{
          titleIcon: 'data2',
          ...salesChartData.value,
          subMarket1Name: subMarket1Name,
          heightKey: 'vehicle',
          selectedName: '燃料结构份额走势'
          // rotate:40,
        }" />
      </el-col>
    </el-row>
    <el-form :inline="true" class="demo-form-inline" style="margin-left: 0; margin-right: 0">
      <el-form-item prop="year">
        <el-date-picker v-model="params2.year" type="year" value-format="YYYY" format="YYYY"
          :disabled-date="disabledFeatureDate" placeholder="年份" :clearable="false" style="width: 100%" />
      </el-form-item>
      <el-form-item prop="pointerType">
        <el-select v-model="params2.pointerType" placeholder="指标类型" style="width: 100%">
          <el-option v-for="item in filterdictsPointerType()" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="params2.pointerType === '2'" prop="month">
        <el-select v-model="params2.month" placeholder="月累" style="width: 100%">
          <el-option v-for="item in newDictsMonthTotal" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-else prop="month">
        <el-select v-model="params2.month" placeholder="月度" style="width: 100%">
          <el-option v-for="item in newDictsMonth" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit2({})">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row class="transportRoot transportRoot1" :gutter="15" v-loading="loading2"
      style="margin-left: 0; margin-right: 0">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <Chart v-bind="{
          ...TrendList.value,
          heightKey: 'marketenergy2',
          titleIcon: 'data3',
          isPropsTitle: true,
          title: `${dataYear.year}年${dataYear.month}月新能源企业竞争格局(辆，%)`
        }" />
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="box-card table-box">
          <el-table :data="tableData.value" style="width: 100%">
            <el-table-column label="企业销量及份额变化（辆)" align="center">
              <el-table-column prop="manuFacturer" label="企业名称" align="center">
              </el-table-column>
              <el-table-column :label="dataYear.lastYear + dataYear.month" align="center">
                <el-table-column prop="lastSales" label="销量" align="center">
                  <template #default="scope">
                    <div>{{ numberFormat(scope?.row?.lastSales, 0) }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="lastProp" label="份额" align="center">
                  <template #default="scope">
                    <div>{{ numberFormat(scope?.row?.lastProp) || 0.0 }}%</div>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column :label="dataYear.year + dataYear.month" align="center">
                <el-table-column prop="nowSales" label="销量" align="center">
                  <template #default="scope">
                    <div>{{ numberFormat(scope?.row?.nowSales, 0) }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="nowProp" label="份额" align="center">
                  <template #default="scope">
                    <div>{{ numberFormat(scope?.row?.nowProp) || 0.0 }}%</div>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column>
                <el-table-column prop="sales_prop" label="销量同比" align="center">
                  <template #default="scope">
                    <div :style="Number(scope.row.sales_prop) >= 0 ? { color: '#529b2e' } : { color: 'red' }
                      ">
                      {{ numberFormat(scope?.row?.sales_prop) || 0.0 }} %
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="prop_change" label="份额变化" align="center">
                  <template #default="scope">
                    <div :style="Number(scope.row.prop_change) >= 0 ? { color: '#529b2e' } : { color: 'red' }
                      ">
                      {{ numberFormat(scope?.row?.prop_change) || 0.0 }} %
                    </div>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <!-- </div> -->
  </CommonTabs>
</template>

<script setup>
import CommonFrom from '../../components/CommonFrom.vue'
import CommonTabs from '@/views/components/tabs/CommonTabs'
import Chart from '@/views/ambience/components/CommonChart/Chart2'
import { getNewPatternList, getMarketEnvPatternList } from '@/api/ambience/marketenergy'
import { filterdictsPointerType } from '../../components/commonConfigData'
import { numberFormat } from '@/utils/format.js'
import { dictsMonth, dictsMonthTotal } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'
const formInline = reactive({
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  dataSource: '1', // 数据来源
  segment: '商用车', // 板块
  subMarket1: '', //'卡车', // 细分市场1
  subMarket2: '', // 细分市场2
  manuFacturer: '', // 主机厂
  engineFactory: '', // 发动机厂
  fuelType: '', // 燃料
  date: '', // getInitDate(),
  vehicleType: '',
  breed: '',
  dataType: '',
  weightMidLight: ''
})

const params2 = reactive({
  year: '',
  month: '', // 月
  // quarter: '', // 季度
  pointerType: '2'
})

const dataYear = reactive({
  lastYear: '',
  year: '',
  month: ''
})

const tableData = reactive([])

const sliceChartData = reactive([])
const salesChartData = reactive([])
const TrendList = reactive([])
const loading = ref(false)
const loading2 = ref(false)
const subMarket1Name = ref(formInline.subMarket1)
watch(
  () => params2.pointerType,
  () => {
    innerdate()
  }
)
// 监听年份变化
watch(
  () => params2.year,
  () => {
    innerdate()
  }
)

const { initDateRange, innerdate, disabledFeatureDate, newDictsMonthTotal, newDictsMonth } =
  useInnerData(params2, onSubmit2)

const setData = params => {
  formInline.segment = params?.segment
  formInline.subMarket1 = params?.subMarket1
  formInline.subMarket2 = params?.subMarket2
  formInline.manuFacturer = params?.manuFacturer
  formInline.engineFactory = params?.engineFactory
  formInline.fuelType = params?.fuelType
  formInline.date = params?.date
  formInline.vehicleType = params?.vehicleType
  formInline.breed = params?.breed
  formInline.dataType = params?.dataType
}

const onSubmit = params => {
  // 日期处理

  formInline.date = params?.date || []

  setData(params)
  //  formInline=params

  getgetPatternData(params)

  let ty = new Date().getFullYear()
  let tm = new Date().getMonth() + 1
  if (params?.date) {
    let timeVal = params?.date[1] || '' // 当前年的日期
    if (timeVal && ty == params?.date[1] && tm <= 2) {
      ty = timeVal - 1
      // tm=12;
      timeVal = `${ty}`
    } else {
      timeVal = `${timeVal}`
    }
    params2.year = timeVal
    onSubmit2(params)
  } else {
    let num = `${ty}`
    params2.year = num || ''
    onSubmit2(params)
  }
}

const getgetPatternData = async params => {
  if (loading.value) return
  loading.value = true
  getNewPatternList(params)
    .then(data => {
      const { chart1, chart2 } = data

      chart1.series = chart1.series.map(x => {
        return {
          ...x,
          label: {
            // show: true,
            // position: 'top',
            textStyle: {
              fontSize: 8 // 设置你想要的字体大小
            }
          }
        }
      })

      sliceChartData.value = chart1
      salesChartData.value = chart2
      loading.value = false
      subMarket1Name.value = params.subMarket1
    })
    .catch(() => {
      loading.value = false
      subMarket1Name.value = params.subMarket1
    })
  //  const data2 = await getShipElecFacturerSliceList(params)
}

function onSubmit2(params) {
  if (loading2.value) return
  loading2.value = true
  getMarketEnvPatternList({
    ...formInline,
    ...params,
    ...params2
  })
    .then(data => {
      const { pieOptionsCar, list } = data
      let titleMonth = getTimeData()

      TrendList.value = {
        ...pieOptionsCar,
        title: {
          text: `${titleMonth}${pieOptionsCar?.title.text}`
        }
      }
      tableData.value = list
    })
    .finally(() => {
      loading2.value = false
    })
}

const getTimeData = () => {
  const obj = {
    '0': dictsMonth,
    '2': dictsMonthTotal
  }
  let mon = params2.month
  let val = ''
  if (obj[params2.pointerType] && mon) {
    let list = obj[params2.pointerType].filter(item => {
      const { value } = item
      return value == mon
    })
    if (list && list.length) {
      val = list[0]?.label
    }
  }

  let _date = params2.year
  dataYear.lastYear = `${_date * 1 - 1}年`
  dataYear.year = `${_date}年`
  dataYear.month = val
  //  console.log(_date)
  return `${_date}年${val}`
}

initDateRange('上险数', true)
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';

.demo-tabs {
  border: 1px solid $border-btn-color;
  padding: 10px;

  :deep(.el-tabs__header) {
    margin-right: 100px;
  }
}

.newRoot {
  position: relative;
  background-color: #fff;
}

:deep(.el-tabs__header) {
  margin: 0 0 3px;
}

.transportRoot1 {
  :deep(.el-card__body) .chart {
    margin: 0 auto;
    /* 水平居中 */
    display: table;
    margin-top: auto !important;
    padding: 0 10px;
  }
}

:deep(.listRoot) {
  padding-bottom: 15px;
}

.box-card {
  height: 100%;
}

.lookMore {
  color: $btn-color;
  min-width: 70px;
  line-height: 20px;
  font-size: 14px;
  position: absolute;
  top: 22px;
  right: 20px;
}

:deep(.el-table) thead.is-group {
  // background: #fff;
  background: linear-gradient(180deg, #c6dff7 0%, #e8f2fc 100%);
}

:deep(.el-table) thead.is-group tr {
  background: transparent;
}

:deep(.el-table) thead.is-group th.el-table__cell {
  color: #000;
  background: transparent;
}
</style>
