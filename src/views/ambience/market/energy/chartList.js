import { getYAxisName,xAxisData } from '@/views/ambience/components/commonConfigData'
import { getAddTotal } from '../../components/commonConfigData';


const data1 = [50000, 50000, 54000, 58000, 55000, 57000, 50000, 53000, 56000, 57000, 54000, 55000];
const data2 = [40000, 40000, 44000, 38000, 45000, 47000, 40000, 45000,46000, 47000, 44000, 45000];
const data3 = [31000, 31000, 24000, 33000, 38000, 22000, 21000, 34000, 28000, 39000, 22000, 35000];

const data4 = [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000];
const data5 = [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000];


export const chartList = [
    {
      tooltip:{
        formatter:(params)=>{
          return getAddTotal(params)
        },
      },
      title:{
        text:'月度燃料结构走势'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        type: "value",
        name:getYAxisName('台')
      },
      series: [
        {
          type: "bar",
          data: data1,
          name: "燃料电池",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: data2,// [40000, 40000, 44000, 38000, 45000, 47000, 40000, 45000,46000, 47000, 44000, 45000],
          name: "纯动电池",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: data3,//[31000, 31000, 24000, 33000, 38000, 22000, 21000, 34000, 28000, 39000, 22000, 35000],
          name: "混合动力",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: data4,//[3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000],
          name: "气体",
          stack: "total",
          barWidth: "40%",
        },
        {
            type: "bar",
            data:data5,// [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000],
            name: "柴油",
            stack: "total",
            barWidth: "40%",
            label: {
              show: true,
              position: 'top',
              formatter:(params)=>{
                const text = data1[params.dataIndex]+data2[params.dataIndex]+data3[params.dataIndex]+data4[params.dataIndex]+data5[params.dataIndex]
                return text.toLocaleString()
              }
            },
          },
      ],
    },
    {
      title:{
        text:'月度燃料结构份额走势（%）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        name:getYAxisName('%'),
      },
      series: [
        {
            type: "bar",
            data: [10, 20, 30, 20, 30, 15, 20, 20, 15, 20, 10, 20],
            name: "燃料电池",
            stack: "total",
            barWidth: "40%",
        },
        {
            type: "bar",
            data: [40, 20, 20, 20, 10, 15, 30, 20, 15, 15, 30, 20],
            name: "纯动电池",
            symbolSize:1,
            stack: "total",
            barWidth: "40%",
        },
        {
            type: "bar",
            data: [20, 30, 20, 20, 20, 20, 10, 30, 20, 15, 10, 30],
            name: "混合动力",
            symbolSize:1,
            stack: "total",
            barWidth: "40%",
        },
        {
            type: "bar",
            data: [10, 20, 15, 10, 20, 30, 20, 20, 25, 25, 20, 20],
            name: "气体",
            symbolSize:1,
            stack: "total",
            barWidth: "40%",
        },
        {
            type: "bar",
            data: [20, 10, 15, 30, 20, 20, 20, 10, 25, 25, 30, 10],
            name: "柴油",
            symbolSize:1,
            stack: "total",
            barWidth: "40%",
            // label: {
            //   show: true,
            //   position: 'top',
            //   formatter:()=>{
            //     return `100%`
            //   }
            // },
        },
        ],
    },
    
  ]

export const pieOptionsCar = {
    legend: {
        orient: 'vertical',
        right:'10px',
        top:'10px',
        textStyle: {
          fontSize: 12,
        },
      },
    title:{
      text:'2023年1月新能源企业竞争格局(辆，%)'
    },
    xAxis:{
        show:false,
    },
    yAxis:{
        show:false,
    },
    series: [
        {
        name: '新能源',
        type: 'pie',
        radius: '50%',
        label: {
            formatter: (params)=>{
              const { value,name ,percent} = params
              const result = percent.toFixed(1);
              return `${name},${value},${Math.round(result)}%`

            },
            rich: {
              time: {
                fontSize: 10,
                color: '#999'
              }
            }
          },
        data: [
            { value: 1584, name: '吉利' },
            { value: 428, name: '福田' },
            { value: 375, name: '徐工' },
            { value: 351, name: '东风' },
            { value: 299, name: '南京金龙' },
            { value: 299, name: '宇通' },
            { value: 286, name: '大通' },
            { value: 274, name: '广西汽车' },
            { value: 270, name: '三一' },
            { value: 247, name: '瑞驰' },
            { value: 2630, name: '其他' }
        ],
        }
    ]
};





export default chartList