import { getCommonOption } from '../power/data'
import {reactive} from "vue";
export const machineData = [
  '高空平台',
  '矿用车',
  '挖掘机',
  '装载机',
  '工程机（其他）',
  '叉车',
  '空压机',
  '内燃叉车',
  '钻机',
  '花生机',
  '农机（其他）',
  '水稻机',
  '拖拉机',
  '小麦机',
  '玉米机'
]

export const ship = [
  '云内',
  '全柴',
  '潍柴',
  '江铃',
  '康明斯',
  '江淮汽车',
  '锡柴',
  '福田股份',
  '五十铃（中国）',
  '江西五十铃',
  '东风轻发'
]

export const lineOptionsmach = {
  ...getCommonOption(),
  series: [
    {
      type: 'line',
      data: [50, 50, 54, 58, 55, 57, 50, 53, 56, 57, 54, 55],
      name: '拖拉机',
      symbolSize: 1
    },
    {
      type: 'line',
      data: [40, 20, 44, 48, 45, 47, 40, 43, 46, 47, 44, 45],
      name: '小麦机',
      symbolSize: 1
    },
    {
      type: 'line',
      data: [23, 21, 24, 23, 28, 32, 21, 34, 38, 39, 22, 35],
      name: '玉米机',
      symbolSize: 1
    },
    {
      type: 'line',
      data: [10, 22, 12, 20, 10, 28, 11, 24, 25, 38, 25, 12],
      name: '其他',
      symbolSize: 1
    }
  ]
}

export const lineOptionsmach2 = {
  ...getCommonOption('%'),
  series: [
    {
      type: 'bar',
      data: [30, 50, 44, 25, 30, 20, 10, 30, 20, 20, 10, 25],
      name: '拖拉机',
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [40, 20, 30, 25, 30, 20, 40, 20, 30, 20, 20, 25],
      name: '小麦机',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [20, 15, 16, 30, 20, 30, 20, 30, 20, 30, 30, 25],
      name: '玉米机',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [10, 15, 10, 20, 20, 30, 30, 20, 30, 30, 40, 25],
      name: '其他',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    }
  ]
}

export const barOptionsCar = {
  ...getCommonOption('%'),
  series: [
    {
      type: 'bar',
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: '重汽',
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
      name: '云内',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
      name: '玉柴',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
      name: '锡柴',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },

    {
      type: 'bar',
      data: [22, 32, 44, 12, 4, 23, 54, 23, 44, 11, 33, 33],
      name: '康明斯',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [23, 43, 23, 31, 42, 32, 23, 65, 41, 51, 45, 22],
      name: '潍柴',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
    },
    {
      type: 'bar',
      data: [35, 26, 38, 36, 13, 44, 22, 55, 23, 56, 32, 78],
      name: '其他',
      symbolSize: 1,
      stack: 'total',
      barWidth: '40%'
      // label: {
      //   show: true,
      //   position: 'top',
      //   formatter:()=>{
      //     return `100%`
      //   }
      // },
    }
  ]
}

export const barDataLeft_1 = [
  {
    'name': '燃料电池',
    'type': 'bar',
    'dataNameKey': 'name',
    'dataValueKey': 'fuelcell',
    'data': [
      {
        'name': '2020年',
        'value': 79.41
      },
      {
        'name': '2021年',
        'value': 79.41
      },
      {
        'name': '2022年',
        'value': 79.41
      },
      {
        'name': '2023年',
        'value': 79.41
      },
      {
        'name': '2024年',
        'value': 79.41
      }
    ]
  },
  {
    'name': '纯电动',
    'type': 'bar',
    'dataNameKey': 'name',
    'dataValueKey': 'electricity',
    'data': [
      {
        'name': '2020年',
        'value': 20.36
      },
      {
        'name': '2021年',
        'value': 20.36
      },
      {
        'name': '2022年',
        'value': 20.36
      },
      {
        'name': '2023年',
        'value': 20.36
      },
      {
        'name': '2024年',
        'value': 20.36
      }
    ]
  },
  {
    'name': '混合动力',
    'type': 'bar',
    'dataNameKey': 'name',
    'dataValueKey': 'mixture',
    'data': [
      {
        'name': '2020年',
        'value': 0.57
      },
      {
        'name': '2021年',
        'value': 0.57
      },
      {
        'name': '2022年',
        'value': 0.57
      },
      {
        'name': '2023年',
        'value': 0.57
      },
      {
        'name': '2024年',
        'value': 0.57
      }
    ]
  }
]

export const barDataRight_1 = [
  {
    'name': '非电',
    'type': 'bar',
    'stack': 'total',
    'dataNameKey': 'name',
    'dataValueKey': 'unelectricity',
    'data': [
      {
        'name': '1月',
        'value': 7.9
      },
      {
        'name': '2月',
        'value': 5.43
      },
      {
        'name': '3月',
        'value': 7.9
      },
      {
        'name': '4月',
        'value': 5.43
      },
      {
        'name': '5月',
        'value': 5.43
      },
      {
        'name': '6月',
        'value': 7.9
      },
      {
        'name': '7月',
        'value': 5.43
      },
      {
        'name': '8月',
        'value': 7.9
      },
      {
        'name': '9月',
        'value': 5.43
      },
      {
        'name': '10月',
        'value': 7.9
      },
      {
        'name': '11月',
        'value': 7.9
      },
      {
        'name': '12月',
        'value': 5.43
      }
    ]
  },
  {
    'name': '电',
    'type': 'bar',
    'stack': 'total',
    'dataNameKey': 'name',
    'dataValueKey': 'electricity',
    'data': [
      {
        'name': '1月',
        'value': 2.04
      },
      {
        'name': '2月',
        'value': 1.36
      },
      {
        'name': '3月',
        'value': 2.04
      },
      {
        'name': '4月',
        'value': 1.36
      },
      {
        'name': '5月',
        'value': 1.36
      },
      {
        'name': '6月',
        'value': 2.04
      },
      {
        'name': '7月',
        'value': 1.36
      },
      {
        'name': '8月',
        'value': 2.04
      },
      {
        'name': '9月',
        'value': 1.36
      },
      {
        'name': '10月',
        'value': 2.04
      },
      {
        'name': '11月',
        'value': 2.04
      },
      {
        'name': '12月',
        'value': 1.36
      }
    ]
  },
  {
    'name': '纯电渗透率',
    'type': 'line',
    'dataNameKey': 'name',
    'dataValueKey': 'trend',
    'data': [
      {
        'name': '1月',
        'value': 1
      },
      {
        'name': '2月',
        'value': 2
      },
      {
        'name': '3月',
        'value': 3
      },
      {
        'name': '4月',
        'value': 4
      },
      {
        'name': '5月',
        'value': 5
      },
      {
        'name': '6月',
        'value': 6
      },
      {
        'name': '7月',
        'value': 20
      },
      {
        'name': '8月',
        'value': 20.49
      },
      {
        'name': '9月',
        'value': 20
      },
      {
        'name': '10月',
        'value': 20.49
      },
      {
        'name': '11月',
        'value': 20.49
      },
      {
        'name': '12月',
        'value': 20
      }
    ]
  }
]

export const xx = {
  "yoyRsList": [
      {
          "yoy": "-29.7%",
          "month": 2023,
          "type": "其它"
      },
      {
          "yoy": "-13.7%",
          "month": 2023,
          "type": "安徽合力"
      },
      {
          "yoy": "-2.0%",
          "month": 2023,
          "type": "杭叉集团"
      },
      {
          "yoy": "-28.9%",
          "month": 2023,
          "type": "潍柴雷沃"
      },
      {
          "yoy": "46.9%",
          "month": 2023,
          "type": "江苏沃得"
      },
      {
          "yoy": "2.9%",
          "month": 2023,
          "type": "三一集团"
      },
      {
          "yoy": "1.9%",
          "month": 2023,
          "type": "一拖集团"
      },
      {
          "yoy": "-51.3%",
          "month": 2023,
          "type": "中国龙工"
      },
      {
          "yoy": "-58.4%",
          "month": 2023,
          "type": "徐工集团"
      },
      {
          "yoy": "-51.3%",
          "month": 2023,
          "type": "柳工集团"
      },
      {
          "yoy": "-55.0%",
          "month": 2023,
          "type": "临工集团"
      }
  ],
  "patternRsList": [
      [
          {
              "month": "2024",
              "installationcount": 139897,
              "type": "其它"
          },
          {
              "month": 2023,
              "installationcount": 199063,
              "type": "其它"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 83086,
              "type": "安徽合力"
          },
          {
              "month": 2023,
              "installationcount": 96301,
              "type": "安徽合力"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 80091,
              "type": "杭叉集团"
          },
          {
              "month": 2023,
              "installationcount": 81749,
              "type": "杭叉集团"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 79945,
              "type": "潍柴雷沃"
          },
          {
              "month": 2023,
              "installationcount": 112482,
              "type": "潍柴雷沃"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 56275,
              "type": "江苏沃得"
          },
          {
              "month": 2023,
              "installationcount": 38307,
              "type": "江苏沃得"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 53538,
              "type": "三一集团"
          },
          {
              "month": 2023,
              "installationcount": 52040,
              "type": "三一集团"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 48304,
              "type": "一拖集团"
          },
          {
              "month": 2023,
              "installationcount": 47411,
              "type": "一拖集团"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 39228,
              "type": "中国龙工"
          },
          {
              "month": 2023,
              "installationcount": 80596,
              "type": "中国龙工"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 37544,
              "type": "徐工集团"
          },
          {
              "month": 2023,
              "installationcount": 90210,
              "type": "徐工集团"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 28141,
              "type": "柳工集团"
          },
          {
              "month": 2023,
              "installationcount": 57733,
              "type": "柳工集团"
          }
      ],
      [
          {
              "month": "2024",
              "installationcount": 24755,
              "type": "临工集团"
          },
          {
              "month": 2023,
              "installationcount": 55016,
              "type": "临工集团"
          }
      ]
  ]
};

export const dictSubMarketDefault = reactive([
  {
    "label": "整体",
    "value": "整体"
  },
  {
    "label": "工程机械",
    "value": "工程机械"
  },
  {
    "label": "挖掘机",
    "value": "挖掘机",
    "parent": "工程机械"
  },
  {
    "label": "装载机",
    "value": "装载机",
    "parent": "工程机械"
  },
  {
    "label": "矿用车",
    "value": "矿用车",
    "parent": "工程机械"
  },
  {
    "label": "高空平台",
    "value": "高空平台",
    "parent": "工程机械"
  },
  {
    "label": "工业动力",
    "value": "工业动力"
  },
  {
    "label": "内燃叉车",
    "value": "内燃叉车",
    "parent": "工业动力"
  },
  {
    "label": "空压机",
    "value": "空压机",
    "parent": "工业动力"
  },
  {
    "label": "钻机",
    "value": "钻机",
    "parent": "工业动力"
  },
  {
    "label": "农业机械",
    "value": "农业机械"
  },
  {
    "label": "拖拉机",
    "value": "拖拉机",
    "parent": "农业机械"
  },
  {
    "label": "水稻机",
    "value": "水稻机",
    "parent": "农业机械"
  },

  {
    "label": "小麦机",
    "value": "小麦机",
    "parent": "农业机械"
  },
  {
    "label": "玉米机",
    "value": "玉米机",
    "parent": "农业机械"
  },
  {
    "label": "花生机",
    "value": "花生机",
    "parent": "农业机械"
  },
])