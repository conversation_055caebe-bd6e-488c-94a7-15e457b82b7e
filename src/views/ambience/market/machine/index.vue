<template>
  <CommonTabs>
    <template #searchArea>
      <!-- 搜索框 -->
      <SearchFormResource
        class="search_form_box"
        :params="searchForm"
        :linkageData="searchConfig.linkageData"
        :dictsManuFacturer="searchConfig.dictManuFacturer"
        :dictsEngineFactory="searchConfig.dictsEngineFactory"
        @change="onSubmit"
      />
      <!-- 分类 -->
      <el-segmented
        ref="segmentedRef"
        v-model="searchConfig.subMarket"
        :options="searchConfig.dictSubMarket"
        @change="tabChange"
      >
        <template #default="scope">
          <div>{{ scope.item.label }}</div>
        </template>
      </el-segmented>
    </template>
    <!-- 内容 -->
    <!-- <div class="el-row-sclorl"> -->
    <el-row :gutter="20" type="flex" style="margin-left: 0; margin-right: 0">
      <el-col :xs="24" :sm="24" :md="8">
        <bar
          titleIcon="data1"
          v-loading="barDataLeft1.loading"
          :title="barDataLeft1.title"
          :series="barDataLeft1.data"
          y-axis-name="单位：台"
          :tooltip="{
            position: positionLeft,
            formatter: params =>
              TooltipFormatter(TooltipSalesAndPercentageComponent, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'proportion'
                }
              })
          }"
          tooltipUnits=""
          :precision="0"
          show-total
          title-rotate
          :grid="{ left: 80, bottom: 36, right: 100, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4, data: sortLegendWithOtherFirst(barDataLeft1.data)}"
          addTooltipTotalPercent
          :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px"
        />
        <!-- TooltipSalesAndPercentageComponent -->
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        
        <mixBarLine
          v-loading="barDataRight1.loading"
          :title="barDataRight1.title"
          y-axis-name="单位：台"
          yAxisName2="同比：(%)"
          :series="barDataRight1.data"
          :grid="{ left: 80, bottom: 36, right: 125, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 20 ,data: sortLegendWithOtherFirst(barDataRight1.data)}"
          :color="['#115e93', '#87aec9', '#3a76ff', '#9cbaff', '#00a9f4', '#7fd3f9', '#fac858']"
          :show-total="false"
          :xAxis="{
            data:
              searchConfig.params.pointerType != '1' ? referData.monthSort : referData.quarterSort,
            nameTextStyle: { height: '220px' }
          }"
          height="270px"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'proportion',
                  yoy: 'slice'
                },
                isYoySortField: true,
                singleColumn: true,
                shouldSort: true,
                showTotal: false,
                sortField: 'value'
              })
          }"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <bar
          titleIcon="data1"
          v-loading="barDataLeft2.loading"
          :title="barDataLeft2.title"
          :series="barDataLeft2.data"
          y-axis-name="单位：台"
          tooltipUnits=""
          :precision="0"
          :tooltip="{
            position: positionLeft,
            formatter: params =>
              TooltipFormatter(TooltipSalesAndPercentageComponent, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'proportion',
                  yoy: 'slice'
                },
                singleColumn: false,
                shouldSort: true,
                showTotal: true,
                sortField: 'proportion'
              })
          }"
          show-total
          title-rotate
          :grid="{ left: 80, bottom: 36, right: 100, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          addTooltipTotalPercent
          :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <mixBarLine
          v-loading="barDataRight2.loading"
          :title="barDataRight2.title"
          y-axis-name="单位：台"
          yAxisName2="同比：(%)"
          :series="barDataRight2.data"
          :grid="{ left: 80, bottom: 24, right: 125, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :show-total="false"
          :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponentYoy, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                singleColumn: true,
                shouldSort: false,
                showTotal: false,
                sortField: 'value'
              })
          }"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <bar
          titleIcon="data1"
          :tooltip="{
            position: positionLeft,
            formatter: params =>
              TooltipFormatter(TooltipSalesAndPercentageComponent, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'proportion',
                  yoy: 'slice'
                },
                singleColumn: false,
                shouldSort: false,
                showTotal: true,
                sortField: 'proportion'
              })
          }"
          v-loading="barDataLeft3.loading"
          :title="barDataLeft3.title"
          :series="barDataLeft3.data"
          y-axis-name="单位：台"
          tooltipUnits=""
          :precision="0"
          show-total
          title-rotate
          :grid="{ left: 80, bottom: 36, right: 100, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          addTooltipTotalPercent
          :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <mixBarLine
          v-loading="barDataRight3.loading"
          :title="barDataRight3.title"
          y-axis-name="单位：台"
          yAxisName2="同比：(%)"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponentYoy, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                singleColumn: true,
                shouldSort: false,
                showTotal: false,
                sortField: 'value'
              })
          }"
          :series="barDataRight3.data"
          :grid="{ left: 80, bottom: 24, right: 125, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :show-total="false"
          :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="270px"
        />
      </el-col>
    </el-row>
    <!-- </div> -->
  </CommonTabs>
</template>

<script setup lang="jsx">
import { onMounted } from 'vue'
import mixBarLine from './components/mixBarLine.vue'
import { positionLeft } from '@/utils/echarts'
import SearchFormResource from './components/SearchFormResource.vue'
import bar from '@/views/components/echarts/bar.vue'
import CommonTabs from '@/views/components/tabs/CommonTabs'
import calChartsData from '@/utils/hooks/calChartsData.js'
import { TooltipFormatter } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'
import { getMonthByNameAndYear, setYuchaiColor } from '@/utils/common/method.js'
import { typePointerType } from '@/utils/common/dicts'
// import {setYuchaiColor} from '@/utils/common/method';
import {
  generalMachineYearSale,
  generalMachineMonthSale,
  generalMachineYearSaleForMain,
  generalMachineYearSaleForEngine,
  generalMachineYearYoy,
  generalMachineYearSaleYoy
} from '@/api/ambience/machine'
import { throttle } from '../../../../utils'
import Bicontent from '@/views/components/tabs/BiContent'
const store = useStore()
const { referData } = calChartsData()
import { reactive } from 'vue'
import { dictSubMarketDefault } from './data'
import {
  TooltipSalesAndPercentageComponent,
  TooltipComponent,
  TooltipComponentYoy
} from '@/views/components/jsx/TooltipComponent'
import { seriesSortOtherFirst, sortLegendWithOtherFirst } from '../../../../utils/common/method'

const searchForm = reactive({
  year: (new Date().getFullYear() - 1).toString(), // 年份
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  month: '12', // 月
  // quarter: '', // 季度
  dataSource: '2', // 数据来源
  segment: '通机', // 板块
  manuFacturer: '', // 主机厂
  engineFactory: '', // 发动机厂
  subMarket: '' // 细分市场
})
const searchConfig = reactive({
  params: { ...searchForm },
  subMarket: '整体',
  linkageData: [], // 多级联动数据
  dictSubMarket: [],
  dictsManuFacturer: [], // 主机厂
  dictsEngineFactory: [] // 发动机厂
})
const barDataLeft1 = reactive({
  title: '非道路总体市场年度销量趋势',
  loading: false,
  data: []
}) // 第1行左侧报表
const barDataRight1 = reactive({
  title: '非道路总体市场月度销量趋势',
  loading: false,
  data: []
}) // 第1行右侧报表

const barDataLeft2 = reactive({
  title: '非道路市场主机厂销量',
  loading: false,
  data: []
}) // 第2行左侧报表
const barDataRight2 = reactive({
  title: '非道路市场主机厂销量',
  loading: false,
  data: []
}) // 第2行右侧报表

const barDataLeft3 = reactive({
  title: '非道路市场发动机销量',
  loading: false,
  data: []
}) // 第2行左侧报表
const barDataRight3 = reactive({
  title: '非道路市场发动机销量',
  loading: false,
  data: []
}) // 第2行右侧报表

const segmentedRef = ref(null)
const { yAxisRight } = calChartsData()
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['装机数']
    })
    .catch(e => e)

  // console.log('字典', dicts)

  if (dicts && dicts.length > 0) {
    searchConfig.linkageData = dicts
    const zjs = dicts.find(item => item.label === '装机数')
    const tongji = zjs.children?.find(item => item.label === '通机')

    searchConfig.dictSubMarket = [{ label: '整体', value: '整体' }]
    let subMarketList = [
      '工程机械',
      '挖掘机',
      '装载机',
      '矿用车',
      '高空平台',
      '工业',
      '空压机',
      '钻机',
      '农业机械',
      '拖拉机',
      '水稻机',
      '小麦机',
      '玉米机',
      '花生机'
    ]
    subMarketList.forEach(element => {
      // 把自己放进去
      searchConfig.dictSubMarket.push({ label: element.label, value: element.value })

      // // 是否有子项，有则丢进去
      // if (element.children) {
      //   element.children.forEach(child => {
      //     searchConfig.dictSubMarket.push({
      //       label: child.label,
      //       value: child.value,
      //       parent: element.value
      //     })
      //   })
      // }
    })
    searchConfig.dictSubMarket = dictSubMarketDefault
    searchConfig.dictsManuFacturer = tongji.manuFacturer
    searchConfig.dictsEngineFactory = tongji.engineFactory
  }
}
getDictsData()

function getPercent() {
  const curWidth = segmentedRef.value.$el.offsetWidth
  const parentWidth = segmentedRef.value.$parent.$el.offsetWidth
  return parentWidth / curWidth
}
const length = ref(searchConfig.dictSubMarket.length)
const resizeDom = throttle(() => {
  if (!segmentedRef.value) return
  const percent = getPercent()
  length.value = Math.floor(searchConfig.dictSubMarket.length * percent.toFixed(2))
  nextTick(() => {
    const percent = getPercent()
    if (percent < 1) {
      length.value--
    }
  })
})
onMounted(() => {
  // getSalesSliceList(searchForm)
  // getSalesSliceList1(searchForm)
  resizeDom()
  if (window.ResizeObserver) {
    // 使用 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      // console.log('ResizeObserver: ')
      resizeDom()
    })

    // 开始监听容器大小变化
    // resizeObserver?.observe(segmentedRef.value.$parent.$el)
  } else {
    window.addEventListener('resize', () => {
      // console.log('onresize: ')
      resizeDom()
    })
  }

  nextTick(() => {
    barDataLeft1.data = []
    barDataLeft2.data = []
    barDataLeft3.data = []

    barDataRight1.data = []
    barDataRight2.data = []
    barDataRight3.data = []
  })
})

function tabChange(subMarket) {
  searchConfig.subMarket = subMarket
  barDataLeft1.title = `${subMarket === '整体' ? '非道路总体' : subMarket}市场年度销量趋势`
  barDataRight1.title = `${subMarket === '整体' ? '非道路总体' : subMarket}市场${typePointerType[searchConfig.params.pointerType]}销量趋势`
  getDataLetf1()
  getDataRight1()
  getDataLine2()
  getDataLine3()
}
// 由搜索组件控制发起请求，包括首次请求
const onSubmit = params => {
  searchConfig.params = params
  getDataLetf1()
  getDataRight1()
  getDataLine2()
  getDataLine3()
  console.log('参数测试', params)
  barDataRight1.title = `${searchConfig.subMarket === '整体' ? '非道路总体' : subMarket}市场${typePointerType[searchConfig.params.pointerType]}销量趋势`

  barDataRight2.title = `非道路市场主机厂${typePointerType[searchConfig.params.pointerType]}销量`
  barDataRight3.title = `非道路市场发动机${typePointerType[searchConfig.params.pointerType]}销量`
}

function apiSearchParam() {
  const param = { ...searchConfig.params, subMarket: searchConfig.subMarket }
  delete param.quarter
  const mk = searchConfig.dictSubMarket.find(item => item.value === param.subMarket)
  if (mk && mk.parent) {
    param.subMarket1 = mk.parent
    param.subMarket2 = param.subMarket
  } else {
    param.subMarket1 = param.subMarket === '整体' ? '' : param.subMarket
    param.subMarket2 = ''
  }
  delete param.subMarket

  return param
}

// 报表数据请求1
const getDataLetf1 = async () => {
  if (barDataLeft1.loading) return
  barDataLeft1.loading = true

  generalMachineYearSale(apiSearchParam())
    .then(data => {
      barDataLeft1.data = data
    })
    .finally(() => {
      barDataLeft1.loading = false
    })
}

// 报表数据请求2
const getDataRight1 = async () => {
  if (barDataRight1.loading) return
  barDataRight1.loading = true

  generalMachineMonthSale(apiSearchParam())
    .then(data => {
      console.log(data, 'getDataRight1')
      barDataRight1.data = data
    })
    .finally(() => {
      barDataRight1.loading = false
    })
}

// 报表数据请求3
const getDataLine2 = async () => {
  if (barDataLeft2.loading || barDataRight2.loading) return
  barDataLeft2.loading = true
  barDataRight2.loading = true
  // console.log(await getAllDateRange('上险数'), '测试AA')
  const maxMonth = await getMonthByNameAndYear('装机数', searchConfig.params.year)

  generalMachineYearYoy({ ...apiSearchParam(), month: maxMonth })
    .then(data => {
      // console.log('参数内容',data.right)
      barDataRight2.data = sortDataWithOtherLast(data.right)
    })
    .finally(() => {
      barDataRight2.loading = false
    })
  generalMachineYearSaleForMain(apiSearchParam())
    .then(data => {
      // console.log(data.left, 'getDataLine2')
      barDataLeft2.data = seriesSortOtherFirst(data.left)
      // barDataRight2.data = data.right
    })
    .finally(() => {
      barDataLeft2.loading = false
      barDataRight2.loading = false
    })
}

// 报表数据请求4
const getDataLine3 = async () => {
  if (barDataLeft3.loading || barDataRight3.loading) return
  barDataLeft3.loading = true
  barDataRight3.loading = true
  const maxMonth = await getMonthByNameAndYear('装机数', searchConfig.params.year)
  generalMachineYearSaleForEngine(apiSearchParam())
    .then(data => {
      barDataLeft3.data = seriesSortOtherFirst(data.left)
      // barDataRight3.data = data.right
    })
    .finally(() => {
      barDataLeft3.loading = false
      // barDataRight3.loading = false
    })

  generalMachineYearSaleYoy({ ...apiSearchParam(), month: maxMonth })
    .then(data => {
      //   console.log(data, 'getDataLine3')
      //  // data?.right[0].data.forEach(bar => {
      //    // if (bar.name.includes('玉柴')) {
      //      // bar.itemStyle = { color: '#E72331' }
      //     // }
      //   // })

      let d = data?.right
      d = setYuchaiColor(d)

      console.log(d, 'd')
      barDataRight3.data = sortDataWithOtherLast(d)
    })
    .finally(() => {
      barDataRight3.loading = false
    })
}

function sortDataWithOtherLast(data) {
  return data?.map(item => {
    if (item.data) {
      // 找到"其他"项
      const otherIndex = item.data.findIndex(d => d.name === '其他')
      if (otherIndex !== -1) {
        // 移除"其他"项
        const otherItem = item.data.splice(otherIndex, 1)[0]
        // 将"其他"项添加到数组末位
        item.data.push(otherItem)
      }
    }
    return item
  })
}
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';

.search_form_box {
  margin-bottom: 0px;
}

.el-row-sclorl {
  margin-top: 20px;
}

.el-row-sclorl_whlie {
  background: #fff;
  margin-top: 10px;
  border-radius: 10px;

  .is-guttered {
    margin-bottom: 0px;
  }

  .is-always-shadow {
    box-shadow: none;
    border-radius: 0px;
  }
}
</style>
<style>
.chartTips .tipItems.wrap {
  min-height: max-content;
  width: 1.1rem;
  padding: 0px;
}
</style>
