import { getYAxisName,xAxisData } from '@/views/ambience/components/commonConfigData'


export const chartList = [
    {
      title:{
              text:'农业机械销量走势（台）'
            },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        name:getYAxisName('台')
      },

      series: [
          {
            type: "line",
            data: [50, 50, 54, 58, 55, 57, 50, 53, 56, 57, 54, 55],
            name: "拖拉机",
            symbolSize:1,
          },
          {
            type: "line",
            data: [40, 20, 44, 48, 45, 47, 40, 43, 46, 47, 44, 45],
            name: "小麦机",
            symbolSize:1,
          },
          {
            type: "line",
            data: [23, 21, 24, 23, 28, 32, 21, 34, 38, 39, 22, 35],
            name: "玉米机",
            symbolSize:1,
          },
          {
            type: "line",
            data: [10, 22, 12, 20, 10, 28, 11, 24, 25, 38, 25, 12],
            name: "其他",
            symbolSize:1,
          },
        ],
    },

     {
      title:{
        text:'农业机械占比走势（%）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        name:getYAxisName('%')
      },
      series: [
        {
          type: "bar",
          data: [30, 50, 44, 25, 30, 20, 10, 30, 20, 20, 10, 25],
          name: "拖拉机",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [40, 20, 30, 25, 30, 20, 40, 20, 30, 20, 20, 25],
          name: "小麦机",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [20, 15, 16, 30, 20, 30, 20, 30, 20, 30, 30, 25],
          name: "玉米机",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [10, 15, 10, 20, 20, 30, 30, 20, 30, 30, 40, 25],
          name: "其他",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
         
        },
      ],
      
    },
    {

      title:{
        text:'农业机械动力厂家占比（%）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        name:getYAxisName('%')
      },

      series: [
        {
          type: "bar",
          data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
          name: "重汽",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
          name: "云内",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
          name: "玉柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
          name: "锡柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
    
        {
          type: "bar",
          data: [22, 32, 44, 12, 4, 23, 54, 23, 44, 11, 33, 33],
          name: "康明斯",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [23, 43, 23, 31, 42, 32, 23, 65, 41, 51, 45, 22],
          name: "潍柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [35, 26, 38, 36, 13, 44, 22, 55, 23, 56, 32, 78],
          name: "其他",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
      ],
    }


]

export default chartList