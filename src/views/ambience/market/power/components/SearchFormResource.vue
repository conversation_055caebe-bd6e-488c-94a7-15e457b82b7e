<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :span="3">
        <el-form-item prop="year">
          <el-date-picker v-model="value1" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
        value-format="YYYYMM"
        @change="dateChange" />
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            clearable: true
          },
          {
            name: '板块',
            key: 'segment'
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            key: 'subMarket2'
          }
        ]"
        :dicts="data.dictsResourceData"
      />
      <el-col :span="3">
        <el-form-item prop="manuFacturer">
          <el-select
            v-model="params.manuFacturer"
            placeholder="主机厂"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictsManuFacturer"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item prop="breed">
          <el-select
            v-model="params.breed"
            placeholder="品系"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictcping"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item prop="fuelType">
          <el-select v-model="params.fuelType" placeholder="燃料" clearable style="width: 100%">
            <el-option
              v-for="item in dictsFuelType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-button type="primary" color="#115E93" @click="toggleSearch">查询</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import {
  dictsPointerType,
  dictsManuFacturer,
  dictsFuelType,
  dictsResource,
  dictcping,
  dictsMonth,
  dictsQuarter
} from '@/utils/common/dicts.js'
import formValidate from '@/utils/hooks/formValidate.js'
const { disabledFeatureDate } = formValidate()

const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      startYear:'',   
      endYear:'',  
      year:'',  
      startMonth:'',  
      endMonth:'',  
      dataSource:'',  
      segment:'',  
      subMarket1:'',  
      subMarket2:'',  
      manuFacturer:'',  
      breed:'',  
      fuelType:'',  
      vehicleType:'',
    })
  }
})
const value1 = ref([])
function dateChange(){
  let date1 = value1.value[0]
  let date2 = value1.value[1]
  params.startYear = date1.substring(0, 4)
  params.endYear = date2.substring(0, 4)
  params.startMonth = date1.substring(date1.length - 2)
  params.endMonth = date2.substring(date2.length - 2)
}
const emit = defineEmits(['change'])

const dictsResourceData = [
  dictsResource.find(v => v.label === '货运新增'),
  dictsResource.find(v => v.label === '上险数')
]
const data = reactive({
  dictsResourceData,
  dictsPointerType,
  dictsManuFacturer,
  dictcping,
  dictsFuelType
})
const params = reactive({ ...toRaw(props.params) })

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = toRaw(params)
  emit('change', data)
}
</script>
