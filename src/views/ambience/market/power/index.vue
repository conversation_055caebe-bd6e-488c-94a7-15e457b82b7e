<template>
  <CommonTabs>
     <template #searchArea>  

    <CommonFrom
      v-model:params="formInline"
      @change="onSubmit"
      :dictsResourceValue="['1', '2', '6', '9']"
      :showProp="['breed','fuelType', 'manuFacturer', 'engineFactory', 'weightMidLight']"
      :igoneShow="['pointerType']"
      :iscike="true"
    />
    </template>

    <!-- <div class="el-row-sclorl" style="margin-left: 0; margin-right: 0"> -->
      <el-row v-loading="loading">
        <el-col :span="24" v-if="['1', '6'].includes(dataSource)">
          <Chart :="{ ...chartAlll?.chart1, subMarket1Name, heightKey: 'vehicle' }" />
        </el-col>
        <el-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
          v-if="['1', '6'].includes(dataSource)"
          style="padding-right: 10px"
        >
          <Chart
            :="{
              ...chartAlll?.chart2,
              barTotal: true,
              heightKey: 'marketenergy1',
              selectedName: '商用车月度发动机销量走势',
              titleIcon: 'data2'
            }"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="['1', '6'].includes(dataSource)">
          <Chart
            :="{
              ...chartAlll?.chart3,
              heightKey: 'marketenergy1',
              selectedName: '商用车月度发动机结构走势',
              titleIcon: 'data3'
            }"
          />
        </el-col>

        <el-col :span="24" v-if="['2'].includes(dataSource)">
          <Chart :="{ ...chartAlll?.chart4, heightKey: 'vehicle' }" />
        </el-col>
        <el-col :span="24" v-if="['9'].includes(dataSource)">
          <Chart :="{ ...chartAlll?.chart5, heightKey: 'vehicle' }" />
        </el-col>
      </el-row>
    <!-- </div> -->
  </CommonTabs>
</template>

<script setup>
import CommonTabs from '@/views/components/tabs/CommonTabs'
import Chart from '@/views/ambience/components/CommonChart/Chart'
import { genMachineCptTrendList } from '@/api/ambience/power'
import CommonFrom from '../../components/CommonFrom.vue'
import { reactive, ref } from 'vue'
import { getInitDate2 } from '@/store/modules/macro'
const formInline = reactive({
  date: getInitDate2(),
  // pointerType:'',
  dataSource: '6',
  segment: '商用车',
  subMarket1: '卡车',
  subMarket2: '',
  manuFacturer: '',
  breed: '',
  fuelType: '',
  vehicleType: '',
  engineFactory: '',
  dataType: ''
})
const chartAlll = reactive({
  chart1: {},
  chart2: {},
  chart3: {},
  chart4: {},
  chart5: {}
})

const dataSource = ref('1')

const loading = ref(false)
const subMarket1Name = ref(formInline.subMarket1)
const setData = params => {
  formInline.segment = params?.segment
  formInline.subMarket1 = params?.subMarket1
  formInline.subMarket2 = params?.subMarket2
  formInline.manuFacturer = params?.manuFacturer
  formInline.engineFactory = params?.engineFactory
  formInline.fuelType = params?.fuelType
  formInline.date = params?.date
  formInline.vehicleType = params?.vehicleType
  formInline.breed = params?.breed
  formInline.dataType = params?.dataType
}
const onSubmit = params => {
  subMarket1Name.value = params.subMarket1
  dataSource.value = params?.dataSource
  setData(params)
  getSalesSliceList(params)
}
const getSalesSliceList = async params => {
  if (loading.value) return
  loading.value = true

  genMachineCptTrendList(params)
    .then(data => {
      if (data?.chart2?.grid) data.chart2.grid = {}
      if (data?.chart3?.grid) data.chart3.grid = {}
      if (data?.chart4?.grid) data.chart4.grid = {}
      if (data?.chart5?.grid) data.chart5.grid = {}
      chartAlll.chart1 = data?.chart1
      chartAlll.chart2 = data?.chart2
      chartAlll.chart3 = data?.chart3
      chartAlll.chart4 = data?.chart4
      chartAlll.chart5 = data?.chart5
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

onMounted(() => {
  // getSalesSliceList(formInline)
})
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';
// .el-row-sclorl{
//   height:calc($bi-main-height - 160px);
//   overflow-x:hidden;
// }
</style>
