import { commonOption,xAxisDatamonth } from '../vehicle/data'
import { xAxisData } from "../../macro/economy/mockData";
export const xAxisData2 = ["333",	"W04",	"W06",	"W08",	"W10",	"W12",	"W14"	,"W16",	"W20",	"W22",	"W24",	"W26"]

export const getCommonOption = (flg = '台')=>{
  return  {
    yAxis:{
      type: "value",
      name:`单位:（${flg}）`,
      // axisLabel:{
      //   formatter: function (value) {
      //       return value + flg;
      //   }
      // }
    },
    tooltip:{
      show: true,
      trigger: "axis",
      // valueFormatter:(value) => `${value}${flg}`
    },
  }
}

const data1 = [8000, 6000, 24000, ]
const data2 = [7000, 5000, 14000, ]

const data3 = [1000, 4000, 24000, ]

const data4 = [6000, 4000, 20000, ]
const data5 = [5000, 4000, 20000, ]



export const powerChartData = [
    {
      ...getCommonOption(),
      span:24,
      title:'细分市场销售趋势图（台）',
      xAxis: {
        type: 'category',
        data: xAxisData2,
        name:`单位：（周）`,
        axisLabel:{
           interval:0,
        }
      },
      series: [
          {
            type: "line",
            data: [1000, 3000, 2400, 1800, 3500, 4700, 6000, 3300, 7600, 6700, 3400, 4500],
            name: "行业",
            symbolSize:1,
          },
          {
            type: "line",
            data: [4000, 5000, 4400, 3008, 5005, 3700, 6000, 5003, 7600, 6700, 5400, 3500],
            name: "玉柴",
            symbolSize:1,
          },
          {
            type: "line",
            data: [3300, 3100, 2400, 3300, 3800, 2200, 6100, 3400, 7800, 6900, 2200, 5500],
            name: "潍柴",
            symbolSize:1,
          },
          {
            type: "line",
            data: [3000, 2200, 3200, 3000, 4000, 3800, 5100, 4400, 6500, 7008, 4005, 4002],
            name: "康明斯",
            symbolSize:1,
          },
          {
              type: "line",
              data: [3000, 2200, 3200, 3000, 4000, 3008, 5001, 4004, 6005, 7008, 4005, 4002],
              name: "解放动力",
              symbolSize:1,
            },
        ],
    },
  {
    title:'机械工程市场挖掘动力走势（台）',
    ...getCommonOption(),
    xAxisData: xAxisDatamonth,
    series: [
      {
        type: "line",
        name:'五十铃',
        data:[3589,	9666,	10070,	8920	,8483	,9771,	7973,	8265,	7672	,6206	,5217	,4317
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'洋马',
        data:[3925,	10915	,11405	,10636,	10050	,10935,	9278,	9084,	8305,	6709,	5687	,4939
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'玉柴',
        data:[1565,	4755	,5055,	4336,	4020,	4065,	3518,	3574,	3185	,2579	,2287,	1909

        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'康明斯',
        data:[730,	1920,	2150	,2100,	2050,	1980,	1850	,1680,	1450,	1110,	1000,	980
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'久保田',
        data:[850,	2650,	2100,	2200,	2200,	2800,	2050,	2100,	2000,	1600,	1300,	1100
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'三菱',
        data:[336,	1249,	1335,	1716,	1567,	1164	,1305,	819,	633,	503,	470,	622
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'潍柴',
        data:[730,	1920	,2150	,2100	,2050	,1980,	1850,	1680,	1450,	1110,	1000,	980
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'其他',
        data:[73423.2,	41722.5,	38145.0	,38545.6,	39009.3	,37754.2	,38217.4,	39297.9,	38875.3,	41400.0,	41430.7
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'行业',
        data:[68659.8,	39579.5,	36279.6	,36783.3,	37931.3,	37266.3,	37044.0,	38671.8,	37009.5,	39130.6,	40269.3
        ],
        symbolSize:1,
      },
    ],
  },

  {
    title:'工业动力市场内燃叉车动力走势（台）',
    xAxisData: xAxisDatamonth,
    ...getCommonOption(),
    series: [
      {
        type: "line",
        name:'新柴',
        data:[35889,	98666,	108070,	89820	,84883	,89771,	87973,	88265,	87672	,86206	,85217	,84317
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'云内',
        data:[39258,	109158	,114058	,106368,	100508	,109358,	92788,	90848,	83085,	77098,	86878	,84939
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'玉柴',
        data:[1565,	4755	,5055,	4336,	4020,	4065,	3518,	3574,	3185	,2579	,2287,	1909

        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'康明斯',
        data:[7380,	19820,	21580	,28100,	28050,	18980,	11850	,18680,	11450,	11110,	18000,	9880
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'全柴',
        data:[8850,	28650,	21800,	22800,	22800,	22800,	20850,	21000,	20000,	16000,	13000,	11000
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'潍柴',
        data:[7300,	10920	,20150	,21000	,20500	,19080,	18050,	16080,	14050,	11010,	10000,	9080
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'其他',
        data:[73423.2,	41722.5,	38145.0	,38545.6,	39009.3	,37754.2	,38217.4,	39297.9,	38875.3,	41400.0,	41430.7
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'行业',
        data:[68659.8,	39579.5,	36279.6	,36783.3,	37931.3,	37266.3,	37044.0,	38671.8,	37009.5,	39130.6,	40269.3
        ],
        symbolSize:1,
      },
    ],
  },


  {
    title:'农业机械市场拖拉机动力走势（台）',
    xAxisData: xAxisDatamonth,
    ...getCommonOption(),
    series: [
      {
        type: "line",
        name:'洛柴',
        data:[20622,	33259	,44636,	55706,	67654,	78899,	90920,	102887,	114417,	125282	,136268

        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'上柴',
        data:[19671.1,	31192.6,	42682.4,	54930.8	,66714.4,	77650.0,	88464.7,	100584.5,	111638.9	,122552.7,	134033.5
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'玉柴',
        data:[20952.6,	32940.1,	45252.5,	57657.2	,69831.4,	80901.7,	91833.2,	102035.0	,112234.7	,122333.0	,133666.8
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'潍柴',
        data:[16712.6,	26742.2	,37438.9,	48819.1,	60583.6	,72394.6	,84468.8	,96423.7,	108328.1,	120202.8	,132489.2
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'其他',
        data:[17145.6	,26906.6,	37086.1,	48036.4	,58689.8,	69776.2,	80366.5,	90930.6,	101034.2,	110473.5,	120477.4
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'行业',
        data:[73423.2	,115302.7,	152673.6	,191191.4,	230053.6,	267182.3,	305083.9,	344214.6,	382922.4,	423877.5,	465838.0
        ],
        symbolSize:1,
      },
    ],
  },


  {
    title:'发动机厂家销量月度走势-情报数（台）',
    xAxisData: xAxisDatamonth,
    ...getCommonOption(),
    series: [
      {
        type: "line",
        name:'洛柴',
        data:[51367.1,	29835.3,	29429.0	,31239.4,	33335.0,	32222.7,	31602.0	,32414.1,	32486.8	,33405.7,	33174.2
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'上柴',
        data:[48902.6	,33726.0,	32212.1	,31884.0,	33427.6,	31794.1,	32580.9,	33107.3,	33662.8,	34727.3,	35189.3
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'玉柴',
        data:[61759.3,	34076.2,	32221.7,	32628.9,	32318.6	,31417.4	,33524.0,	33409.8	,35708.9,	37084.4,	38466.8
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'潍柴',
        data:[68659.8	,39579.5,	36279.6	,36783.3,	37931.3	,37266.3,	37044.0,	38671.8,	37009.5,	39130.6,	40269.3
        ],
        symbolSize:1,
      },
    ],
   },
   {
    title:'商用车月度发动机销量走势-上险数(台)',
    xAxisData: ["2024年1月",	"2月",	"2024汇总",	],
    ...getCommonOption(),
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
              // var total = 0;
              // for (var i = 0, l = params.length; i < l; i++) {
              //     total += params[i].data;
              // }
              // return params[0].name + '<br/>' + '总数' + ' : ' + total;
              var total = 0;
          var result = params[0].name + '<br/>';
          params.forEach(function (item) {
              if (item.value != null) {
                  total += item.value;
              }
          });
          params.forEach(function (item) {
              if (item.value != null) {
                  result += item.marker +item.seriesName + ' : ' + item.value.toLocaleString() +'<br/>';
              }
          });
          result += '总和 : ' + total.toLocaleString() ;
          return result;
          }

},
    series: [
      {
        type: "bar",
        data: data1,
        name: "燃料电池",
        stack: "total",
        barWidth: "40%",
      },
      {
        type: "bar",
        data: data2,
        name: "解放动力",
        stack: "total",
        barWidth: "40%",
      },
      {
        type: "bar",
        data: data3,
        name: "玉柴",
        stack: "total",
        barWidth: "40%",
      },
      {
        type: "bar",
        data: data4,
        name: "锡柴",
        stack: "total",
        barWidth: "40%",
      },
      {
          type: "bar",
          data: data5,
          name: "康明斯",
          stack: "total",
          barWidth: "40%",
          label: {
            show: true,
            position: 'top',
            formatter:(params)=>{
              const text = data1[params.dataIndex]+data2[params.dataIndex]+data3[params.dataIndex]+data4[params.dataIndex]+data5[params.dataIndex]
              return text.toLocaleString()
            }
          },
        },
    ],
  },

    {
      title:'商用车月度发动机结构走势-上险数(%)',
      ...getCommonOption('%'),
      xAxisData: ["2024年1月",	"2月",	"2024汇总",	],
      series: [
        {
          type: "bar",
          data: [10, 20, 30],
          name: "全柴",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [40, 20, 20],
          name: "解放动力",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [20, 30, 20],
          name: "玉柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [10, 20, 15, ],
          name: "锡柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [20, 10, 15, ],
          name: "康明斯",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
          // label: {
          //   show: true,
          //   position: 'top',
          //   formatter:()=>{
          //     return `100%`
          //   }
          // },
        },
      ],
    }
  
]
