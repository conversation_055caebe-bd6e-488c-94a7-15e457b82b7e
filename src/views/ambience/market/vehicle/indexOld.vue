<template>
  <CommonTabs title="商用数据">
    <CommonFrom
      :params="formInline"
      @change="onSubmit"
      :dictsResourceValue="['1', '6']"
      :showProp="['breed']"
      :iscike="true"
    />
    <div class="el-row-sclorl" v-loading="loading">
      <el-row>
        <el-col :span="24">
          <Chart
            v-bind="{
              ...salesChartData?.value,
              dataSource: dataSource,
              subMarket1Name,
              selectedName: '细分销量趋势',
              heightKey: 'vehicle'
            }"
          />
        </el-col>
        <el-col :span="24">
          <Chart
            v-bind="{
              titleIcon: 'data2',
              ...sliceChartData?.value,
              dataSource: dataSource,
              subMarket1Name,
              selectedName: '细分销量趋势',
              heightKey: 'vehicle'
            }"
          />
        </el-col>
        <el-col :span="24">
          <Chart
            v-bind="{
              titleIcon: 'data3',
              ...TrendList.value,
              dataSource: dataSource,
              subMarket1Name,
              heightKey: 'vehicle',
              selectedName: '商用车卡车市场'
            }"
          />
        </el-col>
      </el-row>
    </div>
  </CommonTabs>
</template>

<script setup>
import { onMounted } from 'vue'
import CommonFrom from '../../components/CommonFrom.vue'
import Chart from '@/views/ambience/components/CommonChart/Chart'
import CommonTabs from '@/views/components/tabs/CommonTabs'
import {
  getCommVehicleSalesSliceList,
  getCommVehicleEngineCptTrendList
} from '@/api/ambience/market'
// import { getInitDate } from '../../components/commonConfigData'
import { getInitDate2 } from '@/store/modules/macro'

import { reactive } from 'vue'
const store = useStore()
// const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '海关数据')?.value

const formInline = reactive({
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  dataSource: '6', // 数据来源
  segment: '商用车', // 板块
  subMarket1: '卡车', // 细分市场1
  subMarket2: '', // 细分市场2
  manuFacturer: '', // 主机厂
  engineFactory: '', // 发动机厂
  fuelType: '', // 燃料
  date: getInitDate2(),
  dataType: ''
})

const selected = ref({})
const sliceChartData = reactive([])
const salesChartData = reactive([])
const TrendList = reactive([])
const loading = ref(false)
const loading2 = ref(false)
const dataSource = ref(formInline.dataSource)
const subMarket1Name = ref(formInline.subMarket1)

onMounted(() => {
  // getVehicleChart(formInline)
})

const getVehicleChart = async params => {
  if (loading.value) return
  loading.value = true
  loading2.value = true
  getCommVehicleSalesSliceList(params)
    .then(data => {
      const { sliceChart = [], totlaChart = [] } = data
      sliceChartData.value = sliceChart
      salesChartData.value = totlaChart
    })
    .finally(() => {
      loading.value = false
    })

  getCommVehicleEngineCptTrendList(params)
    .then(data => {
      TrendList.value = data || []
    })
    .finally(() => {
      loading2.value = false
    })
}

const onSubmit = p => {
  dataSource.value = p.dataSource
  subMarket1Name.value = p.subMarket1
  getVehicleChart(p)
}

// onMounted(()=>{
//   onSubmit(formInline.value)
// })

// const onSubmit = async(params)=>{

//   try{
//     if(loading.value)return
//     loading.value = true;
//     const { sliceChart = [],totlaChart = [] } = await getCommVehicleSalesSliceList(params)
//     const data2 = await getCommVehicleEngineCptTrendList(params)
//     console.log(sliceChart,data2,'0000000000000000000000000')

//     sliceChartData.value = sliceChart
//     salesChartData.value = totlaChart
//     TrendList.value = data2
//     loading.value = false;
//   }catch(error){
//     loading.value = false;
//     sliceChartData.value = []
//     salesChartData.value = []
//     TrendList.value = []
//   }

// }
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';

// .el-row-sclorl{
//   height:calc($bi-main-height - 160px);
//   overflow-x:hidden;
// }
</style>
