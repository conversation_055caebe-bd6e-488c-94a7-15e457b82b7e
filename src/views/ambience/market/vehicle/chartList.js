import { getYAxisName,xAxisData } from '@/views/ambience/components/commonConfigData'
const xAxisDataQ2= ["2022年Q1",	"2022年Q2",	"2022年Q3",	"2022年Q4",	"2023年Q1",	"2023年Q2",	"2023年Q3"	,"2023年Q4",	"2024年Q1",	"2024年Q2",	"2024年Q3",	"2024年Q4"
]

export const chartList = [
    {
      title:{
        text:'卡车细分销量趋势-北斗数（台）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        type: "value",
        name:getYAxisName('台')
      },
      series: [
        {
          type: "line",
          data: [50000, 50000, 54000, 58000, 55000, 57000, 50000, 53000, 56000, 57000, 54000, 55000],
          name: "牵引车",
          symbolSize:1,
        },
        {
          type: "line",
          data: [40000, 40000, 44000, 38000, 45000, 47000, 40000, 45000,46000, 47000, 44000, 45000],
          name: "载货车",
          symbolSize:1,
        },
        {
          type: "line",
          data: [31000, 31000, 24000, 33000, 38000, 22000, 21000, 34000, 28000, 39000, 22000, 35000],
          name: "专用车",
          symbolSize:1,
        },
        {
          type: "line",
          data: [3000, 2000, 3000, 3000, 4000, 3000, 5000, 4000, 6000, 7000, 4000, 4000],
          name: "自卸车",
          symbolSize:1,
        },
      ],
    },
    {
      title:{
        text:'卡车细分销量趋势-北斗数（%）'
      },
      xAxis:{
        data:xAxisData
      },
      yAxis:{
        name:getYAxisName(),
      },
      series: [
        {
          type: "line",
          data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
          name: "牵引车",
          symbolSize:1,
        },
        {
          type: "line",
          data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
          name: "载货车",
          symbolSize:1,
        },
        {
          type: "line",
          data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
          name: "专用车",
          symbolSize:1,
        },
        {
          type: "line",
          data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
          name: "自卸车",
          symbolSize:1,
        },
      ],
    },

    {
      title:{
        text:'卡车市场发动机竞争趋势-上险数（%）'
      },
      yAxis:{
        name:getYAxisName(),
      },
      xAxis:{
        data:xAxisDataQ2
      },
      series: [
        {
          type: "bar",
          data: [10, 20, 30, 20, 30, 15, 20, 20, 15, 20, 10, 20],
          name: "重汽",
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [40, 20, 20, 20, 10, 15, 30, 20, 15, 15, 30, 20],
          name: "云内",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [20, 30, 20, 20, 20, 20, 10, 30, 20, 15, 10, 30],
          name: "玉柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [10, 20, 15, 10, 20, 30, 20, 20, 25, 25, 20, 20],
          name: "锡柴",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
        },
        {
          type: "bar",
          data: [20, 10, 15, 30, 20, 20, 20, 10, 25, 25, 30, 10],
          name: "其他",
          symbolSize:1,
          stack: "total",
          barWidth: "40%",
          // label: {
          //   show: true,
          //   position: 'top',
          //   formatter:()=>{
          //     return `100%`
          //   }
          // },
        },
      ],
    }
  ]

export default chartList