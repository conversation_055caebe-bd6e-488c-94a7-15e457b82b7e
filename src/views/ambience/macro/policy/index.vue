<template>
  <div>
    <CommonTabs :hideList="[1]" active="2" />
  </div>
</template>

<script setup>
import CommonTabs from '@/views/components/tabs/CommonTabs'

const activeName = ref('1')

// const handleClick = (tab) => {
//   activeName.value = tab
// }
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';

.root {
  min-height: calc(100vh - 132px - 32px - 1px);
}
</style>
