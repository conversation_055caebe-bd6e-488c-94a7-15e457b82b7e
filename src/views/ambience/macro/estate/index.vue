<template>
  <CommonTabs :hideList="[2]" title="产业数据">
    <template #searchArea>
      <el-form :inline="true" :model="formInline" class="demo-form-inline tabs-form">
        <el-form-item style="width: 300px">
          <el-date-picker
            v-model="formInline.date"
            type="monthrange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled-date="disabledDate"
            value-format="YYYY-MM"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="onSubmit"
            style="margin-top: 2px"
            :loading="estateLoading"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <!-- <div class="el-row-sclorl"> -->
    <el-row :gutter="15" v-loading="estateLoading" style="margin-left: 0; margin-right: 0">
      <el-col
        :xs="24"
        :sm="12"
        :md="12"
        :lg="8"
        :xl="8"
        v-for="(item, index) in estateChartList"
        :key="index"
      >
        <Chart
          :titleIcon="`data${(index % 6) + 1}`"
          v-bind="{ ...item }"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent, params, {
                shouldSort: false,
                item
              })
          }"
        />
      </el-col>
    </el-row>
    <!-- </div> -->
  </CommonTabs>
</template>

<script setup lang="jsx">
import Chart from '@/views/ambience/components/CommonChart/Chart'
import Tpis from '@/views/components/tooltip/index.vue'
import { numberFormat } from '@/utils/format'

import CommonTabs from '@/views/components/tabs/CommonTabs'
import { TooltipFormatter } from '@/utils/common/method'
let store = useStore()

const formInline = computed(() => store.state.macro.estateParams)
const estateChartList = computed(() => store.state.macro.estateChartList)
const estateLoading = computed(() => store.state.macro.estateLoading)

onMounted(() => {
  store.dispatch('macro/getMacroEnvIndData')
})

const disabledDate = time => {
  return time.getTime() > Date.now()
}

const onSubmit = () => {
  const data = formInline?.value
  store.dispatch('macro/getMacroEnvIndData', data)
}
// 提取括号内的单位
// 提取括号内的单位 - 更健壮的版本，支持中文和英文括号
const extractUnitFromParentheses = str => {
  if (!str) return ''
  // 同时匹配中文括号（）和英文括号()
  const match = str.match(/[（(]([^）)]+)[）)]/)
  return match ? match[1] : ''
}
const TooltipComponent = propos => {
  let params = propos.params
  params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })

  const unit = extractUnitFromParentheses(propos.item.yAxis.name)
  console.log('unit', propos.item.yAxis.name, unit)
  
  return (
    <Tpis {...propos} params={params}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {numberFormat(item.value, item.seriesType == 'line' ? 1 : 0) || '0'}
                {unit}
                {/* {item.seriesType == 'line' ? '%' : '台'} */}
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';
@import '@/assets/styles/bi/variables.module.scss';

.transportRoot {
  border: 1px solid $border-btn-color;
  margin-top: 10px;
  // padding-top: 10px;
}

// .el-row-sclorl{
//   height:calc($bi-main-height - 160px);
//   overflow-x:hidden;
// }
// .el-col{
//   margin-right:16px;
// }

::v-deep .el-row .el-col:nth-child(3n) {
  padding-right: 0px !important;
}
</style>
