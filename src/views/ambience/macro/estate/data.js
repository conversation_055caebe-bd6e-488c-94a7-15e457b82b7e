import { xAxisData } from '../../market/vehicle/data'
import { getCommonOption } from '../../market/power/data'
export const date =['1月','2月',	'3月','4月'	,'5月'	,'6月'	,'7月'	,'8月'	,'9月','10月',	'11月'	,'12月']
export const mothDate =date;//['1月','2月',	'3月','4月'	,'5月'	,'6月'	,'7月'	,'8月'	,'9月','10月',	'11月'	,'12月']
export const chartData = [
  {
    title:'近三年原煤月度产量同比增速（%）',
    xAxisData:date,
    ...getCommonOption('%'),
    series: [
      {
        type: "line",
        name:'2022年',
        data:[10.3,	14.8,	10.7	,10.3,	15.3,	16.1,	8.1	,12.3,	1.2,	3.1,	2.4],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [5.8,	4.3	,4.5,	4.2,	2.5	,0.1,	2.0,	0.4,	3.8,	4.6,	1.9],
        symbolSize:1,
      },
      {
        type: "line",
        data: [-4.2	,-4.2	,-2.9	,-0.8,	3.6,	2.8,	2.8,	4.4],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },


  {
    title:'近三年水泥月度产量同比增速（%）',
    xAxisData:date,
    ...getCommonOption('%'),
    series: [
      {
        type: "line",
        name:'2022年',
        data:[-17.8	,-5.6	,-18.9,	-17.0	,-12.9	,-7.0,	-13.1	,1.0,	0.4	,-4.7,	-12.3
          ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [-0.6,	10.4,	1.4	,-0.4,	-1.5,	-5.7,	-2.0,	-7.2,	-4.0,	1.6,	-0.9],
        symbolSize:1,
      },
      {
        type: "line",
        data: [-1.6	,-22.0	,-8.6,	-8.2	,-10.7,	-12.4	,-11.9,	-10.3	],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

  {
    title:'近三年钢材月度产量同比增速（%）',
    xAxisData:date,
    ...getCommonOption('%'),
    series: [
      {
        type: "line",
        name:'2022年',
        data:[-6.0,	-3.2,	-5.8,	-2.3,	-2.3	,-5.2,	-1.5,	12.5,	11.3,	7.1	,-2.6
          ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [ 3.6,	8.1,	5.0,	-1.3,	5.4	,14.5	,11.4,	5.5,	3.0,	4.2	,1.5],
        symbolSize:1,
      },
      {
        type: "line",
        data: [7.9,	0.1,	-1.6,	3.4	,3.2,	-4.0,	-6.5,	-2.4	],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

  {
    title:'近三年快递量月度同比增速（%）',
    ...getCommonOption('%'),
    xAxisData: xAxisData,
    series: [
      {
        type: "line",
        name:'2022年',
        data:[80.7,	67.0	,47.4,	30.8,	24.9,	30.4,	28.8,	24.3	,16.8,	20.8,	16.5,	10.7],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [-17.6,	32.8,	22.7,	36.4	,18.9	,11.4,	11.7,	18.3,	20.0,	22.2,	31.9,	27.9],
        symbolSize:1,
      },
      {
        type: "line",
        data: [84.8,	-15.6	,20.1,	22.7,	23.8,	17.7,	22.2,	19.5				
        ],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

  {
    title:'近三年冷鲜肉月度产量同比增速（%）',
    ...getCommonOption('%'),
    xAxisData: date,
    series: [
      {
        type: "line",
        name:'2022年',
        data:[10.3,	14.8,	10.7	,10.3	,15.3	,16.1	,8.1,	12.3,	1.2,	3.1,	2.4
          ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [ 5.8,	4.3	,4.5,	4.2	,2.5,	0.1,	2.0	,0.4,	3.8	,4.6,	1.9],
        symbolSize:1,
      },
      {
        type: "line",
        data: [  -4.2,	-4.2,	-2.9	,-0.8,	3.6,	2.8,	2.8,	4.4	],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

  {
    title:'近三年小松挖掘机开工小时月度走势（小时）',
    ...getCommonOption('小时'),
    xAxisData: xAxisData,
    series: [
      {
        type: "line",
        name:'2022年',
        data:[70.0,	47.4,	101.0,	101.4,	102.3,	93.7,	97.9	,96.0,	99.7,	102.1,	97.9,	94.3
          ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [ 44.0,	76.4,	105.0,	88.8,	90.6,	90.8,	87.4,	66.9,	68.0,	68.7,	68.2,	68.7],
        symbolSize:1,
      },
      {
        type: "line",
        data: [  80.4,	26.0,	93.0,	97.0,	101.1,	87.9,	88.4,	93.0	,95.3],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },


  {
    title:'近三年公路货运量月度走势（亿吨）',
    ...getCommonOption('亿吨'),
    xAxisData: date,
    series: [
      {
        type: "line",
        name:'2022年',
        data:[90.1,	98.9,	90.8,	90.4,	101.4,	92.8,	93.3,	93.1	,93.1,	92.9,	92.9,	92.7 ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [103.7,	103.4,	103.5,	103.4,	102.7,	102.5,	102.6,	102.7,	103.1,	103.9,	103.9	,104.2
        ],
        symbolSize:1,
      },
      {
        type: "line",
        data: [114.0,	112.7,	112.6,	112.8,	113.0,	112.1,	113.2,	114.2,	114.4
        ],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },
  {
    title:'近三年物流景气指数走势（%）',
    ...getCommonOption('%'),
    xAxisData: xAxisData,
    series: [
      {
        type: "line",
        name:'2022年',
        data:[61.1,	51.2,	48.7,	43.8,	49.3,	52.1,	48.6	,46.3,	50.6,	48.8,	46.4,	46.0
        ],
        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [44.7,	50.1,	55.5	,53.8,	51.5,	51.7,	50.9,	50.3,	53.5,	52.9,	53.3,	53.5
         	
          ],
        symbolSize:1,
      },
      {
        type: "line",
        data: [ 62.7,	67.1,	61.5,	62.4,	61.8,	61.6,	61.0,61.5,	62.4		],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

  {
    title:'近三年公路物流运价指数走势（%）',
    xAxisData: xAxisData,
    ...getCommonOption('%'),
    series: [
      {
        type: "line",
        name:'2022年',
        data: [40.0,	40.7,	50.6,	40.8,	40.0,	40.1,	45.2,	45.2,	60.4	],

        symbolSize:1,
      },
      {
        type: "line",
        name:'2023年',
        data: [30.7	,33.4,	30.5,	40.4	,40.7,	40.5,	45,	44.7	,45.1,	46.9,	22.9	,67.2],
        symbolSize:1,
      },
      {
        type: "line",
        data:[70.1,	98.9,	50.8,	56.4,	57.4,	68.8,	66.3	,67.1,	66.1	,70.9,	62.9	,76.7],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },


  


 

 

]
