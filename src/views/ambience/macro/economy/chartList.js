import { getYAxisName,xAxisData } from '@/views/ambience/components/commonConfigData'


export const chartList = [
  {
    span:24,
    title:{
      text:'GDP累计增速走势（%）'
    },
    yAxis:{
      name:getYAxisName('%')
    },

    xAxis: {
      type: "category",
      data: [	"21'Q1",	"21'Q2",	"21'Q3",	"21'Q4"	,"22'Q1",	"22'Q2",	"22'Q3",	"22'Q4",	"23'Q1",	"23'Q2",	"23'Q3"	,"23'Q4",	"24'Q1",	"24'Q2",	"24'Q3",	"24'Q4"
      ],
      axisLabel:{
         interval:0,
        //  rotate:40,
      },
      name:"单位：（年'季度'）",
      nameTextStyle:{
        align:'center',
        verticalAlign: 'bottom',
      },
      boundaryGap: ['20%', '20%']
    },
    series: [
      {
        type: "line",
        name:'GDP',
        data:[68.7,	68.3,	65.2,	64.3	,64.8	,60.4,	63.9,	62.9	,64.5,	66.3,	64.9,	65.2,	65.3	,64.7,	64.6
        ],
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:{
      text:'CPI与PPI月度增速走势（%）'
    },
    xAxis:{
      data:xAxisData
    },
    yAxis:{
      name:getYAxisName('%')
    },

    series: [
      {
        type: "line",
        name:'CPI',
        data:[60.9	,70.9,	71.5	,71.1	,62.1,	62.5,	62.7,	62.5	,62.8,	62.1,	61.6	,61.8],
        symbolSize:1,
      },
      {
        type: "line",
        name:'PPI',
        data: [89.1	,88.8	,88.3	,88.0	,86.4	,86.1	,84.2	,82.3,	80.9,	88.7,	88.7,	89.3],
        symbolSize:1,
      }
    ],
  },
  {
    span:12,
    title:{
      text:'PMI月度走势（%）'
    },
    xAxis:{
      data:xAxisData
    },
    yAxis:{
      name:getYAxisName('%')
    },
    series: [
      {
        type: "line",
        name:'PMI',
        data: [50.1,	50.2,	49.5,	47.4,	49.6,	50.2	,49.0,	49.4,	50.1,	49.2,	48.0,	47.0],
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:{
      text:'固定资产分行业投资累计增速（%）'
    },
    xAxis:{
      data:xAxisData
    },
    yAxis:{
      name:getYAxisName('%')
    },
    series: [
      {
        type: "line",
        data: [6.1	,6.3	,6.1	,5.6	,5.8	,5.7,	5.5	,5.4,	5.2	,5.2	,5.4,6],
        name: "固定资产投产累计增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [5.9	,4.6,	2.5,	2.7,	3	,3.3,	2.6,	2.5,	2.6,	2.5,	3.1,6],
        name: "制造业投产累计增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [4.3,	4.4	,4.4	,4.0	,4.1,	3.8	,4.2	,4.5,	4.2,	4.0,	3.8,4],
        name: "基础设施投产累计增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [-3.6	,-0.9,	-0.3,	-1.6,	-1.8	,-1.3	,-0.6,	-0.1,	0.1,	0.2,	-0.1,1],
        name: "房地产投产累计增速%",
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:{
      text:'进出口同比增速（%）'
    },
    xAxis:{
      data:xAxisData
    },
    yAxis:{
      name:getYAxisName('%')
    },
    
    series: [
      {
        type: "line",
        data: [-2.7,17.9,6.9,2.9,19.1,7.7,27.3,7.1,17.0,32.1,30.7,34.7,66.4],
        name: "进口累计增速%（人民币）",
        symbolSize:1,
      },
      {
        type: "line",
        data: [12.5,5.8,32.6,39.2,33.7,35.9,20.2,10.7,52.7,28.2,22.2,13.0,97.2],
        name: "出口累计增速%（人民币）",
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:{
      text:'消费同比增速（%）'
    },
    xAxis:{
      data:xAxisData
    },
    yAxis:{
      name:getYAxisName('%')
    },
    
    series: [
      {
        type: "line",
        data: [23.7,	26,	24.1,	25.3,	23.4,	25.9	,24.4,	22.4,	24.1	,24.3,	24.6,	26.6
        ],
        name: "消费品零售增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [16.4,	18.9,	12.2,	16.4,	15.8,	12.6,	17.2,	16.4,	20.5,	21.7,	24,	22.1],
        name: "消费品零售总额增速%",
        symbolSize:1,
      },
    ],
  },


  {
    span:12,
    title:{
      text:'消费者信心指数（%）'
    },
    xAxis:{
      data:xAxisData
    },
    yAxis:{
      name:getYAxisName('%')
    },
    series: [
      {
        type: "line",
        data: [21.5,	20.5,	63.2,	66.7,	66.8,	68.9,	67.9,	67.0	,67.2,	66.8,	85.5,	68.3],
        name: "2022年",
        symbolSize:1,
      },
      {
        type: "line",
        data: [91.2,	94.7,	94.9,	87.1	,88.2,86.4,	86.4,	86.5,	87.2,	87.9,	87.0	,87.6],
        name: "2023年",
        symbolSize:1,
      },
      {
        type: "line",
        data: [88.9,	89.1,	89.4,	88.2,	96.4,	96.2,	96.0,	85.8],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

]


export default chartList