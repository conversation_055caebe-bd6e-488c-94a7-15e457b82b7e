export const date =['1月','2月',	'3月','4月'	,'5月'	,'6月'	,'7月'	,'8月'	,'9月','10月',	'11月'	,'12月']
export const getCommonOption = (flg = '台')=>{
  return  {
    yAxis:{
      type: "value",
      name:`单位:（${flg}）`,
    },
    // tooltip:{
    //   show: true,
    //   trigger: "axis",
    //   valueFormatter:(value) => `${value}${flg}`
    // },
  }
}
export const statisticData = [
  {
    statisticValue: 5,
    statisticValueSuffix: "亿元",
    statisticTitle: "GDP",
    bgcolor: "#303133",
    quiteList: [
      {
        quiteName: "同比",
        mark: 1,
        quiteNum: 30,
      },
      {
        quiteName: "环比",
        mark: 0,
        quiteNum: 20,
      },
    ],
  },
  {
    statisticValue: 5,
    statisticValueSuffix: "亿元",
    statisticTitle: "GDP",
    bgcolor: "#303133",

    quiteList: [
      {
        quiteName: "同比",
        mark: 1,
        quiteNum: 30,
      },
      {
        quiteName: "环比",
        mark: 0,
        quiteNum: 20,
      },
    ],
  },
  {
    statisticValue: 5,
    statisticValueSuffix: "亿元",
    statisticTitle: "GDP",
    bgcolor: "#303133",

    quiteList: [
      {
        quiteName: "同比",
        mark: 1,
        quiteNum: 30,
      },
      {
        quiteName: "环比",
        mark: 0,
        quiteNum: 20,
      },
    ],
  },
  {
    statisticValue: 5,
    statisticValueSuffix: "亿元",
    statisticTitle: "GDP",
    bgcolor: "#303133",

    quiteList: [
      {
        quiteName: "同比",
        mark: 1,
        quiteNum: 30,
      },
      {
        quiteName: "环比",
        mark: 0,
        quiteNum: 20,
      },
    ],
  },
];
export const colors = ['#213047','#115e93','#2970da','#00a9f4']
export const lineData = {
  xAxisData: [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
  ],
  seriesData: [
    {
      name:'GDP',
      data:[50, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
    },
  ],
};

export const barData = {
  xAxisData: [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
  ],
  seriesData: [
    {
      name:'CPI',
      data:[50, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
    },
    {
      name:'PPI',
      data:[50, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
    }
  ],
};

export const lineOptions = {
  // legend: {
  //   bottom: "20px",
  //   right: "20px",
  // },
  series: [
    {
      type: "line",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "2022年",
      symbolSize:1,
    },
    {
      type: "line",
      data: [20, 40, 34, 28, 45, 37, 50, 43, 66, 77, 44, 45],
      name: "2023年",
      symbolSize:1,
    },
  ],
};

export const lineOptionsCar = {
  // legend: {
  //   bottom: "20px",
  //   right: "20px",
  // },
  series: [
    {
      type: "line",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "牵引车",
      symbolSize:1,
    },
    {
      type: "line",
      data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
      name: "载货车",
      symbolSize:1,
    },
    {
      type: "line",
      data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
      name: "专用车",
      symbolSize:1,
    },
    {
      type: "line",
      data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
      name: "自卸车",
      symbolSize:1,
    },
  ],
};

export const barOptionsCar = {
  // legend: {
  //   bottom: "20px",
  //   right: "20px",
  //   itemWidth: 10,
  //   itemHeight: 10,
  //   textStyle:{
  //     fontSize:12,
  //   }
  // },
  series: [
    {
      type: "bar",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "重汽",
      stack: "total",
      barWidth: "40%",
    },
    {
      type: "bar",
      data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
      name: "云内",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },
    {
      type: "bar",
      data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
      name: "玉柴",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },
    {
      type: "bar",
      data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
      name: "锡柴",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },

    {
      type: "bar",
      data: [22, 32, 44, 12, 4, 23, 54, 23, 44, 11, 33, 33],
      name: "康明斯",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },
    {
      type: "bar",
      data: [23, 43, 23, 31, 42, 32, 23, 65, 41, 51, 45, 22],
      name: "潍柴",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },
    {
      type: "bar",
      data: [35, 26, 38, 36, 13, 44, 22, 55, 23, 56, 32, 78],
      name: "其他",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },
  ],
};

export const barOptionscity = {
  series: [
    {
      type: "bar",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "重汽",
      stack: "total",
      barWidth: "40%",
    },
    {
      type: "bar",
      data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
      name: "云内",
      symbolSize:1,
      stack: "total",
      barWidth: "40%",
    },
  ]
    }

export const lineOptionsmach = {
  // legend: {
  //   bottom: "20px",
  //   right: "20px",
  // },
  series: [
    {
      type: "line",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "云内",
      symbolSize:1,
    },
    {
      type: "line",
      data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
      name: "玉柴",
      symbolSize:1,
    },
    {
      type: "line",
      data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
      name: "康思明",
      symbolSize:1,
    },
    {
      type: "line",
      data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
      name: "其他",
      symbolSize:1,
    },
  ],
};


export const lineOptionsship = {
  // legend: {
  //   bottom: "20px",
  //   right: "20px",
  // },
  series: [
    {
      type: "line",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "船机",
      symbolSize:1,
    },
    {
      type: "line",
      data: [20, 40, 34, 28, 45, 37, 50, 43, 66, 77, 44, 45],
      name: "单机",
      symbolSize:1,
    },
  ],
};

export const lineOptionspower = {
  // legend: {
  //   bottom: "20px",
  //   right: "20px",
  // },
  series: [
    {
      type: "line",
      data: [10, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      name: "拖拉机",
      symbolSize:1,
    },
    {
      type: "line",
      data: [40, 50, 44, 38, 55, 37, 60, 53, 76, 67, 54, 35],
      name: "小麦机",
      symbolSize:1,
    },
    {
      type: "line",
      data: [33, 31, 24, 33, 38, 22, 61, 34, 78, 69, 22, 55],
      name: "玉米机",
      symbolSize:1,
    },
    {
      type: "line",
      data: [30, 22, 32, 30, 40, 38, 51, 44, 65, 78, 45, 42],
      name: "水稻机",
      symbolSize:1,
    },
  ],
};

export const xAxispoewrData = [
  "2023年1月",
  "2023年2月",
  "2023年3月",
  "2023年4月",
  "2023年5月",
  "2023年6月",
  "2023年7月",
  "2023年8月",
  "2023年9月",
  "2023年10月",
  "2023年11月",
  "2023年12月",
]


export const lineChartData = {
  xAxisData: [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
  ],
  series: [
    {
      type: "line",
      name:'GDP',
      data:[50, 30, 24, 18, 35, 47, 60, 33, 76, 67, 34, 45],
      symbolSize:1,
    },
  ],
};

export const xAxisData = ["22'1"	,"22'2",	"22'3",	"22'4",	"22'5",	"22'6"	,"22'7",	"22'8"	,"22'9"	,"22'10",	"22'11",	"22'12"]

// GDP累计增速走势、CPI与PPI月度增速走势、制造业采购经理人指数月度走势、固定资产分行业投资累计增速、消费累计月度同比增速走势、进出口市场累计增速、消费者信息指数月度走势
export const economyChartData = [
  {
    title:'GDP累计增速走势（%）',
    ...getCommonOption('%'),
    xAxis: {
      type: "category",
      data: [	"21'Q1",	"21'Q2",	"21'Q3",	"21'Q4"	,"22'Q1",	"22'Q2",	"22'Q3",	"22'Q4",	"23'Q1",	"23'Q2",	"23'Q3"	,"23'Q4",	"24'Q1",	"24'Q2",	"24'Q3",	"24'Q4"
      ],
      axisLabel:{
         interval:0,
         rotate:40,
      },
      name:"单位：（年'季度'）",
      nameTextStyle:{
        align:'center',
        verticalAlign: 'bottom',
      },
      boundaryGap: ['20%', '20%']
    },
    series: [
      {
        type: "line",
        name:'GDP',
        data:[68.7,	68.3,	65.2,	64.3	,64.8	,60.4,	63.9,	62.9	,64.5,	66.3,	64.9,	65.2,	65.3	,64.7,	64.6
        ],
        symbolSize:1,
      },
    ],
  },

  {
    title:'CPI与PPI月度增速走势（%）',
    xAxisData:date,// xAxisData,
    ...getCommonOption('%'),
    series: [
      {
        type: "line",
        name:'CPI',
        data:[60.9	,70.9,	71.5	,71.1	,62.1,	62.5,	62.7,	62.5	,62.8,	62.1,	61.6	,61.8],
        symbolSize:1,
      },
      {
        type: "line",
        name:'PPI',
        data: [89.1	,88.8	,88.3	,88.0	,86.4	,86.1	,84.2	,82.3,	80.9,	88.7,	88.7,	89.3],
        symbolSize:1,
      }
    ],
  },
  {
    title:'PMI月度走势（%）',
    xAxisData: date,//xAxisData,
    ...getCommonOption('%'),
    series: [
      {
        type: "line",
        name:'PMI',
        data: [50.1,	50.2,	49.5,	47.4,	49.6,	50.2	,49.0,	49.4,	50.1,	49.2,	48.0,	47.0],
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:'固定资产分行业投资累计增速（%）',
    ...getCommonOption('%'),
    xAxisData: date,//xAxisData,
    series: [
      {
        type: "line",
        data: [6.1	,6.3	,6.1	,5.6	,5.8	,5.7,	5.5	,5.4,	5.2	,5.2	,5.4,6],
        name: "固定资产投产累计增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [5.9	,4.6,	2.5,	2.7,	3	,3.3,	2.6,	2.5,	2.6,	2.5,	3.1,6],
        name: "制造业投产累计增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [4.3,	4.4	,4.4	,4.0	,4.1,	3.8	,4.2	,4.5,	4.2,	4.0,	3.8,4],
        name: "基础设施投产累计增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [-3.6	,-0.9,	-0.3,	-1.6,	-1.8	,-1.3	,-0.6,	-0.1,	0.1,	0.2,	-0.1,1],
        name: "房地产投产累计增速%",
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:'进出口同比增速（%）',
    ...getCommonOption('%'),
    xAxisData: date,//xAxisData,
    series: [
      {
        type: "line",
        data: [-2.7,17.9,6.9,2.9,19.1,7.7,27.3,7.1,17.0,32.1,30.7,34.7,66.4],
        name: "进口累计增速%（人民币）",
        symbolSize:1,
      },
      {
        type: "line",
        data: [12.5,5.8,32.6,39.2,33.7,35.9,20.2,10.7,52.7,28.2,22.2,13.0,97.2],
        name: "出口累计增速%（人民币）",
        symbolSize:1,
      },
    ],
  },

  {
    span:12,
    title:'消费同比增速（%）',
    ...getCommonOption('%'),
    xAxisData: date,//xAxisData,
    
    series: [
      {
        type: "line",
        data: [123.7,	126,	124.1,	125.3,	123.4,	125.9	,124.4,	122.4,	124.1	,124.3,	124.6,	126.6
        ],
        name: "消费品零售增速%",
        symbolSize:1,
      },
      {
        type: "line",
        data: [126.4,	118.9,	122.2,	116.4,	115.8,	112.6,	117.2,	116.4,	120.5,	121.7,	124,	122.1],
        name: "消费品零售总额增速%",
        symbolSize:1,
      },
    ],
  },


  {
    span:12,
    ...getCommonOption('%'),
    title:'消费者信心指数（%）',
    xAxisData: ['1月',	'2月',	'3月',	'4月','5月',	'6月',	'7月',	'8月',	'9月',	'10月',	'11月',	'12月'],
    series: [
      {
        type: "line",
        data: [121.5,	120.5,	63.2,	66.7,	66.8,	68.9,	67.9,	67.0	,67.2,	66.8,	85.5,	68.3],
        name: "2022年",
        symbolSize:1,
      },
      {
        type: "line",
        data: [91.2,	94.7,	94.9,	87.1	,88.2,86.4,	86.4,	86.5,	87.2,	87.9,	87.0	,87.6],
        name: "2023年",
        symbolSize:1,
      },
      {
        type: "line",
        data: [88.9,	89.1,	89.4,	88.2,	96.4,	96.2,	96.0,	85.8],
        name: "2024年",
        symbolSize:1,
      },
    ],
  },

]




export const list = [
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
];

export const listData = [
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  {
    name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
    date: "2024-12-17",
  },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
  // {
  //   name: "打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业，打造高水平对外开放平台，支持航空维修产业",
  //   date: "2024-12-17",
  // },
];
