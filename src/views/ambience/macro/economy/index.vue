<template>
  <CommonTabs :hideList="[2]" title="经济数据">
    
    <template #searchArea>
      <el-form :model="formInline" label-width="0" :inline="true" class="tabs-form">
        <el-row :gutter="16" style="margin-right: unset">
          <el-col :xs="16" :sm="16" :md="6">
            <el-form-item>
              <el-date-picker
                v-model="formInline.date"
                type="monthrange"
                value-format="YYYY-MM"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabledDate="disabledDate"
                range-separator="~"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="8" :md="3">
            <el-form-item>
              <el-button type="primary" @click="onSubmit" :loading="economyLoading">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <el-row v-loading="economyLoading">
      <el-col :span="24" v-for="(item, index) in economyChartList" :key="index">
        <Chart
          :titleIcon="`data${(index % 6) + 1}`"
          v-bind="{
            ...item,
            heightKey: 'vehicle'
          }"
        />
      </el-col>
    </el-row>
  </CommonTabs>
</template>

<script setup>
import Chart from '@/views/ambience/components/CommonChart/Chart'
import CommonTabs from '@/views/components/tabs/CommonTabs'

let store = useStore()

const formInline = computed(() => store.state.macro.economyParams)
const economyChartList = computed(() => store.state.macro.economyChartList)
const economyLoading = computed(() => store.state.macro.economyLoading)

onMounted(() => {
  store.dispatch('macro/getChartList')
})

const disabledDate = time => {
  return time.getTime() > Date.now()
}

const onSubmit = () => {
  const data = formInline?.value
  store.dispatch('macro/getChartList', data)
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
</style>
