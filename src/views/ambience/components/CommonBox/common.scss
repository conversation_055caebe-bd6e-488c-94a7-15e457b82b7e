@import "@/assets/styles/bi/variables.module.scss";

// 下划线颜色
$border-btn-color: #f5f5f5;

// 按钮颜色
$btn-color: #0B5FC5;

.demo-form-inline {
  .el-form-item:last-child{
    width: 80px;
    margin-right: 0px;
  }
}
.el-form--inline .el-form-item {
  width: 180px;
  margin-right: 20px;

  ::v-deep .el-input__wrapper {
  }
  ::v-deep .el-select__wrapper,
  ::v-deep .el-button {
  }
}

.el-button{
  background-color: $btn-color;
  width: 80px;
  border-radius: 8px;
  &.el-button--primary {
    padding-left: 24px;
    padding-right: 24px;
  }
}


.commonRoot {
  padding: 10px;
  // background: #fff;
  // margin-bottom: 20px;
  // min-width: 1300px;

}




.commonBg{
  background: #213047;
}

.commonPt{
  padding-top: 15px;
}

.commonPb{
  padding-bottom: 15px;
}

.is-guttered{
  margin-bottom: 15px;
}

::v-deep .el-tabs__content{
  // min-height: 600px;
  // background-color: $bi-main-background-color;

}


.el-row-sclorl{
  // height:calc($bi-main-height - 150px);
  overflow-y:auto;
  width:100%;
  overflow-x:hidden;
  min-width:calc(100vw - $bi-sidebar-width - 80px);
}

.el-row,.el-col-24{
  // width: 100% !important;
}

/* 修改滚动条长度 */
::v-deep .el-row-sclorl::-webkit-scrollbar {
		width:4px !important;
	}

	/* 修改滚动条内滑块的长度 */
	::v-deep .el-row-sclorl::-webkit-scrollbar-thumb {
		height: 2px;
		/* 水平滚动条内滑块的高度 */
		width: 2px;
		/* 垂直滚动条内滑块的宽度 */
		background-color: #ccc;
	}



