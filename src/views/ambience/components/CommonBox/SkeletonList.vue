<template>
  <div class="listRoot" :style="styleRoot">
       <el-row :gutter="16" class="search">
        <el-col :span="6">
          <el-date-picker
            v-model="params.date"
            type="daterange"
            range-separator="-"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%"
            :disabledDate="disabledDate"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="params.fileName"
            placeholder="请输入关键字"
            clearable
            />
        </el-col>
        <el-col :span="2" style="padding-right:0">
          <el-button  type="primary" color="#115E93" @click="onSearch" style="margin-right:0;width:100%"
            >搜索
          </el-button
          >
        </el-col>
      </el-row> 
    <el-skeleton :rows="5" animated v-if="loading" />
   <div class="list-box" v-else>
    <div class="list" :class="newsData && newsData.length > 0 ? 'list1' : ''" >
      <div v-if="newsData && newsData.length > 0">
        <div
        class="listItem moderItem"
        v-for="(item, index) in newsData"
        :key="index"
      >
          <div class="moderitem_span">
            <div style="font-weight: 600">报告 | </div>
            <div class="name">{{ item?.name }}</div>
            <div class="pdf">{{ item?.type }}</div>
            <div class="date">{{ item?.createTime }}</div>
          </div>
          <div class="moder-right">
            <!-- <a class="down" @click="()=>{getLisdetail({id:item.id})}">预览</a> -->
            <a class="down" 
              @click="handleExport(item)" 
              style="margin-right: 0px;"
              v-hasPermi="['ambiece_down']"
            >下载</a>
          </div>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[15,20,30, 50]"
          :total="count"
          @size-change="sizeChange"
          @prev-click="sizeChange"
          @current-change="sizeChange"
          @next-click="sizeChange"
        />
      </div>
      <el-empty description="暂无数据" v-else />
    </div>
  </div>
  </div>
</template>

<script setup>
import BlockTitle from "@/views/components/BlockTitle.vue";
import { getQueryFileList,getQueryFileLisdetail } from '@/api/ambience/macro'
import { disabledDate } from '../commonConfigData'
import BiPagination from '@/views/components/BiPagination.vue'
const { proxy } = getCurrentInstance();
defineProps({
  title: {
    type: String,
    required: false,
  },
  styleRoot:{
    type:Object,
    required: false,
    default: {},
  }
});

  const currentPage = ref(1)
  const pageSize = ref(15)
  const newsData = ref([])
  const count = ref(0)
  const params = ref({
     date: undefined,
     fileName: ''
   })
   
  const loading = ref(false)


  onMounted(()=>{
    getNewList()
  })

  const onSearch = ()=>{
    getNewList()
  }

  function handleExport(row) {
  proxy.download("upload/download",{
    fileName:row.fileName,
    fileKey:row.fileKey
  }, row.fileName);
}

  

  const getNewList = async()=>{
    try{
      if(loading.value)return
       loading.value = true
      const res = await getQueryFileList({
        pageNum:currentPage?.value,
        pageSize:pageSize?.value,
        ...params.value
      })


      const { rows = [],total = 0 } = res;
      const _rows =rows.map(( item)=>{
        const { fileName = '' } = item
        const [val,val2] = fileName.split(".");
        return{
          ...item,
          name:val,
          type:val2
        }

      })
      newsData.value=_rows;
      count.value = total;
      loading.value = false

    }catch(error){
        console.log(error)
        loading.value = false
    }
    
  }


  const getLisdetail = (query)=>{
    const res = getQueryFileLisdetail(query)
    console.log(res,'res')
  }

  const sizeChange = (val)=>{
    getNewList();

  }



</script>

<style lang="scss" scoped>
@import "@/views/ambience/components/CommonBox/common.scss";
.el-pagination{
  display: flex;
  justify-content: end;
}
.search{
   dispatch:flex;
   justify-content:end;
   margin-right:0px !important;
   padding-right:0px !important
 }

.listRoot {
  // margin-top: 40px;
  border: 1px solid $border-btn-color;
  // padding: 10px;
  padding-bottom: 0px;
  // background-color: #fff;
  .listTop {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $border-btn-color;
    padding-bottom: 6px;
    .block-title {
      height: 28px;
    }
  }

  .lookMore {
    color: $btn-color;
    min-width: 70px;
    line-height: 20px;
    font-size: 16px;
  }

  .moderitem_span{
    display:flex
   
  }

  .listItem {
    display: flex;
    border-bottom: 1px solid $border-btn-color;
    margin: 0px;
    
  }

  .moderItem {
    font-size: 16px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    span {
      height: 16px;
    }
    .name {
      white-space: nowrap; /* 保证文本在一行内显示 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
      flex: 1;
      padding: 0px;
      width: 60px;
      margin-right: 0px;
      margin-left: 2px;
    }
    .name:hover{
        color: #115E93;
        cursor: pointer;
      }
    .pdf {
      margin: 0px 10px;
      color: $btn-color;
    }
    .moderitem_span {
      display: flex;
      flex: 1;
    }
    .moder-right{
      min-width: 50px;
    }
    .date {
      line-height: 18px;
      margin-right: 20px;
    }
    .down {
      color: $btn-color;
      margin-right: 10px;
    }
  }
  .list1{
    background-color:#fff;
    padding:10px;
  }

  .list .listItem:last-child {
    border-bottom: 0px;
  }
}

.listRoot{
    height:calc(100vh - 230px);
    position:reactive;
    overflow-y:auto;
    ::v-deep .el-pagination--default{
      position:absolute;
      bottom:30px;
      right:24px;
    }

}

::v-deep .el-pager .is-active {
    background: #115E93;
    color: #fff;
}

.list{
      height:calc(100% - 60px);
      overflow-y:auto;
      padding-bottom:20px
}

::v-deep .list::-webkit-scrollbar {
		width: 4px !important;
	}


::v-deep  .list::-webkit-scrollbar-thumb {
		height: 3px;
		/* 水平滚动条内滑块的高度 */
		width: 2px;
		/* 垂直滚动条内滑块的宽度 */
		background-color: #ccc;
	}

  ::v-deep .el-empty{
    background-color:#fff
  }

  .list-box{
    background-color:#fff;
    height:calc(100% - 60px);
    overflow-y:auto;
    padding-bottom:20px
  }
</style>
