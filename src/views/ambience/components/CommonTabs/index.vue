<template>
  <div class="wrap">
    <el-tabs v-model="activeName" type="border-card" class="bi-tabs">
      <el-tab-pane class="custom-scrollbar" label="数据区" name="1" v-if="title" key="1">
        <slot></slot>
      </el-tab-pane>
      <el-tab-pane class="custom-scrollbar" label="新闻区" name="2" key="2">
        <NewS :key="activeName" />
      </el-tab-pane>
      <el-tab-pane label="报告区" name="3" key="3">
        <ReportList :key="activeName" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
// import SkeletonList from "@/views/ambience/components/CommonBox/SkeletonList";
import ReportList from '@/views/components/tabs/ReportList.vue'
import NewS from '../NewS.vue'
// import { list } from '../../macro/economy/mockData'

const props = defineProps({
  // label
  title: {
    type: String,
    require: false,
    default: '数据区'
  },
  title2: {
    type: String,
    require: false,
    default: '新闻区'
  },
  active: {
    type: String,
    require: false,
    default: '1'
  }
})

const activeName = ref(props.active)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.wrap {
  height: $bi-main-height;
  :deep(.el-tabs) {
    border-radius: 8px;
    height: 100%;
    background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);
    .el-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
  :deep(.el-tabs--border-card) {
    & > .el-tabs__content {
      padding: 0 16px 16px 16px;
    }
  }
}
</style>
