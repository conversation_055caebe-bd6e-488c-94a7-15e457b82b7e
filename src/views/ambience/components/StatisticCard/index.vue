<template>
  <div class="card-root" :style="{ 'background-color': bgcolor }">
    <div class="statisticLeft">
      <span class="statisticValue">
        {{ statisticItem?.statisticValue }}
      </span>
      <span class="statisticValueSuffix">
        {{ statisticItem?.statisticValueSuffix }}
      </span>
    </div>
    <div class="statisticTitle">
      {{ statisticItem?.statisticTitle }}
    </div>
    <div class="statisticFooter">
      <div
        class="quiteList"
        v-for="(item, index) in statisticItem?.quiteList"
        :key="index"
      >
        <span class="quiteName">{{ item?.quiteName }}</span>
        <!-- <el-icon v-if="item?.mark === 1" style="color: #67c23a">
          <Top />
        </el-icon> -->
        <img :src="upIcon" v-if="item?.mark === 1" />
        <img :src="downIcon" v-if="item?.mark === 0" />
        <!-- <el-icon v-if="item?.mark === 0" style="color: #f56c6c">
          <Bottom />
        </el-icon> -->
        <span class="quiteNum">{{ item?.quiteNum }}</span>
        <span class="quiteSuffix">{{ item?.quiteNum ? "%" : "" }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import upIcon from "@/assets/images/up.png";
import downIcon from "@/assets/images/down.png";

defineProps({
  // 背景图片
  bgcolor: {
    type: String,
    default: "#303133",
  },
  statisticItem: {
    type: Object,

    // 右边数字
    statisticValue: {
      type: Number,
      require: true,
    },

    // 右边数据后缀
    statisticValueSuffix: {
      type: String,
      require: true,
    },
    // 左上角文字
    statisticTitle: {
      type: String,
      require: true,
    },
    // 右下角指标
    quiteList: {
      type: Array,
    },
  },
});
</script>

<style lang="scss" scoped>
.card-root {
  height: 120px;
  padding: 10px;
  position: relative;
  color: #fff;
  /* // background-color: #303133; */

  .statisticLeft {
    position: absolute;
    top: 24px;
  }

  .statisticValue {
    font-size: 60px;
  }

  .statisticTitle {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 20px;
  }

  .statisticFooter {
    position: absolute;
    bottom: 10px;
    right: 0px;
    font-size: 12px;
    display: flex;
    align-items: end;

    .quiteList {
      margin-right: 10px;

      img {
        height: 15px;
        position: relative;
        top: 2px;
        margin-left: 6px;
        margin-right: 2px;
      }
    }
  }
}
</style>
