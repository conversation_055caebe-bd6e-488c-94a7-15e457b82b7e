<template>
  <el-card class="box-card" >
    <BlockTitle :title="title" v-if="title" />
    <div class="ratio-width" :class="chartPadding" :style="_paddingStyle">
      <div ref="lineRef" class="ratio-width__wrap" :style="chartStyle" />
    </div>
  </el-card>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, onUnmounted, ref } from 'vue'
import BlockTitle from '@/views/components/BlockTitle.vue'
import echartsResize from '@/utils/hooks/echartsResize.js'
const props = defineProps({
  type: {
    type: String,
    default: 'line'
  },
  // 左上标题
  title: {
    type: String,
    require: false
  },

  // 顶部中间标题
  topCenterTitle: {
    type: String,
    require: false
  },

  // 折线图的配置
  lineOptions: {
    type: Object,
    require: false,
    default: {}
  },
  // x轴的数据
  xAxisData: {
    type: Array,
    require: true,
    default: []
  },
  // 只适用只有一条折线图，如果多条需要配置lineOptions进行覆盖
  seriesData: {
    type: Array,
    require: true,
    default: []
  },
  // 图表解释
  legend: {
    type: Object,
    require: false
  },
  //图表额外配置覆盖
  lineOptions: {
    require: false,
    type: Object
  },
  // 外层样式
  chartStyle:{
    type: Object,
    default: {},
  },
  // X轴坐标倾斜度
  rotate:{
    type: Number,
    require: false,  
  },
  paddingStyle:{
    type: Object,
    default: null,
  },
});

const chartPadding = ref('chartPadding1')
const _paddingStyle = ref(props?.paddingStyle || {})
// 初始化实例
let myChart = null
const lineRef = ref(null)
onMounted(() => {
  myChart = echarts.init(lineRef.value)
  getInitOption()
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', checkScreenSize)
  window.addEventListener('resize', resizeHandler)
})

onUnmounted(() => {
  const { resizeHandler } = echartsResize(myChart)
  window.removeEventListener('resize', resizeHandler)
  window.removeEventListener('resize', checkScreenSize)
})

const checkScreenSize = () => {
  const windowWidth = window.innerWidth

  if (windowWidth <= 1500) {
    chartPadding.value = 'chartPadding2'
  } else {
    chartPadding.value = 'chartPadding1'
    if(props?.paddingStyle){
    _paddingStyle.value={
        paddingBottom: '25%'
      }
    }
   
  }
}

// 构建options,配置对象
/** @type EChartsOption */
const getInitOption = () => {
  let seriesOptions = [];
  let { seriesData = [], type, topCenterTitle = "" ,rotate = 0,lineOptions = {}} = props;
  // const yAxis = lineOptions?.yAxis || {}
  const { yAxis = {},xAxis = {},...otherOptions } = lineOptions || {}
  // console.log(rotate,'rotate',props)

  // 多重数据
  if (seriesData && seriesData.length) {
    seriesOptions = seriesData.map(item => {
      return {
        type: type || 'line',
        // symbol: "none",
        symbolSize: 1,
        ...item
      }
    })
  }

  const options = {
    color: [
      '#c2e7f2',
      '#5fceff',
      '#02a9f4',
      '#2970da',
      '#115e93',
      '#2970da',
      '#051c2c',
      '#67C23A',
      '#f3d19e'
    ],
    tooltip: {
      show: true,
      trigger: 'axis'
    },

    xAxis: {
      type: 'category',
      data: props?.xAxisData,
      axisLabel:{
         interval:0,
         rotate:rotate || 0,
      },
      ...xAxis,
    },
    yAxis: {
      type: 'value',
      splitLine: {
            show: false // 不显示x轴网格线
        },
        min: function (value) {
         return value.min;
       },
       ...yAxis
    },
    legend: {
      bottom: '0px',
      right: '40px',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 14,
      },
    },
    series: [...seriesOptions],
    ...otherOptions
  }

  if (topCenterTitle) {
    options.title = {
      text: topCenterTitle,
      top: '20px',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    }
  }

  // 通过实例.setOption(options)
  myChart.setOption(options)
}
</script>

<style lang="scss" scoped>
.chartPadding1 {
  padding-bottom: 50%;
}
.chartPadding2 {
  padding-bottom: 60%;
}
</style>
