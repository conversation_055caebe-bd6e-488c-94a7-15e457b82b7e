import { getAddTotal } from '../commonConfigData'
import {numberFormat} from '@/utils/format.js'
import { dataConvertForPercent,dataConvertForLine ,dataConvertForPercentTopN} from '@/utils/dataconvert'
import {
  dictsResource
 } from '@/utils/common/dicts.js'
 import { lineColor } from '@/views/components/echarts/config'
export const selectedNameObj = {
    '细分销量趋势':['牵引车',"自卸车",'专用车',"载货车"],
    '农业机械':['花生机','水稻机','拖拉机','玉米机','小麦机'],
    '工业动力':['空压机','内燃叉车','钻机'],
    '工程机械':['高空平台','矿用车','挖掘机','装载机'],
    '商用车卡车市场':['重汽','云内','玉柴','锡柴','全柴','康明斯','潍柴'],
    '通机近三年占比':['玉柴','常柴','全柴','潍柴','洛柴','上柴'],
    '燃料结构走势':['纯电动','燃料电池','混合动力'],
    '燃料结构份额走势':['纯电动','燃料电池','混合动力'],
    // "商用车月度发动机销量走势":['玉柴','康明斯','潍柴','全柴','云内','柳州五菱','东风轻发','锡柴','沈阳三菱','朝柴']
}



const chartHeight={
}



  

    // 截取指定的数据
  export const seriesSrice = (selectedNameKey = '',seriesList = [])=>{
    
    if(!selectedNameKey)return seriesList
    const selectedName = selectedNameObj[selectedNameKey] || [];
    if(selectedNameKey === '燃料结构走势'){
      return seriesList.filter(({name = ''})=>(selectedName.includes(name)))
    }
    // const data = seriesList.filter(({name = ''})=>(selectedName.includes(name)))
    let data = dataConvertForPercent(seriesList,selectedName) 
    if(selectedNameKey === '燃料结构份额走势'){
      data = data?.map((item)=>{
        let { name = ''} = item 
        return {
          ...item,
          name:name === "其他" ? '传统动力':name,
        }
      })
      data = dataConvertForLine(data)
    }
     return data
  }



  export const getInitactiveOption = (props) => {
    let title = props?.title || {};
    const rotate = props?.rotate;
    const yAxis = props?.yAxis || {}
    let xAxis = props?.xAxis || {}
    // TODO 年份升序排序
    const tooltip = props?.tooltip || {
      show:true,
      trigger: 'axis'
    } 
    let legend = props?.legend || {}
    let { series = [],color = undefined } = props
    const otherOptions = props?.otherOptions || {}
    const { barTotal=false,selectedName = '' } = props

    let isSort = false;

    // 重新对series排序
   let istSortArr = ['商用车月度发动机销量']
   istSortArr?.forEach((i)=>{
      if(title?.text && (title?.text?.indexOf(i) > -1)){
        isSort = true
        return
      }
    })
    if(isSort){
      let _series =  sortData([...series])
      xAxis = {
        ...xAxis,
        // 自定义x轴的data
        data:getXAxisData(_series)
      }
   
    }

    let dataZoom = []

    let _rotate = rotate 
    
  
    if(series && series.length >0){
        // 缩放
      const len = series?.map(({ data = [] })=>(data?.length))
      if(len && Math.max(...len) > 24){
        // _rotate = rotate || 30
        dataZoom = [
          {
            type: 'inside',
          },
        ]
      }

      if(series[0]?.type === 'line'){
        let _series= [...series]
        series = _series.map((item)=>{
         return{
          ...item,
          stack:''
         }
        })
        color = lineColor
      }
       
    } 

    // 对标题进行拼接
    if(title?.text){
     let pre =  ''
     if(props?.dataSource){
      pre = props?.dataSource ? dictsResource?.filter(({value})=>(value === props?.dataSource))[0]?.label : ''
      pre = `-${pre}`
     }
     let textContent = title.text.includes('走势') ? `新能源${props?.subMarket1Name || ''}${title.text}${pre}` : `${props?.subMarket1Name || ''}${title.text}${pre}`
     if(title.text.includes('商用车月度发动机销量走势') || title.text.includes('商用车月度发动机结构走势')){
         textContent = title.text;
     }
     title ={
      ...title,
     text: textContent
     }
    }

    // // 指定展示的数据
    if(selectedName){
      series = seriesSrice(selectedName,[...series])
    }else if(series && series.length > 0 && series[0].type === 'bar'){
      series = dataConvertForPercentTopN([...series],0,0)

    }

  
    let units = ''
    let precision = 1
    if (yAxis?.name?.indexOf('%') > -1) {
      units = '%'
      // yAxis.max = 100
    }

    if (yAxis?.name?.indexOf('%') > -1) {
      units = '%'
      if(series && series[0]?.type === 'bar'){
        yAxis.max = 100
      }

    }

  
   
    
    if (yAxis?.name?.indexOf('台') > -1) {
      precision = 0;
      units='台'
    }

    if (yAxis?.name?.indexOf('万台') > -1) {
      precision = 2;
      units='万台'
    }


    if(series && series[0]?.type === 'pie'){
      legend = { show:false}
    }

    if(title?.text?.indexOf('燃料结构走势') > -1){
      precision = 2;
      units='万台'
    }

    // console.log(props,)
    let arrigronTitle = ['消费者信心','公路','物流','小松']
    arrigronTitle?.forEach((i)=>{
      if(title?.text && (title?.text?.indexOf(i) > -1)){
        yAxis.min=(value)=>{
          return parseInt(value.min -2)
         }
         yAxis.max =(value)=>{
            return parseInt(value.max + 2)
        }
      }
    })
   let istextBreak = false;

   let istextBreakArr = ['细分市场销售趋势图','商用车月度发动机']
    istextBreakArr?.forEach((i)=>{
        if(title?.text && (title?.text?.indexOf(i) > -1)){
          istextBreak = true
          return
        }
    })

    // 是否x轴换行
    if(istextBreak && series.length){
      let _series = [...series]
      series = _series.map((item)=>{
        const { data = []} = item;
        let _data =  data.map(v => {
              let name = v.name
              let index =  name?.indexOf('年') || -1
              // console.log( v.name,' v.name')
              // 遇到年换行
                name =  index > -1  ?  name.slice(0, index+1) + '\n' + name.slice(index+1) : name;
              return {
                ...v,
                name,
              }
          })
          return {
            ...item,
            data:_data
          }

      })
    }
    console.log(title?.text,'series')
    const options = {
      color:color,
      legend,
      tooltip: {
        ...tooltip,
      },
      tooltipUnits:units,
      precision,
      title:title?.text,
      xAxis: {
        type: 'category',
        ...xAxis,
        axisLabel:{
          //  interval:0,
           rotate:_rotate || 0,
           ...xAxis?.axisLabel
        },
       
      },
      yAxisName:yAxis?.name || '',
      yAxis: {
        type: 'value',
        splitLine: {
              show: false // 不显示x轴网格线
          },
         ...yAxis,
       
        //  min:(value)=>{
        //   return value.min
        //  },
        //  max:(value)=>{
        //   return value.max
        //  },
        //  axisLabel: {
        //   margin: 40 // 增大标签与轴线之间的距离
        // },
         scale:false
        //  max: 100, // 设置最大值
         
      },
      series,
      // dataZoom:{},
      barTotal,
      showTotal:barTotal,
      istextBreak: true,
      legendWrap: false,
      ...otherOptions
    }

    

    return options
    
  }



  export const defaultHeight = {
     // 两个图并排
    'marketenergy1':'50%',
    // 饼图
    'marketenergy2':'82%',
    // 单个图
    'vehicle':'300px',
    'electricity':'25%',
    // 三个图
    'estate':'60%'

  }


  // 排序
  export const sortData = (list)=>{

  let _list = list.map((item)=>{
    const { data = [] } = item
    let _data = sortYearWeek(data);
    return {
      ...item,
      data:_data
    }

  })

    return _list
  }




  const sortYearWeek = yearMonths => {
    // console.log(yearMonths,'000000')
    const yms = yearMonths?.sort((av, bv) => {
      let a = av?.name
      let b = bv.name
      let a1 = a?.replace(new RegExp('-', 'g'), '')
      let b1 = b?.replace(new RegExp('-', 'g'), '')
      if (a && a.indexOf('NULL') > -1) {
        a1 = a1?.replace(new RegExp('NULL', 'g'), '')
        b1 = b1?.replace(new RegExp('NULL', 'g'), '')
      }
      if (b && b.indexOf('NULL') > -1) {
        a1 = a1?.replace(new RegExp('NULL', 'g'), '')
        b1 = b1?.replace(new RegExp('NULL', 'g'), '')
      }
      if (a && a.indexOf('汇总') > -1) {
        a1 = a1?.replace(new RegExp('汇总', 'g'), '')
        a1 = `${a1}13`
      }
  
      if (b1 && b1.indexOf('汇总') > -1) {
        b1 = b1?.replace(new RegExp('汇总', 'g'), '')
        b1 = `${b1}13`
      }
  
      let _a = a1 === NaN ? 0 : Number(a1)
      let _b = b1 === NaN ? 0 : Number(b1)
      return a1 * 1 - b1 * 1
    })
    return yms
  }


// 重新定义x轴的data
const getXAxisData = (value) => {
  const data = value[0] ? (value[0].data ? value[0].data : []) : []
  let _data =  data.map(v => {
    let name = v?.name || ''
    let [year = '', week = ''] = name?.split("-")

    if(!week){
      return `${week}月`
    }


    if (week === '汇总') {
      return `${year}年\n${week}`
    }
    week = week * 1
    if(week == '01'){
      return  `${year}年\n${week}月`
    }
    return `${week}月`
  })
  return _data
}

