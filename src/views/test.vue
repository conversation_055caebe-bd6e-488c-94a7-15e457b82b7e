<template>
  <div style="padding: 50px;">
    <YcCharts :options="option" :title="title" :loading="loading" />
  </div>
</template>
<script setup>
import YcCharts from '@/components/YcCharts/index.vue';
import { onMounted, reactive } from 'vue'
const title = ref('我的图表')
const loading = ref(false)
const option = reactive({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // Use axis to trigger tooltip
      type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
    }
  },
  xAxis: {
    type: 'value',
    axisTick: {
      show: true, //显示x轴刻度
      alignWithLabel: true //刻度线与标签对齐
    },
  },
  yAxis: {
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  },
  series: [
    {
      name: 'Direct',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [320, 302, 301, 334, 390, 330, 320]
    },
    {
      name: 'Mail Ad',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: 'Affiliate Ad',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: 'Video Ad',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [150, 212, 201, 154, 190, 330, 410]
    },
    {
      name: 'Search Engine',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [820, 832, 901, 934, 1290, 1330, 1320]
    }
  ]
});
onMounted(() => {
  loading.value = true;
  setTimeout(() => {
    // title.value = "测试图表"
    // option.series[0].data[0] = { value: 200, name: "Direct" }
    loading.value = false;

  }, 400);
});
</script>
