<template>
  <el-dialog
    v-model="modelValue"
    :close-on-click-modal="false"
    top="4vh"
    width="80vw"
    append-to-body
  >
  <template v-if="props.isPage">
    <yjhInfo :page-query="props.query" :is-page="props.isPage" @back="modelValue = false" />      
  </template>
    <template v-else>
      <newInfo :page-query="props.query"  :is-page="false" @back="modelValue = false" />
    </template>
  </el-dialog>
</template>
<script setup>
import newInfo from './index.vue'
import yjhInfo from './yjhInfo.vue';

const props = defineProps({
  query: {
    type: Object,
    default: () => ({ newsInfoId: '', newsTagId: '', searchKey: '' ,infoContent:''})
  },
  isPage: {
    type: Boolean,
    default: false
  }
})

const modelValue = defineModel({ require: true })
</script>
<style scoped lang="scss">
.wrap {
  width: calc(80vw - 36px);
  height: 86vh;
  min-height: 86vh;
  padding: 0;
  margin: 0;
  overflow: auto;
}
</style>
