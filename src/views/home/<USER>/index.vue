<template>
  <div class="wrap">
    <el-row>
      <el-col :span="24">
        <el-card>
          <template #header>
            <block-title title="需求录入" />
          </template>
          <el-table :data="data.list" class="table-box" style="width: 100%">
            <el-table-column prop="requirement" label="需求描述" />
            <el-table-column prop="purpose" label="用途描述" align="right" />
            <el-table-column prop="department" label="需求部门" align="right" />
            <el-table-column prop="needer" label="需求人" align="right" />
            <el-table-column prop="date" label="需求时间" align="right" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card>
          <template #header>
            <block-title title="需求审核流程" icon="data2" />
          </template>
          <flow />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import flow from './components/flow.vue'
const json = {
  requirement: 'XXXXX',
  purpose: '用于汽车运输产业',
  department: '1231',
  needer: '张易之',
  date: '2024年5月23日'
}
const list = []
for (let i = 0; i < 4; i++) {
  list.push(json)
}
const data = reactive({
  list: list
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
:deep(.el-table th.el-table__cell) {
  background-color: #fff;
  color: var(--el-text-color-regular);
  font-weight: bold;
  font-size: 14px;
}
:deep(.el-row) {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
