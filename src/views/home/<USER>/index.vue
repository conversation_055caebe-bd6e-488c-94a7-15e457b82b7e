<template>
  <div class="wrap" :class="device === 'mobile' ? 'mobile-wrap' : ''">
    <div :class="device === 'mobile' ? '' : 'bi-search-bar'">
      <el-row :gutter="16">
        <el-col :span="10">
          <el-form-item prop="year">
            <el-date-picker
              v-model="data.params.year"
              type="daterange"
              range-separator="~"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :disabled-date="disabledFeatureDate"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item prop="pointerType">
            <el-input
              v-model="data.params.keyword"
              :prefix-icon="Search"
              placeholder="输入搜索关键字"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item>
            <el-button type="primary" @click="toggleSearch" style="width: 100%">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <el-tabs v-model="data.tabsActive" type="border-card" class="bi-tabs" @tab-change="changeTabs">
      <el-tab-pane label="一句话信息" name="0" v-if="data.isShowOne">
        <word :params="data.sendData" :is-active="data.tabsActive === '0'" @reset="changeTabs" />
      </el-tab-pane>
      <el-tab-pane label="公开新闻" name="1">
        <publicity :params="data.sendData" :is-active="data.tabsActive === '1'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import word from './components/word.vue'
import publicity from './components/publicity.vue'
import { Search } from '@element-plus/icons-vue'

import formValidate from '@/utils/hooks/formValidate.js'
import { hasPermission } from '../../../utils/ruoyi'

defineOptions({
  name: 'News'
})
const store = useStore()
const device = computed(() => store.state.biapp.device)
const { disabledFeatureDate } = formValidate()
const data = reactive({
  tabsActive: '1',
  params: {
    year: [],
    beginTime: '',
    endTime: '',
    keyword: ''
  }, // 搜索框的值
  sendData: {}, // 点击搜索的时候传递的值
  isShowOne: false
})

watch(
  () => data.params.year,
  val => {
    if (val) {
      data.params.beginTime = val[0]
      data.params.endTime = val[1]
    } else {
      data.params.beginTime = ''
      data.params.endTime = ''
    }
  }
)

function hasOneNews() {
  let auth = hasPermission(['home:onenews:index'])
  let username = store.getters.name
  if (auth || username === 'admin') {
    data.tabsActive = '0'
    data.isShowOne = true
  }
}
hasOneNews()

function toggleSearch() {
  data.sendData = JSON.parse(JSON.stringify(toRaw(data.params)))
}
const changeTabs = () => {
  data.params = {
    year: [],
    beginTime: '',
    endTime: '',
    keyword: ''
  }
  data.sendData = JSON.parse(JSON.stringify(toRaw(data.params)))
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.wrap {
  height: $bi-main-height;
  :deep(.el-tabs) {
    height: 100%;
    background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);
    .el-tab-pane {
      height: 100%;
    }
  }
  :deep(.el-tabs--border-card) {
    & > .el-tabs__content {
      padding: 0 16px;
    }
  }
}
.mobile-wrap {
  height: calc($bi-main-height - 60px);
  min-height: calc($bi-main-height - 60px);
}
.bi-search-bar {
  position: absolute;
  left: 280px;
  top: 26px;
  z-index: 1;
  width: 600px;
}
</style>
