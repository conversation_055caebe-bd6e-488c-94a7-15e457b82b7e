<!--https://frrc.app.yuchai.com/frbi/decision/v10/entry/access/4ab5792f-2172-49a9-bd35-c9a45db0aac8?preview=true-->
<template>
  <div class="wrap">
<!--    <iframe-->
<!--      :src="genFrUrl('https://frrc.app.yuchai.com/frbi/decision/v10/entry/access/4ab5792f-2172-49a9-bd35-c9a45db0aac8?preview=true',getters.name)"-->
<!--      frameborder="0"-->
<!--      allowfullscreen-->
<!--    >-->
<!--    </iframe>-->
    <iframe
      :src="genFrUrl('https://frrc.app.yuchai.com/frbi/decision/v10/entry/access/7c5b4ceb-8ba9-4a9b-8d83-4ab199098e2f?preview=true',getters.name)"
      frameborder="0"
      allowfullscreen
    >
    </iframe>
    <!-- <el-table :data="data.list" class="table-box" style="width: 100%;border-radius: 8px;">
      <el-table-column prop="province" label="省" min-width="60" />
      <el-table-column prop="city" label="市" min-width="60" />
      <el-table-column prop="area" label="区" min-width="60" />
      <el-table-column prop="company" label="公司名称" show-overflow-tooltip />
      <el-table-column prop="quality" label="公司性质" width="80" />
      <el-table-column prop="car" label="公司拥有车辆数" width="130" />
      <el-table-column prop="history" label="公司历史平均置换年限" width="164" />
      <el-table-column prop="over" label="超领车数量" width="110" />
    </el-table> -->
  </div>
</template>

<script setup>
import {genFrUrl} from '../../../utils/common/crypto-yc'
import {getUserProfile} from "../../../api/system/user";
const store = useStore()
const getters = computed(() => store.getters)
// const state = reactive({
//   user: {},
//   isShow: false,
// });
//
// function getUser() {
//   getUserProfile().then(response => {
//     state.user = response.data;
//     state.isShow = true;
//   });
// };
// getUser()
/* const json = {
  province: '河南省',
  city: '安阳市',
  area: '龙安区',
  company: '安阳市兴业汽车运输有限责任公司',
  quality: '挂靠',
  car: '123',
  history: '5.33',
  over: '1'
}
const list = []
for (let i = 0; i < 10; i++) {
  list.push(json)
}
const data = reactive({
  list: list
}) */

</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.wrap {
  iframe {
    background-color: #fff;
    border-radius: 8px;
    height: calc($bi-main-height - $bi-layout-margin - $bi-layout-margin - 5px);
    width: 100%;

    border: none;
  }
}
</style>
