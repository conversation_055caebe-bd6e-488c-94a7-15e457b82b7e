<template>
  <div class="wrap">
    <SearchFormResource :params="data.params" @change="getParams" />
    {{ data.currentDrillLevel }}
    <el-row :gutter="16">
      <el-col :xs="24" :sm="24" :md="15">
        <el-card
          v-loading="loading.sendingMap"
          class="bi-card bi-loading-mask"
          style="background: linear-gradient(180deg, #d6ebfa 0%, rgba(210, 230, 252, 0.5) 100%)"
        >
          <div class="content">
            <div class="bi-segmented">
              <el-segmented
                v-model="data.params.queryType"
                :options="dictSearchType"
                @change="changeQueryType"
                style="width: 100%"
              />
            </div>
            <div class="sales">
              <div class="sales__item">
                <img src="@/assets/images/icon/starMap.png" class="sales__item--icon" />
                <div
                  class="sales__item--value"
                  :style="{
                    color: data.mapTotal.total.toString().includes('-') ? '#0085FF' : '#0B5FC5'
                  }"
                >
                  {{ data.mapTotal.total }}
                </div>
                <div class="sales__item--name">
                  总销量{{ data.mapTotal.dateRange ? `（${data.mapTotal.dateRange}）` : '' }}
                </div>
              </div>
              <div class="sales__item">
                <img
                  src="@/assets/images/icon/chartMap.png"
                  class="sales__item--icon"
                  style="top: -16px; right: -10px"
                />
                <div
                  class="sales__item--value"
                  :style="{
                    color: data.mapTotal.sales_prop.toString().includes('-') ? '#0085FF' : '#0B5FC5'
                  }"
                >
                  {{ data.mapTotal.sales_prop }}
                </div>
                <div class="sales__item--name">同比增长</div>
              </div>
            </div>
            <maps
              ref="refMaps"
              :series-data="data.areaData"
              @select="changeSelectArea"
              :queryType="data.params.queryType"
              style="padding-bottom: 69%"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="9" v-loading="loading.sendingTable" class="bi-loading-mask">
        <tables
          :query-type="data.params.queryType"
          :current-drill-level="data.currentDrillLevel"
          :params="data.params"
          :table-a="data.tableA"
          :table-b="data.tableB"
          @change="changeAreaData"
        ></tables>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import SearchFormResource from './components/SearchFormResource.vue'
import maps from './components/maps.vue'
import tables from './components/tables.vue'

import { domesticMarket, domesticMarketRange } from '@/api/intelligence/homePage.js'
import { dictSearchType } from '@/utils/common/dicts.js'
import { numFormat } from '../../components/echarts/config'
const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '货运新增数')?.value
import decode from '@/utils/common/provincejian/adcodes.json'

// 细分市场
const ENGINE_TYPE_30 = [
  '卡车',
  '牵引车',
  '中重载货',
  '中重专用',
  '中重自卸',
  '轻卡',
  '皮卡',
  '客车',
  '公路',
  '公交',
  '校车'
]
// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  quarter: '', // 季度
  queryType: '0', // 查询类型0发动机，1整车
  segment: '商用车', // 板块
  subMarket1: '卡车', // 细分市场1
  subMarket2: '', //  细分市场2
  manuFacturer: '', // 主机厂
  engineFactory: '', // 发动机厂111
  breed: '', // 品系
  fuelType: '', // 燃料
  province: '', // 省
  city: '', // 市
  dataType: [], // 数据扩展
  dataSource: dataSource, // 数据来源（货运新增）
  weightMidLight: ''
}
const refMaps = ref(null)
const data = reactive({
  params: { ...originParams },
  areaData: [], // 地图数据
  mapTotal: { total: '—', sales_prop: '—' },
  tableA: [], // 第一个列表
  tableB: [], // 第二个列表
  currentDrillLevel: 1 // 当前下钻层级：1-中国地图，2-省级地图
})
const loading = reactive({
  sendingMap: false,
  sendingTable: false
})

/**
 * @description 点击搜索
 * @param params 搜索参数
 */
function getParams(params) {
  params.queryType = data.params.queryType
  data.params = params
  initChartData(params)
}
/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = async param => {
  if (loading.sendingTable || loading.sendingMap) return
  loading.sendingTable = true
  loading.sendingMap = true

  const params = JSON.parse(JSON.stringify(param))
  // 处理 dataType 参数（兼容单选和多选）
  params.dataType = params.dataType
    ? Array.isArray(params.dataType)
      ? params.dataType.join()
      : params.dataType
    : ''
  params.province = params.province
  params.dataRange = params.year ? '1' : '0'
  domesticMarket(params, 'map')
    .then(res => {
      if (res.code == '200') {
        // 地图数据
        let domesticArea = res.data.domesticArea
        domesticArea = domesticArea.map(el => ({
          name: el?.sales_province_short || el.sales_city_short,
          value: el.sales
        }))
        // console.log('地图数据',domesticArea)

        const areaData = []
        for (let i = 0; i < domesticArea.length; i++) {
          const el = domesticArea[i]
          if (!el.name || el.name === 'NULL') {
            continue
          }
          areaData.push(el)
        }
        // console.log('地图数据', params.queryType)
        if (params.province == '' || params.queryType == '0') {
          data.areaData = areaData
        }
        //图表上的总计
        const mapTotal = res.data.maptotal && res.data.maptotal[0] ? res.data.maptotal[0] : {}
        data.mapTotal.total = mapTotal.total ? numFormat(mapTotal.total, 0) : '—'
        data.mapTotal.sales_prop = mapTotal.sales_prop ? numFormat(mapTotal.sales_prop) : '—'
      }
      loading.sendingMap = false
    })
    .catch(e => {
      loading.sendingMap = false
    })

  domesticMarket(params, 'table')
    .then(res => {
      if (res.code == '200') {
        // queryType: '0', // 查询类型0发动机，1整车
        const queryType = data.params.queryType
        // 发动机企业销量top10(0-发动机)
        const engineTop10 = res.data.engineTop10 ?? []
        // 集团企业销量top10(1-整车)
        const groupTop10 = res.data.groupTop10 ?? []
        // data.tableA = queryType === '0' ? engineTop10 : queryType === '1' ? groupTop10 : []
        let tableA = []
        let otherIndex = -1
        if (queryType === '0') {
          tableA = engineTop10
          otherIndex = tableA.findIndex(el => el.engineFactory === '其他')
        } else if (queryType === '1') {
          tableA = groupTop10
          otherIndex = tableA.findIndex(el => el.company === '其他')
        }

        if (otherIndex !== -1) {
          const otherItem = tableA.splice(otherIndex, 1)
          if (otherItem && otherItem[0] && otherItem[0].sales) {
            tableA.push(...otherItem)
          }
        }
        data.tableA = tableA

        // 细分市场销量排名Top 30(0-发动机)
        const engineType30 = res.data.engineType30 ?? []
        const newEngineType30 = []
        ENGINE_TYPE_30.forEach(el => {
          const item = engineType30.find(e => e.market === el)
          if (item) {
            if (item.market === '轻卡') item.market = '轻卡(3.5T以上)'
            newEngineType30.push(item)
          }
        })
        // 地区销量(1-整车)
        const areaSales = res.data.areaSales ?? []
        data.tableB =
          queryType === '0'
            ? newEngineType30
            : queryType === '1' && params.city == ''
              ? areaSales
              : data.tableB

        if (queryType == '1' && params.province && params.city == '') {
          data.areaData = areaSales.map(el => ({
            name: el.area,
            value: el.sales
          }))
        }
      }
      loading.sendingTable = false
    })
    .catch(e => {
      loading.sendingTable = false
    })
  domesticMarketRange(params).then(res => {
    if (res.code === 200) {
      let minDate = res?.data?.minDate ?? ''
      let maxDate = res?.data?.maxDate ?? ''
      let dateRange = ''
      if (minDate && maxDate && minDate.indexOf('-') !== -1 && maxDate.indexOf('-') !== -1) {
        minDate = minDate.split('-')[1]
        maxDate = maxDate.split('-')[1]
      }
      dateRange = `${minDate}-${maxDate}月`
      if (minDate === maxDate) dateRange = `${minDate}月`
      if (!minDate && !maxDate) dateRange = ''
      data.mapTotal.dateRange = dateRange
    } else {
      data.mapTotal.dateRange = ''
    }
  })
}

const changeQueryType = ev => {
  data.params.province = ''
  data.params.city = ''
  data.currentDrillLevel = 1 // 重置为默认层级
  initChartData(data.params)
}
const changeSelectArea = ev => {
  // ev.currentSelect
  if(ev.currentSelect == ''){
    data.currentDrillLevel = 1
  }else if(decode[ev.currentSelect]){
    data.currentDrillLevel = 2
    data.params.province = ev.currentSelect
  }else{
    data.currentDrillLevel = 3
    data.params.city = ev.currentSelect
  }
  // 同步更新当前下钻层级
  if(data.currentDrillLevel == 1){
    data.params.province = '';
    data.params.city = '';
  }
  if(data.currentDrillLevel == 2){
    data.params.province = ev.currentSelect;
    data.params.city = '';
  }
  if(data.currentDrillLevel == 3){
    data.params.city = ev.currentSelect;
  }
  // if (ev.drillLevel == 2) {
  // 层级2：省级地图，设置city参数
  
  // } else {
  // 层级1：中国地图，设置province参数
  
  // }

  console.log('当前下钻层级:', data.currentDrillLevel)
  console.log('选中区域:', ev.currentSelect)
  initChartData(data.params)
}
const changeAreaData = ev => {
  console.log('changeAreaData', ev)
  if (data.params.province === '') {
    refMaps.value.selectArea(ev)
    data.params.province = ev
    initChartData(data.params)
  }
  console.log('changeAreaData', data.params.city !== '' && data.params.province != '', data.params)
  if (ev && data.params.province != '') {
    data.params.city = ev
    initChartData(data.params)
  }
}

// 首次加载请求由头部组件处理好数据后发起
// initChartData(data.params)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.content {
  position: relative;
  width: 100%;
  height: 100%;
}
.bi-segmented {
  position: absolute;
  right: 10px;
  z-index: 100;
  display: flex;
  align-items: center;
  width: 164px;
  margin: 20px 20px 16px 20px;
  border-radius: 8px;
  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.2);
}
.sales {
  position: absolute;
  top: 20px;
  left: 30px;
  z-index: 2;
  display: flex;
  &__item {
    position: relative;
    width: 160px;
    height: 80px;
    padding: 20px;
    margin-right: 20px;
    border-radius: 8px;
    border: 1px solid #92cdfc;
    background: linear-gradient(288deg, #c6e0f9 2%, #fafbfc 97%);
    backdrop-filter: blur(13.6px);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    &--icon {
      position: absolute;
      top: -25px;
      right: -20px;
      width: 80px;
      height: 80px;
    }
    &--name {
      color: #051c2c;
      font-size: 12px;
    }
    &--value {
      color: #0085ff;
      font-size: 30px;
      font-weight: bold;
    }
  }
}
:deep(.el-col) {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}

.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}
</style>
