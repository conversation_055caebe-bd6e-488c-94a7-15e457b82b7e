<template>
  <div class="wrap">
    <el-affix target=".wrap" :offset="navBottom">
      <SearchFormResource :params="data.params" @change="getParams" />
    </el-affix>
    <el-row :gutter="20">
      <template v-for="i in data.seriesArray" :key="i.key">
        <el-col :xs="24" :sm="24" :md="8">
          <bar v-loading="i.loading" class="bi-loading-mask" titleIcon="data4" :title="i.titleA" :series="i.dataA"
            :key="i.key" y-axis-name="燃料结构占比：(%)" yAxisLabelFormate="{value}%" :tooltip="{
              formatter: params =>
                TooltipFormatter(TooltipComponent, params, {
                  mapping: {
                    sales: 'sale'
                  }
                })
            }" :y-axis-max="100" tooltip-units="%" :otherYAxis="yAxisRight" :grid="{}"
            :legend="{ orient: 'vertical', bottom: 4, right: 4, data: i.dataA.map(v => v.name) }" :xAxis="{
              nameTextStyle: { height: '220px' },
              axisLabel: {
                fontSize: 10,
                color: '#44546A',
                rotate: 45,
                interval: 0
              }
            }" height="270px" :totalSortLegend="true" reverse-legend />
        </el-col>
        <el-col :xs="24" :sm="24" :md="16">
          <bar v-loading="i.loading" class="bi-loading-mask" titleIcon="data4" :title="i.titleB" :series="i.dataB"
            :key="i.key" y-axis-name="燃料结构占比：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" :tooltip="{
              formatter: params =>
                TooltipFormatter(TooltipComponent, params, {
                  mapping: {
                    sales: 'sale'
                  }
                })
            }" :otherYAxis="yAxisRight" :grid="{}"
            :legend="{ orient: 'vertical', bottom: 4, right: 4, data: i.dataB.map(v => v.name) }"
            :xAxis="{ nameTextStyle: { height: '220px' } }" height="270px" :totalSortLegend="true" reverse-legend />
        </el-col>
      </template>
    </el-row>
  </div>
</template>

<script setup lang="jsx">
import SearchFormResource from './components/SearchFormResource.vue'
import bar from '@/views/components/echarts/bar.vue'
import { TooltipFormatter } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'
import provinceFull2Jian from '@/utils/common/map/provinceFull2Jian.json'
import calChartsData from '@/utils/hooks/calChartsData.js'
import { newEnergyPermeabilityList } from '@/api/intelligence/newEnergyMarket.js'
import { numberFormat } from '@/utils/format.js'
import { useRect } from "@/utils/hooks/useRect.js"
const { navBottom } = useRect()

const vueStore = useStore()
const dataSource = vueStore.state.dicts.dictsDataSource.find(v => v.label === '上险数')?.value
const defaultParams = {
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  year: (new Date().getFullYear() - 1).toString(), // 年份
  segment: '商用车', // 板块
  dataSource: dataSource, // 数据来源
  province: '',
  manuFacturer: '', // 主机厂
  weightMidLight: '', // 重中轻
  fuelType: '',
  subMarket1: '', // 细分市场1
  breed: '',
  quarter: '',
  month: '',
  dataType: [],
  subMarketAndBreed: '' // subMarket和breed的选中值（前端展示用）
}
const pointerTypeName = ['月度', '季度', '月累']
const data = reactive({
  params: defaultParams,
  seriesArray: [{ dataA: [], dataB: [], titleA: '', titleB: '', loading: false }]
})
const { yAxisRight, referData, setOneArraySeriesData, sortByArray, findOther2Last } =
  calChartsData()
yAxisRight.value[0].name = '渗透率：(%)'
/**
 * @description 获取页面echarts基础数据，组装成符合echarts数据
 * @param params 搜索参数
 */
async function getChartData(param, index) {
  const params = JSON.parse(JSON.stringify(param))
  // 处理 dataType 参数（兼容单选和多选）
  params.dataType = Array.isArray(params.dataType) ? params.dataType.join() : (params.dataType || '')
  params.province = provinceFull2Jian[params.province]
  if (!params.province) params.province = ''
  if (data.seriesArray[index].loading) return
  data.seriesArray[index].loading = true
  const {
    code,
    data: { subPermeabilityList, subSliceListMap = {}, yearPermeabilityList, yearSliceListMap = {} }
  } = await newEnergyPermeabilityList(params).catch(e => e)
  if (code !== 200) return (data.seriesArray[index].loading = false)
  // const {
  //   data: { subPermeabilityList, subSliceListMap, yearPermeabilityList, yearSliceListMap }
  // } = await newEnergyPermeabilityList(param).catch(e => e)
  // yearSliceListMap 年度占比(左边年度柱状图)
  // subSliceListMap 月度占比(右边月度柱状图)
  // subPermeabilityList 月度渗透率(右边折线图)
  // yearPermeabilityList 年度渗透率(左边折线图)
  // console.log('subPermeabilityList', subPermeabilityList)
  // console.log('subSliceListMap', subSliceListMap)
  // console.log('yearPermeabilityList', yearPermeabilityList)
  // console.log('yearPermeabilityList', yearPermeabilityList)
  // 防止循环到缓存的key
  if (yearSliceListMap && yearSliceListMap['@type']) delete yearSliceListMap['@type']
  const seriesA = []
  const flattenYearSliceListMap = []
  for (let i in yearSliceListMap) {
    yearSliceListMap[i].forEach(el => {
      flattenYearSliceListMap.push(el)
    })
  }
  flattenYearSliceListMap.forEach(el => {
    el.xAxisName = el.year + '年'
    if (el.slice) el.slice = Number(el.slice.replace(/%/g, ''))
  })
  let seriesBarA = setOneArraySeriesData({
    list: flattenYearSliceListMap,
    xAxisKey: 'xAxisName',
    yAxisKey: 'slice',
    legendKey: 'fuleType'
  })
  seriesBarA.forEach(el => {
    el.type = 'bar'
    el.stack = 'flattenYearSliceListMap'
    el.yAxisIndex = 0
  })
  seriesBarA = findOther2Last(seriesBarA)
  yearPermeabilityList.forEach(el => {
    el.year = el.year + ''
    el.legendName = '渗透率'
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
  })

  const seriesLineA = setOneArraySeriesData({
    list: yearPermeabilityList,
    xAxisKey: 'year',
    yAxisKey: 'permeability',
    legendKey: 'legendName'
  })
  seriesLineA.forEach(el => {
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
    el.itemStyle = {
      color: '#00A9F4'// 设置折线颜色为红色
    }
  })
  seriesA.push(...seriesBarA, ...seriesLineA)
  let titleA = ''
  if (param.subMarket1 === '' && param.breed === '') {
    titleA += '商用车'
  } else if (param.subMarket1 === '' && param.breed !== '') {
    titleA += param.breed
  } else if (param.subMarket1 !== '' && param.breed === '') {
    titleA += param.subMarket1
  }
  titleA += '近5年新能源渗透率趋势（国内）'
  data.seriesArray[index].titleA = titleA
  data.seriesArray[index].dataA = seriesA

  const seriesB = []
  const flattenSubSliceListMap = []
  // 防止循环到缓存的key
  if (subSliceListMap && subSliceListMap['@type']) delete subSliceListMap['@type']
  for (let i in subSliceListMap) {
    subSliceListMap[i].forEach(el => {
      flattenSubSliceListMap.push(el)
    })
  }
  flattenSubSliceListMap.forEach(el => {
    // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
    if (param.pointerType === '1') {
      el.xAxisName = referData.quarterRefer[el.month]
    } else {
      el.month = Number(el.month)
      el.xAxisName = el.month + '月'
    }
    if (el.slice) el.slice = Number(el.slice.replace(/%/g, ''))
  })
  let seriesBarB = setOneArraySeriesData({
    list: flattenSubSliceListMap,
    xAxisKey: 'xAxisName',
    yAxisKey: 'slice',
    legendKey: 'fuleType'
  })
  seriesBarB.forEach(el => {
    el.type = 'bar'
    el.stack = 'flattenSubSliceListMap'
    el.yAxisIndex = 0
  })
  seriesBarB = findOther2Last(seriesBarB)
  subPermeabilityList.forEach(el => {
    el.legendName = '渗透率'
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
    el.month = Number(el.month)
    el.xAxisName = el.month + '月'
    // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
    if (param.pointerType === '1') {
      el.xAxisName = referData.quarterRefer[el.month]
    }
    if (el.permeability) el.permeability = Number(el.permeability.replace(/%/g, ''))
  })

  let seriesLineB = setOneArraySeriesData({
    list: subPermeabilityList,
    xAxisKey: 'xAxisName',
    yAxisKey: 'permeability',
    legendKey: 'legendName'
  })
  seriesLineB.forEach(el => {
    el.type = 'line'
    el.yAxisIndex = 1
    el.stack = null
    el.itemStyle = {
      color: '#00A9F4'// 设置折线颜色为红色
    }
  })
  const monthSortArray = referData.monthSort
  const quarterSortArray = referData.quarterSort
  // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  seriesBarB = sortByArray(
    seriesBarB,
    param.pointerType === '1' ? quarterSortArray : monthSortArray,
    'xAxisName'
  )
  seriesLineB = sortByArray(
    seriesLineB,
    param.pointerType === '1' ? quarterSortArray : monthSortArray,
    'xAxisName'
  )
  seriesB.push(...seriesBarB, ...seriesLineB)

  let titleB = ''
  if (param.subMarket1 === '' && param.breed === '') {
    titleB += '商用车'
  } else if (param.subMarket1 === '' && param.breed !== '') {
    titleB += param.breed
  } else if (param.subMarket1 !== '' && param.breed === '') {
    titleB += param.subMarket1
  }
  titleB += pointerTypeName[param.pointerType]
  titleB += '新能源渗透率趋势（国内）'
  data.seriesArray[index].titleB = titleB
  data.seriesArray[index].dataB = seriesB
  data.seriesArray[index].key = new Date().getTime() + index
  data.seriesArray[index].loading = false
}
/**
 * @description 搜索返回值
 * @param params 搜索值
 */
const getParams = params => {
  data.params = params
  const subMarket1 = params.subMarketAndBreed
  let arr = []
  const ka = [
    { subMarket1: '', breed: '牵引车' },
    { subMarket1: '', breed: '载货车' },
    { subMarket1: '', breed: '自卸车' },
    { subMarket1: '', breed: '专用车' },
    { subMarket1: '', breed: '皮卡' }
  ]
  const ke = [{ subMarket1: '客车', breed: '' }]
  if (subMarket1 === '') {
    arr.push({ subMarket1: '', breed: '' }, ...ka, ...ke)
  } else if (subMarket1 === '卡车') {
    arr.push({ subMarket1: '卡车', breed: '' }, ...ka)
  } else if (subMarket1 === '客车') {
    arr.push(...ke)
  } else {
    arr.push({ subMarket1: '', breed: subMarket1 })
  }
  data.seriesArray = new Array(arr.length).fill({})
  data.seriesArray = arr.map((el, index) => {
    const param = JSON.parse(JSON.stringify(params))
    param.subMarket1 = el.subMarket1
    param.breed = el.breed
    return {
      dataA: [],
      dataB: [],
      titleA: '',
      titleB: '',
      loading: false,
      param,
      key: new Date().getTime() + index
    }
  })
  const allMethod = data.seriesArray.map((el, index) => getChartData(el.param, index))
  Promise.all(allMethod)
}

// Tooltip组件渲染
const TooltipComponent = propos => {
  return (
    <Tpis {...propos}>
      {{
        'hander-right': ({ params }) => {
          const total = params?.reduce((sum, x) => sum + (Number(x.data.sale) || 0), 0)

          return <>总销量：{numberFormat(parseInt(total), 0) || 0}辆</>
        },
        item: ({ item }) => {
          return (
            <>
              <span>{numberFormat(item.value || 0) || 0}% </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}

:deep(.el-col) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
