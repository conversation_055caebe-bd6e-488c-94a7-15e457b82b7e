<template>
  <div class="wrap">
    <el-card>
      <template #header>
        <head-title title="宏观经济指标" />
      </template>
      <el-row :gutter="16" class="bi-row">
        <template v-for="(item, index) in data.card" :key="item.macroType">
          <el-col :xs="12" :sm="12" :md="3">
            <card
              v-loading="loading.card"
              class="bi-loading-mask"
              :title="item.macroType"
              :number="item.number"
              :color="item.color"
              mark="%"
              :index="index"
              @click="toggleItem(item)"
            />
          </el-col>
        </template>
      </el-row>
      <el-row :gutter="16">
        <el-col :xs="24" :sm="24" :md="8">
          <lines
            v-loading="loading.dataA"
            titleIcon="data1"
            :series="data.fixedAssets"
            :title="data.fixedAssetsTitle"
            y-axis-name="单位：(%)"
            tooltip-units="%"
            y-axis-label-formate="{value}%"
            :color="lineColor.a"
            class="vibe-card bi-loading-mask"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="8">
          <lines
            v-loading="loading.dataB"
            titleIcon="data2"
            :series="data.consumerMarket"
            :title="data.consumerMarketTitle"
            y-axis-name="单位：(%)"
            tooltip-units="%"
            y-axis-label-formate="{value}%"
            :color="lineColor.b"
            class="vibe-card bi-loading-mask"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="8">
          <lines
            v-loading="loading.dataC"
            titleIcon="data3"
            :series="data.impExpMarket"
            :title="data.impExpMarketTitle"
            y-axis-name="单位：(%)"
            tooltip-units="%"
            y-axis-label-formate="{value}%"
            :color="lineColor.c"
            class="vibe-card bi-loading-mask"
          />
        </el-col>
      </el-row>
    </el-card>
    <el-card>
      <template #header>
        <head-title title="物流类指标" />
      </template>
      <el-row :gutter="16">
        <el-col :xs="24" :sm="24" :md="8">
          <div v-loading="loading.dataD" class="bar-box bi-loading-mask">
            <el-tooltip
              content="中国物流业景气指数：反映物流业经济发展变化情况，以50%作为经济强弱的分界点，高于50%表示物流业经济扩张，低于50%则表示物流业经济收缩。"
              popper-class="bar-box-tooltip"
              placement="top"
            >
              <el-icon :size="18" color="rgba(13, 41, 102, 0.9)" class="bar-box__warn"
                ><InfoFilled
              /></el-icon>
            </el-tooltip>
            <lines
              titleIcon="data4"
              :series="data.logistics"
              :title="data.logisticsTitle"
              y-axis-name="单位：(%)"
              tooltip-units="%"
              :grid="{ left: 56, bottom: 60, right: 56, top: 46 }"
              y-axis-label-formate="{value}%"
              :color="lineColor.d"
              class="vibe-card"
            />
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8">
          <lines
            v-loading="loading.dataE"
            titleIcon="data5"
            :series="data.roadFreight"
            :title="data.roadFreightTitle"
            :grid="{ left: 56, bottom: 60, right: 56, top: 46 }"
            y-axis-name="单位：(亿吨)"
            tooltip-units="亿吨"
            :color="lineColor.e"
            class="vibe-card bi-loading-mask"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="8">
          <div class="bar-box bi-loading-mask" v-loading="loading.dataF">
            <el-tooltip
              content="LNG与柴油价格比走势：LNG液化天然气价格与0#柴油价格比例行业认为70%是临界点，高于70%气体整车使用成本优势降低，低于70%气体整车使用成本优势明显。"
              popper-class="bar-box-tooltip"
              placement="top"
            >
              <el-icon :size="18" color="rgba(13, 41, 102, 0.9)" class="bar-box__warn"
                ><InfoFilled
              /></el-icon>
            </el-tooltip>
            <bar
              titleIcon="data6"
              :series="data.fuelPrice"
              :title="data.fuelPriceTitle"
              y-axis-name="单位：(元/吨)"
              :otherYAxis="[{...otherYAxis[0],name:'汽/柴比：(%)'}]"
              :grid="{ left: 56, bottom: 72, right: 56, top: 46 }"
              :yAxis="{ max: data.dataLimit6.max, min: data.dataLimit6.min }"
              xAxisInterval="auto"
              height="63%"
              :color="lineColor.f"
              class="vibe-card bi-loading-mask"
              :legendWrap="false"
              :legend="{bottom: '10px'}"
            />
          </div>
        </el-col>
      </el-row>
    </el-card>
    <cardList ref="refCardList" />
  </div>
</template>

<script setup>
import { InfoFilled } from '@element-plus/icons-vue'
import bar from '@/views/components/echarts/bar.vue'
import lines from '@/views/components/echarts/lines.vue'
import HeadTitle from '@/views/components/HeadTitle.vue'
import card from './components/card.vue'
import cardList from './components/cardList.vue'

import { homePageMacroDataList } from '@/api/intelligence/macroData.js'
const otherYAxis = ref([
  {
    name: '单位：(%)',
    unit: '%',
    type: 'value',
    position: 'right',
    nameTextStyle: {
      color: '#44546A',
      fontSize: 12,
      align: 'center',
      padding: [0, 0, 0, 25]
    },
    axisLabel: {
      show: true,
      formatter: '{value}%',
      fontSize: 12,
      color: '#44546A'
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: '#9BA4AB'
      }
    },
    axisTick: {
      show: false
    },
    splitLine: {
      show: false
    },
    axisPointer: {
      label: {
        show: true,
        precision: 2
      }
    }
  }
])

const refCardList = ref(null)
const data = reactive({
  card: [
    { macroType: '', number: '0', color: '#2970da' },
    { macroType: '', number: '0', color: '#2970da' },
    { macroType: '', number: '0', color: '#2970da' },
    { macroType: '', number: '0', color: '#2970da' },
    { macroType: '', number: '0', color: '#2970da' },
    { macroType: '', number: '0', color: '#2970da' },
    { macroType: '', number: '0', color: '#2970da' }
  ],
  dataLimit6: { max: 6000, min: 0 },
  dataLimitRight6: { max: 100, min: 0 },
  fixedAssetsTitle: '', // 标题-固定资产分行业投资累计增速
  fixedAssets: [], // 固定资产分行业投资累计增速
  consumerMarketTitle: '', // 标题-消费市场累计增速
  consumerMarket: [], // 消费市场累计增速
  impExpMarketTitle: '', // 标题-进出口市场累计增速
  impExpMarket: [], // 进出口市场累计增速
  logisticsTitle: '', // 标题-物流类指数月度走势
  logistics: [], // 物流类指数月度走势
  roadFreightTitle: '', // 标题-公路货运量月度走势(亿吨)
  roadFreight: [], // 公路货运量月度走势(亿吨)
  fuelPriceTitle: '', // 标题-LNG与柴油价格比走势
  fuelPrice: [] // LNG与柴油价格比走势
})
const loading = reactive({
  card: false,
  dataA: false,
  dataB: false,
  dataC: false,
  dataD: false,
  dataE: false,
  dataF: false
})
const lineColor = reactive({
  a: ['#3BDBD6', '#9BA4AB', '#00A9F4', '#3A76FF'],
  b: ['#9BA4AB', '#00A9F4'],
  c: ['#9BA4AB', '#00A9F4'],
  d: ['#00A9F4', '#3A76FF'],
  e: ['#00A9F4'],
  f: ['#00A9F4', '#3A76FF', '#ff0000']
})
// 获取数据
const getChartsData = async () => {
  if (
    loading.card ||
    loading.dataA ||
    loading.dataB ||
    loading.dataC ||
    loading.dataD ||
    loading.dataE ||
    loading.dataF
  )
    return
  loading.card = true
  loading.dataA = true
  loading.dataB = true
  loading.dataC = true
  loading.dataD = true
  loading.dataE = true
  loading.dataF = true
  const res = await homePageMacroDataList().catch(e => e)
  if (res.code !== 200) return
  // 处理卡片数据有减号加颜色
  const card = res.data.card
  card.forEach((el, index) => {
    el.number = el.number !== null ? el.number : '0'
    el.color = '#115E93'
    //负数不需要红色字体
    if (el.number && el.number.toString().includes('-')) {
      el.color = '#f00'
    }

    if (index === 6) {
      // 小于50红色，大于50黑色
      if (el.number < 50) el.color = '#f00'
    } else {
      // 前6个小于0字体红色
      if (el.number < 0) el.color = '#f00'
    }
    if (el.number > 0) el.number = '+' + el.number

    data.card[index] = el
  })
  await nextTick()
  loading.card = false
  // 表1数据处理(固定资产分行业投资累计增速)
  const fixedAssets = res.data.discount.fixedAssets
  if (fixedAssets && fixedAssets.length > 0) {
    data.fixedAssetsTitle = fixedAssets[0].name
    const fixedAssetsData = formateChartData(fixedAssets, {
      name: 'macroType',
      dataName: 'yearMonths',
      dataValue: 'number'
    })
    data.fixedAssets = changeValueZero2empty(fixedAssetsData)
  }
  await nextTick()
  loading.dataA = false
  // 表2数据处理（消费市场累计增速）
  const consumerMarket = res.data.discount.consumerMarket
  if (consumerMarket && consumerMarket.length > 0) {
    data.consumerMarketTitle = consumerMarket[0].name
    const consumerMarketData = formateChartData(consumerMarket, {
      name: 'macroType',
      dataName: 'yearMonths',
      dataValue: 'number'
    })
    data.consumerMarket = changeValueZero2empty(consumerMarketData)
  }
  await nextTick()
  loading.dataB = false
  // 表3数据处理（进出口市场累计增速）
  const impExpMarket = res.data.discount.impExpMarket
  if (impExpMarket && impExpMarket.length > 0) {
    data.impExpMarketTitle = impExpMarket[0].name
    const impExpMarketData = formateChartData(impExpMarket, {
      name: 'macroType',
      dataName: 'yearMonths',
      dataValue: 'number'
    })

    data.impExpMarket = changeValueZero2empty(impExpMarketData)
  }
  await nextTick()
  loading.dataC = false
  // 表4数据处理（物流类指数月度走势）
  const logistics = res.data.discount.logistics
  if (logistics && logistics.length > 0) {
    data.logisticsTitle = logistics[0].name
    const logisticsData = formateChartData(logistics, {
      name: 'macroType',
      dataName: 'yearMonths',
      dataValue: 'number'
    })
    data.logistics = changeValueZero2empty(logisticsData)
  }
  await nextTick()
  loading.dataD = false
  // 表5数据处理（公路货运量月度走势(亿吨)）
  const roadFreight = res.data.discount.roadFreight

  if (roadFreight && roadFreight.length > 0) {
    data.roadFreightTitle = roadFreight[0].name
    const roadFreightData = formateChartData(roadFreight, {
      name: 'macroType',
      dataName: 'yearMonths',
      dataValue: 'number'
    })
    data.roadFreight = changeValueZero2empty(roadFreightData)
  }
  await nextTick()
  loading.dataE = false
  // 表6数据处理（LNG与柴油价格比走势）
  let fuelPrice = res.data.discount.fuelPrice
  const leftData = []
  const rightData = []
  if (fuelPrice && fuelPrice.length > 0) {
    data.fuelPriceTitle = fuelPrice[0].name
    fuelPrice = formateChartData(fuelPrice, {
      name: 'macroType',
      dataName: 'yearMonths',
      dataValue: 'number'
    })
    for (let i in fuelPrice) {
      if ('气/柴比%' === fuelPrice[i].name) {
        fuelPrice[i].yAxisIndex = 1
        fuelPrice[i].stack = null
        rightData.push(fuelPrice[i])
      } else {
        fuelPrice[i].yAxisIndex = 0
        fuelPrice[i].stack = null
        leftData.push(fuelPrice[i])
      }
    }
    let max = getLimitData(leftData, 'max')
    let min = getLimitData(leftData, 'min')

    max = Math.ceil(max / 1000) * 1000
    min = Math.floor(min / 1000) * 1000
    data.dataLimit6.max = max
    data.dataLimit6.min = min

    let maxRight = getLimitData(rightData, 'max')
    let minRight = getLimitData(rightData, 'min')
    maxRight = Math.ceil(maxRight / 10) * 10
    minRight = Math.floor(minRight / 10) * 10
    data.dataLimitRight6.max = maxRight
    data.dataLimitRight6.min = minRight
    otherYAxis.value[0].max = maxRight
    otherYAxis.value[0].min = minRight - 10 < 0 ? 0 : minRight - 10
    const fuelPriceData = [...leftData, ...rightData]
    data.fuelPrice = changeValueZero2empty(fuelPriceData)
  }
  await nextTick()
  loading.dataF = false
}

const MONTH_NAME = ['', '上旬', '中旬', '下旬']
/**
 * @param "25'02"
 * @return "{index:2,name:"2月"}"
 */
const formatMonth = str => {
  const res = {}
  let name = str.split("'")
  if (name && name.length > 0) {
    res.index = name[1] - 0
    res.name = name[1] - 0 + '月' + (name[2] ? MONTH_NAME[name[2]] : '')
    return res
  }
  return res
}

function changeValueZero2empty(data) {
  if (data && data.length > 0) {
    let list = JSON.parse(JSON.stringify(data))
    list.forEach(element => {
      if (element.data && element.data.length > 0) {
        element.data.forEach(el => {
          if (el.value === 0 || el.value === '0' || !el.value) {
            el.value = ''
          }
        })
      }
    })
    return list
  } else {
    return []
  }
}
const formateChartData = (arr, key) => {
  const response = []
  //收集现有数据的所有：dataName
  const dataNameList = arr.reduce((list, v) => {
    const temps = v.data.map(v1 => formatMonth(v1[key.dataName]))
    // const temps = v.data.map(v1=>formatMonth(v1[key.dataName]))
    // list.
    list.push(...temps)
    return list
  }, [])
  // 使用 Map 来去重，基于 index
  const uniqueArr = Array.from(new Map(dataNameList.map(item => [item.name, item])).values())

  // 按照 index 排序
  const sortedArr = uniqueArr.sort((a, b) => a.index - b.index)
  arr.forEach(el => {
    const json = {
      name: el[key.name],
      data: [],
      data: sortedArr.map(v => {
        v.value = ''
        return { ...v }
      }),
      type: 'line'
    }
    el.data.forEach(v => {
      let name = v[key.dataName].split("'")
      if (name && name.length > 0) name = name[1] - 0 + '月' + (name[2] ? MONTH_NAME[name[2]] : '')
      json.data.forEach(v1 => {
        if (v1.name == name) {
          v1.value = v[key.dataValue]
        }
      })
    })
    response.push(json)
  })
  return response
}
const toggleItem = ev => {
  refCardList.value.toggleDialog(toRaw(ev))
}
const getLimitData = (data, key = 'max') => {
  if (data && data.length > 0) {
    const valueLists = []
    data.forEach(element => {
      if (element.data && element.data.length > 0) {
        valueLists.push(...element.data.map(v => v.value))
      }
    })
    if (key === 'max') {
      return Math.max.apply(null, valueLists)
    } else if (key === 'min') {
      return Math.min.apply(null, valueLists)
    } else {
      return 0
    }
  } else {
    return 0
  }
}
getChartsData()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
@media only screen and (min-width: 992px) {
  .bi-row {
    :deep(.el-col-md-3) {
      max-width: 14.28571429%;
      flex: 0 0 14.28571429%;
    }
  }
}
// .bi-row {
//   :deep(.el-col-3) {
//     max-width: 14.28571429%;
//     flex: 0 0 14.28571429%;
//   }
//   :deep(.el-col-7) {
//     max-width: 14.28571429%;
//     flex: 0 0 14.28571429%;
//   }
//   :deep(.el-col-9) {
//     max-width: 11.111111111%;
//     flex: 0 0 11.111111111%;
//   }
// }

:deep(.el-card) {
  margin-bottom: $bi-layout-margin;
  background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);
  &:last-child {
    margin-bottom: 0;
  }
}
:deep(.el-card__body) {
  padding: $bi-layout-margin;
}
.vibe-card {
  background: linear-gradient(287deg, rgba(205, 228, 250, 0.8) 2%, #fafbfc 97%);
}

:deep(.el-col) {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
