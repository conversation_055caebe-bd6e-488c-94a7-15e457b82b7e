<template>
  <div class="wrap">
    <el-affix target=".wrap" :offset="navBottom" z-index="999">
      <SearchFormResource :params="data.params" @change="getParams" />
    </el-affix>
    <el-row :gutter="16">
      <el-col :xs="24" :sm="24" :md="12">
        <bar titleIcon="data1" v-loading="loading.A" class="bi-loading-mask" :title="data.titleA" :series="data.dataA"
          :y-axis-name="`单位：(${data.dataUnitA})`" :tooltipUnits="data.dataUnitA" :precision="data.dataPrecisionA"
          show-total title-rotate :tooltip="{ position: positionLeft }" :grid="{left: 50, bottom: 50, right: 100, top: 46}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" addTooltipTotalPercent height="40%" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <bar titleIcon="data2" :title="data.titleB" v-loading="loading.B" class="bi-loading-mask" :series="data.dataB"
          y-axis-name="单位：(%)" y-axis-label-formate="{value}%" tooltipUnits="%" :yAxis="{ max: 100, min: 0 }" :grid="{}"
          :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent1, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                singleColumn: false,
                sortField: 'value',
              })
          }" :legend="{ orient: 'vertical', bottom: 4, right: 4 }" height="40%" totalSortLegend />
      </el-col>
    </el-row>
    <el-row :gutter="16">
      <el-col :xs="24" :sm="24" :md="12">
        <bar titleIcon="data3" :title="data.titleC" v-loading="loading.C" class="bi-loading-mask" :series="data.dataC"
          y-axis-name="单位：(%)" y-axis-label-formate="{value}%" :yAxis="{ max: 100, min: 0 }" :precision="1"
          :tooltip="{ position: positionLeft }" tooltipUnits="%" title-rotate :grid="{left: 50, bottom: 50, right: 100, top: 46}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" height="40%" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <bar titleIcon="data4" :title="data.titleD" v-loading="loading.D" class="bi-loading-mask" :series="data.dataD"
          y-axis-name="单位：(%)" y-axis-label-formate="{value}%" :yAxis="{ max: 100, min: 0 }" :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent1, params, {
                mapping: {
                  sales: 'value',
                  proportion: 'proportion',
                  yoy: 'slice'
                },
                shouldSort: true,
                singleColumn: false,
                sortField: 'proportion'
              })
          }" :grid="{}" :legend="{ orient: 'vertical', bottom: 4, right: 4 }" height="40%" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="jsx">
import bar from '@/views/components/echarts/bar.vue'
import SearchFormResource from './components/SearchFormResource.vue'
import { dictsPointerType, dictsResource } from '@/utils/common/dicts.js'
import { homePageOem } from '@/api/intelligence/homePage.js'
import { positionLeft } from '@/utils/echarts.js'
import calChartsData from '@/utils/hooks/calChartsData.js'
import { TooltipFormatter } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'
import { seriesSortOtherFirst } from '../../../utils/common/method'
import { useRect } from "@/utils/hooks/useRect.js"
const { navBottom } = useRect()
// const { proxy } = getCurrentInstance()
const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '上险数')?.value
// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  quarter: '', // 季度
  segment: '商用车', // 板块
  subMarket1: '', // 细分市场1
  subMarket2: '', // 细分市场2
  dataSource: dataSource, // 数据来源(默认货运新增)只可选1,2,6（注：1上险数，2装机数，3北斗数，4中内协，5海关数，6货运新增，7船电行业数,8渠道数）
  engineFactory: '', // 发动机厂
  manuFacturer: '', // 主机厂
  fuelType: '', // 燃料
  breed: '', // 品系
  dataType: [], // 数据分类(汽油、微客、微改、微卡)
  weightMidLight: ''
}

const data = reactive({
  titleA: '',
  titleB: '',
  titleC: '',
  titleD: '',
  dataA: [],
  dataB: [],
  dataC: [],
  dataD: [],
  dataUnitA: '台',
  dataPrecisionA: 1,
  params: originParams
})

const loading = reactive({
  A: false,
  B: false,
  C: false,
  D: false
})
const { referData, setOneArraySeriesData, sortByArray } = calChartsData()
const monthSortArray = referData.monthSort
const quarterSortArray = referData.quarterSort
quarterSortArray.push('汇总')
monthSortArray.push('汇总')
/**
 * @description 获取页面echarts基础数据，组装成符合echarts数据
 * @param params 搜索参数
 */
const getChartData = param => {
  // if (loading.A || loading.B || loading.C || loading.D) {
  //   proxy.$modal.msgError('数据正在处理，请勿重复提交')
  //   return
  // }
  const params = JSON.parse(JSON.stringify(param))
  // 处理 dataType 参数（兼容单选和多选）
  params.dataType = Array.isArray(params.dataType) ? params.dataType.join() : (params.dataType || '')
  data.dataA = []
  data.dataB = []
  data.dataC = []
  data.dataD = []
  loading.A = true
  loading.B = true
  loading.C = true
  loading.D = true
  // 处理表1，表3数据
  homePageOem(params, 'powerPatPropList')
    .then(res => {
      if (res.code == 200) {
        const powerPatPropList = res.data.powerPatPropList // 接口返回的初始数据
        let originData = []
        if (powerPatPropList && powerPatPropList.length > 0) {
          // 计算处理每个分类的“其他”项数据
          const powerPatPropListOther = []
          powerPatPropList.forEach(element => {
            const newList = []
            const otherInfo = JSON.parse(JSON.stringify(element[0]))
            otherInfo.engineFactory = '其他'
            let leftSale = element[0].manuSales
            let leftPercent = 100
            element.forEach(el => {
              if (el.engineFactory && el.engineFactory !== '其他') {
                newList.push(el)
                leftSale = leftSale - el.engineSales
                leftPercent = leftPercent - el.percentage
              }
            })
            otherInfo.engineSales = leftSale
            otherInfo.percentage = leftPercent
            newList.push(otherInfo)
            powerPatPropListOther.push(newList)
          })
          // 多维数组拍成一维数组
          powerPatPropListOther.forEach(element => {
            element.forEach(el => {
              originData.push(el)
            })
          })
        }
        // 选择主机厂之后将其他隐藏掉
        if (params.manuFacturer !== '') {
          originData = originData.filter(el => el.manuFacturer !== '其他')
        }
        let seriesA = setOneArraySeriesData({
          list: originData,
          xAxisKey: 'manuFacturer',
          yAxisKey: 'engineSales',
          legendKey: 'engineFactory'
        })
        let seriesC = setOneArraySeriesData({
          list: originData,
          xAxisKey: 'manuFacturer',
          yAxisKey: 'percentage',
          legendKey: 'engineFactory'
        })
        if (originData.some(e => e.engineSales >= 10000)) {
          data.dataUnitA = '万台'
          data.dataPrecisionA = 1
          seriesA.forEach(element => {
            element.data.forEach(el => {
              el.value = parseFloat(el.value)
              el.value = isNaN(el.value) ? '' : el.value / 10000
            })
          })
        } else {
          data.dataUnitA = '台'
          data.dataPrecisionA = 0
        }

        seriesC.forEach(element => {
          element.data.forEach(el => {
            el.value = parseFloat(el.value)
            el.value = isNaN(el.value) ? '' : el.value
          })
        })
        data.dataA = seriesSortOtherFirst(seriesA)
        data.dataC = seriesSortOtherFirst(seriesC)
      }
      loading.A = false
      loading.C = false
    })
    .catch(() => {
      loading.A = false
      loading.C = false
    })

  // 处理表2数据
  homePageOem(params, 'patternList')
    .then(res => {
      if (res.code == 200) {
        const originData = []
        let patternRsList = res.data.patternRsList // 表2月份数据
        let patternYearList = res.data.patternYearList // 表2汇总数据
        if (patternYearList && patternYearList.length > 0) {
          patternYearList.forEach(el => {
            el.month = '汇总'
          })
        }
        // 多维数组拍成一维数组
        patternRsList.forEach(element => {
          element.forEach(el => {
            originData.push(el)
          })
        })
        originData.forEach(el => {
          // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
          if (params.pointerType === '1') {
            el.month = referData.quarterRefer[el.month]
          } else {
            el.month = el.month + '月'
          }
        })
        originData.push(...patternYearList)
        originData.reverse()
        let seriesB = setOneArraySeriesData({
          list: originData,
          xAxisKey: 'month',
          yAxisKey: 'proportion',
          legendKey: 'manuFacturer',
          legendSortKey: 'top'
        })
        seriesB = sortByArray(
          seriesB,
          params.pointerType === '1' ? quarterSortArray : monthSortArray,
          'month'
        )
        data.dataB = seriesB
      }
      loading.B = false
    })
    .catch(e => {
      loading.B = false
    })

  // 处理表4近五年数据
  homePageOem(params, 'fiveYearRsList')
    .then(res => {
      if (res.code == 200) {
        const originData = []
        // 获取当前日期选中的近五年
        const year = params.year
        const showYears = []
        for (let i = 0; i < 5; i++) {
          showYears.push(`${year - i}年`)
        }
        showYears.reverse()

        let fiveYearRsList = res.data.fiveYearRsList
        // 以最新一年排序倒序数组
        fiveYearRsList.reverse()
        // 多维数组拍成一维数组
        fiveYearRsList.forEach(element => {
          element.forEach(el => {
            originData.push(el)
          })
        })
        originData.forEach(el => {
          el.year = el.year + '年'
        })
        let seriesD = setOneArraySeriesData({
          list: originData,
          xAxisKey: 'year',
          yAxisKey: 'proportion',
          legendKey: 'manuFacturer',
          legendSortKey: 'top'
        })
        seriesD = sortByArray(seriesD, showYears, 'year')
        data.dataD = seriesD
      }
      loading.D = false
    })
    .catch(() => {
      loading.D = false
    })
}

function getParams(params) {
  getChartData(params)
  const year = !params.year ? new Date().getFullYear().toString() : params.year
  if (!params.pointerType) params.pointerType = '0'

  const pointerType = dictsPointerType.find(ev => ev.value === params.pointerType)
  let dataSource = null
  dictsResource && (dataSource = dictsResource.find(ev => ev.value === params.dataSource))

  let segment = null
  dataSource && (segment = dataSource.children.find(ev => ev.value === params.segment))

  let subMarket1 = null
  segment && (subMarket1 = segment.children.find(ev => ev.value === params.subMarket1))
  let subMarket2 = null
  subMarket1 && (subMarket2 = subMarket1.children.find(ev => ev.value === params.subMarket2))

  const name = subMarket2?.label ?? subMarket1?.label ?? segment?.label ?? dataSource?.label
  data.titleA = `${year}年${pointerType.label}${name}主机厂动力配套量（国内）`
  data.titleB = `${year}年${pointerType.label}${name}主机厂份额变化（国内）`
  data.titleC = `${year}年${pointerType.label}${name}主机厂动力配套量占比（国内）`
  data.titleD = `近五年${name}主机厂份额变化（国内）`
}


// Tooltip组件渲染
const TooltipComponent1 = (propos) => {
  // 当 name 为其他 value 为 0 或空 就删除该项
  let params = propos.params
  if (Array.isArray(params)) {
    params = params.filter(item => {
      if (item.seriesName === '其他' && (!item.value || item.value === 0)) {
        return false
      }
      return true
    })
  }

  return (
    <Tpis {...propos} params={params}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{item.value || 0}% </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

:deep(.el-row) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}

:deep(.el-col) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
