<template>
  <div class="wrap">
    <SearchFormResource :params="store.params" @change="getParams" />
    <el-row>
      <el-col :span="24">
        <mixBarLine
          v-loading="loading.A"
          title="新能源渗透率趋势"
          :series="store.seriesChartsA"
          :grid="{ left: 46, bottom: 46, right: 125, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          :color="['#051C2C', '#00A9F4', '#FFB300']"
          show-total
          height="15%"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <bar
          titleIcon="data2"
          v-loading="loading.B"
          title="燃料结构销量走势"
          :series="store.seriesChartsB"
          y-axis-name="单位：(万台)"
          :precision="1"
          :color="['#115E93', '#1E8AA9', '#00A9F4', '#5FCEFF']"
          :grid="{ left: 46, bottom: 46, right: 100, top: 46 }"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }"
          show-total
          height="15%"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import mixBarLine from '@/views/components/echarts/mixBarLine.vue'
import bar from '@/views/components/echarts/bar.vue'
import BlockTitle from '@/views/components/BlockTitle.vue'
import SearchFormResource from './components/SearchFormResource.vue'
import { homePagePermeability } from '@/api/intelligence/homePage.js'
import { dataConvertForPercentTopN } from '../../../utils/dataconvert.js'

const vueStore = useStore()
const dataSource = vueStore.state.dicts.dictsDataSource.find(v => v.label === '上险数')?.value
const { proxy } = getCurrentInstance()
const currentYear = new Date().getFullYear()
const defaultParams = {
  startYear: (currentYear - 4).toString(), // 起始年份
  endYear: currentYear.toString(), // 截止年份
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  month: '12', // 月
  quarter: '', // 季度
  dataSource: dataSource, // 数据来源
  segment: '商用车', // 板块
  subMarket1: '', // 细分市场1
  subMarket2: '', // 细分市场2
  manuFacturer: '', // 主机厂
  engineFactory: '', // 发动机厂
  fuelType: '', // 燃料
  breed: '',
  dataType: [], // 数据分类(汽油、微客、微改、微卡)
  year: [(currentYear - 4).toString(), currentYear.toString()] // 年份区间（仅前端操作用）
}
const store = reactive({
  seriesChartsA: [], // 渗透率图表数据
  seriesChartsB: [], // 燃料结构图表数据
  params: defaultParams
})

const loading = reactive({
  A: false,
  B: false
})

/**
 * @description 获取页面echarts基础数据，组装成符合echarts数据
 * @param params 搜索参数
 */
const getChartData = param => {
  if (loading.A || loading.B) {
    proxy.$modal.msgError('数据正在处理，请勿重复提交')
    return
  }
  store.seriesChartsA = []
  store.seriesChartsB = []

  loading.A = true
  loading.B = true
  const params = JSON.parse(JSON.stringify(param))
  params.dataType = params.dataType.join()
  if (params.year) delete params.year
  // pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  const pointerType = params.pointerType
  homePagePermeability(params, 'trend')
    .then(res => {
      if (res.code === 200) {
        // 处理渗透率年数据
        if (res.data.trendYearList) {
          for (let i = 0; i < res.data.trendYearList.length; i++) {
            res.data.trendYearList[i].trend = res.data.trendYearList[i].year_trend
            if (res.data.trendYearList.length - 1 === i) {
              res.data.trendYearList[i].name = `${res.data.trendYearList[i].year}年累计`
            } else {
              res.data.trendYearList[i].name = `${res.data.trendYearList[i].year}年`
            }
          }
        }

        if (res.data.trendList) {
          if (pointerType === '1') {
            const quarterArray = {
              Q1: '第一季度',
              Q2: '第二季度',
              Q3: '第三季度',
              Q4: '第四季度'
            }
            res.data.trendList.forEach(element => {
              element.name = quarterArray[element.quarter]
                ? quarterArray[element.quarter]
                : element.quarter
            })
          } else {
            res.data.trendList.forEach(el => {
              el.name = `${el.month}月`
            })
          }
        }

        const trendListOrigin = []
        if (res.data.trendYearList && res.data.trendYearList.length > 0) {
          trendListOrigin.push(...res.data.trendYearList)
        }
        if (res.data.trendList && res.data.trendList.length > 0) {
          trendListOrigin.push(...res.data.trendList)
        }
        trendListOrigin.forEach(el => {
          el.trend = el.trend.toString().replace(/%/g, '') - 0
        })
        const trendList = formateChartData(trendListOrigin, [
          {
            name: '电',
            type: 'bar',
            stack: 'total',
            dataNameKey: 'name',
            dataValueKey: 'electricity',
            data: []
          },
          {
            name: '非电',
            type: 'bar',
            stack: 'total',
            dataNameKey: 'name',
            dataValueKey: 'unelectricity',
            data: []
          },
          { name: '纯电渗透率', type: 'line', dataNameKey: 'name', dataValueKey: 'trend', data: [] }
        ])

        store.seriesChartsA = dataConvertForPercentTopN(trendList, 0, 0)
      }
      loading.A = false
    })
    .catch(() => {
      loading.A = false
    })
  homePagePermeability(params, 'fuel')
    .then(res => {
      if (res.code === 200) {
        const data = JSON.parse(JSON.stringify(res.data))
        // 处理燃料结构图表数据
        if (res.data.fuelYearList) {
          for (let i = 0; i < res.data.fuelYearList.length; i++) {
            if (res.data.fuelYearList.length - 1 === i) {
              res.data.fuelYearList[i].name = `${res.data.fuelYearList[i].year}年累计`
            } else {
              res.data.fuelYearList[i].name = `${res.data.fuelYearList[i].year}年`
            }
          }
        }
        if (res.data.fuelList) {
          if (pointerType === '1') {
            const quarterArray = {
              Q1: '第一季度',
              Q2: '第二季度',
              Q3: '第三季度',
              Q4: '第四季度'
            }
            res.data.fuelList.forEach(element => {
              element.name = quarterArray[element.quarter]
                ? quarterArray[element.quarter]
                : element.quarter
            })
          } else {
            res.data.fuelList.forEach(el => {
              el.name = `${el.month}月`
            })
          }
        }

        const fuelYearListOrigin = []

        if (res.data.fuelYearList && res.data.fuelYearList.length > 0) {
          fuelYearListOrigin.push(...res.data.fuelYearList)
        }
        if (res.data.fuelList && res.data.fuelList.length > 0) {
          fuelYearListOrigin.push(...res.data.fuelList)
        }
        const fuelYearList = formateChartData(fuelYearListOrigin, [
          {
            name: '燃料电池',
            type: 'bar',
            dataNameKey: 'name',
            dataValueKey: 'fuelcell',
            data: []
          },
          { name: '混合动力', type: 'bar', dataNameKey: 'name', dataValueKey: 'mixture', data: [] },
          {
            name: '纯电动',
            type: 'bar',
            dataNameKey: 'name',
            dataValueKey: 'electricity',
            data: []
          }
        ])
        store.seriesChartsB = dataConvertForPercentTopN(fuelYearList, 0, 0)
      }
      loading.B = false
    })
    .catch(() => {
      loading.B = false
    })
}
/**
 * @description 搜索返回值
 * @param params 搜索值
 */
const getParams = params => {
  store.params = params
  getChartData(params)
}

const formateChartData = (data, structure) => {
  const response = [...structure]
  data.forEach(el => {
    response.forEach(item => {
      item.data.push({ name: el[item.dataNameKey], value: el[item.dataValueKey] })
    })
  })
  return response
}
// 首次加载请求由头部组件处理好数据后发起
// getChartData(store.params)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
:deep(.el-row) {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
