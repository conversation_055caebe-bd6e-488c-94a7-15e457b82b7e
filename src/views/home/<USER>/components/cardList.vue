<template>
  <el-dialog
    :title="data.title"
    v-model="data.visible"
    width="700px"
    append-to-body
    v-if="data.visible"
  >
    <div class="table-list">
      <el-form :model="data.queryParams" ref="queryRef" label-width="0" class="table-list__search">
        <el-row :gutter="16">
          <el-col :span="8"
            ><el-form-item>
              <el-date-picker
                v-model="data.queryParams.dateRange"
                value-format="YYYY-MM"
                type="monthrange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :disabled-date="disabledFeatureDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <div class="search-form__button">
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table v-loading="loading" :data="data.list" height="100%" class="table-list__content">
        <el-table-column type="index" width="55" />
        <el-table-column label="年份" prop="year" />
        <el-table-column :label="data.pointerTypeName" prop="month" />
        <el-table-column label="累计增长(%)" prop="number" show-overflow-tooltip />
      </el-table>
      <BiPagination
        :total="data.total"
        v-model:page="data.queryParams.pageNum"
        v-model:limit="data.queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import BiPagination from '@/views/components/BiPagination.vue'
import { homePageCardDtlList } from '@/api/intelligence/macroData.js'
import formValidate from '@/utils/hooks/formValidate.js'
const TRANSLATE_TITLE = [
  {
    label: 'GDP',
    value: 'gdp'
  },
  {
    label: '居民消费价格',
    value: 'cpi'
  },
  {
    label: '工业生产者出厂价格',
    value: 'ppi'
  },
  {
    label: '制造业采购经理人',
    value: 'pmi'
  },
  {
    label: '固定资产投资',
    value: 'invest'
  },
  {
    label: '社会消费品零售总额',
    value: 'consumption'
  },
  {
    label: '进出口',
    value: 'impExp'
  }
]
const currentYear = new Date().getFullYear()
const data = reactive({
  visible: false,
  title: '详情',
  pointerTypeName: '季度',
  queryParams: {
    startYear: (currentYear - 1).toString(),
    startMonth: '01',
    endYear: currentYear.toString(),
    endMonth: (new Date().getMonth() + 1).toString(),
    macroType: '', // gdp:GDP累计增速;cpi:居民消费价格增速;ppi:工业生产者出厂价格增速;pmi:制造业采购经理人指数;invest:投资累计增速;consumption:消费累计增速;impExp:进出口累计增速
    page: 1,
    pageSize: 15,
    dateRange: [
      `${(currentYear - 1).toString()}-01`,
      `${currentYear}-${new Date().getMonth() + 1 > 9 ? (new Date().getMonth() + 1).toString() : '0' + (new Date().getMonth() + 1).toString()}`
    ]
  },
  total: 0,
  list: []
})
const loading = ref(false)

watch(
  () => data.queryParams.dateRange,
  val => {
    if (!val) {
      data.queryParams.startYear = ''
      data.queryParams.startMonth = ''
      data.queryParams.endYear = ''
      data.queryParams.endMonth = ''
    } else {
      const start = val[0] ?? '-'
      const end = val[1] ?? '-'
      const startData = start.split('-')
      const endData = end.split('-')
      data.queryParams.startYear = startData[0] ?? ''
      data.queryParams.startMonth = startData[1] ?? ''
      data.queryParams.endYear = endData[0] ?? ''
      data.queryParams.endMonth = endData[1] ?? ''
    }
  },
  {
    deep: true
  }
)

const { disabledFeatureDate } = formValidate()

/** 详情按钮操作 */
function toggleDialog(row) {
  data.pointerTypeName = row.macroSubType
  data.visible = true
  const title = row.macroType
  const item = TRANSLATE_TITLE.find(el => title.includes(el.label))
  data.queryParams.macroType = item.value
  data.title = item.label + '数据'
  getList()
}

const getList = async () => {
  const params = JSON.parse(JSON.stringify(toRaw(data.queryParams)))
  delete params.dateRange
  const res = await homePageCardDtlList(params).catch(e => e)
  if (res.code !== 200) return
  data.list = res.rows
  data.total = res.total
}

const resetQuery = () => {
  data.queryParams.dateRange = []
  data.queryParams.startYear = ''
  data.queryParams.startMonth = ''
  data.queryParams.endYear = ''
  data.queryParams.endMonth = ''
  data.queryParams.page = 1
  getList()
}
defineExpose({
  toggleDialog
})
</script>

<style lang="scss" scoped>
.table-list {
  height: 70vh;
}
</style>
