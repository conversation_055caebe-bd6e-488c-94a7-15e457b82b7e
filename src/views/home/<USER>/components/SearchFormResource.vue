<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            :clearable="false"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="pointerType">
          <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            // hide: true,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment'
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            key: 'subMarket2',
            hide: true,
            disabled: data.disabledSubMarket2
          }
        ]"
        :propsFuelType="{ name: '燃料', key: 'fuelType', show: true, type: 'B' }"
        :propsBreed="{ name: '品系', key: 'breed', show: true, disabled: data.disabledBreed }"
        :propsWeightMidLight="{
          name: '重中轻',
          key: 'weightMidLight',
          show: true,
          disabled: false
        }"
        :propsDataType="{
          name: '数据扩展',
          key: 'dataType',
          show: true
        }"
        :xs="8"
        :sm="8"
        :md="3"
      />
      <el-col :span="3">
        <el-form-item>
          <el-button type="primary" @click="toggleSearch">查询</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from './DictsResource.vue'
import { dictsPointerType } from '@/utils/common/dicts.js'
// import formValidate from '@/utils/hooks/formValidate.js'
import useInnerData from '@/utils/hooks/innerData.js'

const store = useStore()

const emit = defineEmits(['change'])

const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      month: '', // 月
      quarter: '', // 季度
      queryType: '', // 查询类型0发动机，1整车
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', //  细分市场2
      manuFacturer: '', // 主机厂
      engineFactory: '', // 发动机厂111
      breed: '', // 品系
      fuelType: '', // 燃料
      province: '', // 省
      dataType: [], // 数据扩展
      city: '', // 市
      dataSource: '',
      weightMidLight: ''
    })
  }
})
const data = reactive({
  disabledSubMarket2: false,
  disabledBreed: false,
  linkageData: [] // 多级联动数据
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)

watch(
  () => params.dataSource,
  val => {
    if (val === '1') {
      // 上险数
      initDateRange('上险数')
      params.segment = '商用车'
    } else if (val === '6') {
      // 货运新增数
      initDateRange('货运新增数')
      params.segment = '商用车'
      params.subMarket1 = '卡车'
    }
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)

watch([() => params.subMarket2, () => params.breed], val => {
  if (val[0] && !val[1]) {
    data.disabledSubMarket2 = false
    data.disabledBreed = true
  } else if (!val[0] && val[1]) {
    data.disabledSubMarket2 = true
    data.disabledBreed = false
  } else {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
  }
})

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  delete data.subMarket2
  emit('change', data)
}
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['货运新增数', '上险数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}

initDateRange('货运新增数', true)
getDictsData()
</script>
