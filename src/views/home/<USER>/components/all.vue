<template>
  <div class="collapse-box">
    <el-collapse v-model="data.active">
      <el-collapse-item v-for="i in props.list" :name="i.value">
        <template #title>
          <div class="title-in">{{ i.label }}</div>
        </template>
        <el-table
          :data="i.list"
          class="table-box"
          :show-header="false"
          @row-click="toggleList"
          style="width: 100%"
        >
          <el-table-column>
            <template #default="{ row }">
              <div class="ellipsis">{{ row.infoContent }}</div>
            </template>
          </el-table-column>
          <el-table-column width="40">
            <template #default="{ row }">
              <Document
                v-if="row.hasAttachment === 1 || row.hasAttachment === '1'"
                style="width: 14px"
              />
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" width="120" align="right" />
        </el-table>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { nextTick } from 'vue'

const router = useRouter()
const props = defineProps({
  list: {
    type: Array,
    required: false,
    default: () => []
  }
})
watch(
  () => props.list,
  async val => {
    if (val && val.length > 0) {
      await nextTick()
      data.active = val.map(v => v.value)
    }
  },
  { deep: true, immediate: true }
)
const data = reactive({
  active: ['0']
})

function toggleList(ev) {
  router.push({ path: '/home/<USER>', query: toRaw(ev) })
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.table-box {
  :deep(.cell) {
    cursor: pointer;
    font-size: 16px;
  }
}
:deep(.el-table__row) {
  .el-table__cell:last-child {
    .cell {
      text-align: right;
    }
  }
}
:deep(.el-collapse-item__header) {
  position: relative;
  padding: $bi-layout-margin;
  color: #051c2c;
  background: #f1f2f2;
  padding: 0;
  border-radius: 8px;
}
.title-in {
  width: 20%;
  min-width: 240px;
  padding: 0 $bi-layout-margin;
  background: #bae1ff;
  text-align: left;
  border-radius: 8px 40px 0 8px;
  // border-top: 1px solid #fff;
  font-size: 18px;
  font-weight: bolder;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0px;
}
</style>
