<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :span="4">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="yearrange"
            range-separator="-"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            :clearable="false"
            start-placeholder="开始年份"
            end-placeholder="结束年份"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2">
        <el-form-item prop="pointerType">
          <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="2">
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            disabled: true,
            // hide: true,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment'
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            key: 'subMarket2',
            disabled: data.disabledSubMarket2
          }
        ]"
        :propsEngineFactory="{ name: '发动机厂', key: 'engineFactory', show: false }"
        :propsFuelType="{ name: '燃料', key: 'fuelType', show: false, type: 'A' }"
        :propsBreed="{ name: '品系', key: 'breed', show: true, disabled: data.disabledBreed }"
      />
      <el-col v-if="params.dataSource === '1'" :span="params.dataType.length > 2 ? 5 : 3">
        <el-form-item prop="dataType">
          <el-select
            v-model="params.dataType"
            placeholder="数据扩展"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictDataType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="2">
        <el-form-item>
          <el-button type="primary" @click="toggleSearch">查询</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType,  dictDataType } from '@/utils/common/dicts.js'
import useInnerData  from '@/utils/hooks/innerData.js'
// import formValidate from '@/utils/hooks/formValidate.js'

// const { disabledFeatureDate } = formValidate()

const emit = defineEmits(['change'])
const store = useStore()

const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      startYear: '', // 起始年份
      endYear: '', // 截止年份
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      month: '', // 月
      quarter: '', // 季度
      dataSource: '', // 数据来源
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', // 细分市场2
      manuFacturer: '', // 主机厂
      engineFactory: '', // 发动机厂
      fuelType: '', // 燃料
      dataType: [], // 数据分类(汽油、微客、微改、微卡)
      breed: '',
      year: [] // 年份区间（仅前端操作用）
    })
  }
})

const data = reactive({
  disabledSubMarket2: false,
  disabledBreed: false,
  linkageData: [] // 多级联动数据
})

const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const { initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(
    params,
    toggleSearch,
    true
  )
watch(
  () => params.pointerType,
  val => {
    innerdate(params.endYear)
  }
)

watch(
  () => params.year,
  val => {
    if (val) {
      params.startYear = val[0]
      params.endYear = val[1]
    } else {
      params.startYear = ''
      params.endYear = ''
    }
    innerdate(params.endYear)
  }
)

watch([() => params.subMarket2, () => params.breed], val => {
  if (val[0] && !val[1]) {
    data.disabledSubMarket2 = false
    data.disabledBreed = true
  } else if (!val[0] && val[1]) {
    data.disabledSubMarket2 = true
    data.disabledBreed = false
  } else {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
  }
})

// function getCurrentQuarter() {
//   const now = new Date()
//   const month = now.getMonth() // 0-11
//   return Math.floor(month / 3) + 1 // 1-4
// }

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  if (params.dataSource !== '1') {
    params.dataType = []
  }
  const data = toRaw(params)
  emit('change', data)
}

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['上险数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}
initDateRange('上险数', true)
getDictsData()
</script>
