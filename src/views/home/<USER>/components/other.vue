<template>
  <el-table
    :data="props.list"
    class="table-box"
    height="calc(100% - 50px)"
    :show-header="false"
    @row-click="toggleList"
    style="width: 100%"
  >
    <el-table-column>
      <template #default="{ row }">
        <div class="ellipsis">{{ row.infoContent }}</div>
      </template>
    </el-table-column>
    <el-table-column width="40">
      <template #default="{ row }">
        <Document v-if="row.hasAttachment === 1 || row.hasAttachment === '1'" style="width: 14px" />
      </template>
    </el-table-column>
    <el-table-column prop="submitTime" width="120" align="right" />
  </el-table>
</template>

<script setup>
const router = useRouter()
const props = defineProps({
  list: {
    // 是否展示标题
    type: Array,
    required: false,
    default: () => []
  }
})

function toggleList(ev) {
  router.push({ path: '/home/<USER>', query: toRaw(ev) })
}
</script>

<style lang="scss" scoped>
// el-tabs 重写
:deep(.el-tabs) {
  height: 100%;
  .el-tab-pane {
    height: 100%;
  }
}
:deep(.el-tabs__header) {
  margin: 0;
}
:deep(.el-tabs--border-card) {
  & > .el-tabs__content {
    padding: 0 !important;
  }
}
.table-box {
  padding-top: 12px;
  :deep(.cell) {
    cursor: pointer;
  }
}
:deep(.el-table__row) {
  .el-table__cell:last-child {
    .cell {
      text-align: right;
    }
  }
}
</style>
