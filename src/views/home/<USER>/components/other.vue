<template>
  <el-tabs v-model="tabsActive">
    <el-tab-pane v-for="i in tabs" :label="i.label" :name="i.value">
      <el-table
        :data="i.list"
        class="table-box"
        height="calc(100% - 50px)"
        @row-click="toggleList"
        style="width: 100%"
      >
        <el-table-column prop="title" />
        <el-table-column prop="time" width="150" />
      </el-table>
      <BiPagination
        :total="data.total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
const router = useRouter()
import BiPagination from '@/views/components/BiPagination.vue'
const tabsData = [
  { label: '宏观动态', value: '0', list: [] },
  { label: '行业趋势与政策法规', value: '1', list: [] },
  { label: '战略合作', value: '2', list: [] },
  { label: '经营动态', value: '3', list: [] },
  { label: '高管动态', value: '4', list: [] },
  { label: '人事变动', value: '5', list: [] },
  { label: '市场活动与订单', value: '6', list: [] }
]
const json = {
  title: '公开新闻很多文字很多文字很多文字很多文字',
  read: '21',
  unread: '123',
  time: '2024-09-11 12:00',
  check: 'y'
}
tabsData.forEach((item, index) => {
  for (let i = 0; i < 27; i++) {
    item.list.push({
      ...json,
      title: `${json.title}-${json.check}-${item.label}-${index}${i}`
    })
  }
})
const data = reactive({
  total: 20,
  queryParams: {
    pageNum: 1,
    pageSize: 10
  },
  tabsActive: '0',
  tabs: tabsData
})
const { queryParams, tabs, tabsActive } = toRefs(data)

function getList() {}
function toggleList(ev) {
  console.log(ev)
  router.push('/home/<USER>')
}
</script>

<style lang="scss" scoped>
// el-tabs 重写
:deep(.el-tabs) {
  height: 100%;
  .el-tab-pane {
    height: 100%;
  }
}
:deep(.el-tabs__header) {
  margin: 0;
}
:deep(.el-tabs--border-card) {
  & > .el-tabs__content {
    padding: 0 !important;
  }
}
.table-box {
  padding-top: 12px;
  :deep(.el-table__header-wrapper) {
    display: none;
  }
  :deep(.cell) {
    cursor: pointer;
  }
}
</style>
