<template>
  <div class="word bi-loading" v-loading="loading.sending">
    <el-page-header
      v-if="data.showSearch"
      :icon="ArrowLeftBold"
      @back="resetSearch"
      class="bi-page-header"
    >
      <template #content>
        <span> </span>
      </template>
      <div class="search-box">
        <el-table
          :data="data.searchListData"
          class="table-box"
          height="calc(100% - 50px)"
          :show-header="false"
          @row-click="toggleList"
          style="width: 100%"
        >
          <el-table-column>
            <template #default="{ row }">
              <div class="ellipsis">
                <WordHighlighter
                  :query="data.searchParams.searchKey"
                  :highlightStyle="{ 'font-weight': 'bolder', background: 'transparent' }"
                  >{{ row.infoContent }}</WordHighlighter
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column width="40">
            <template #default="{ row }">
              <Document
                v-if="row.hasAttachment === 1 || row.hasAttachment === '1'"
                style="width: 14px"
              />
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" width="120" align="right" />
        </el-table>
        <BiPagination
          :total="data.searchTotal"
          v-model:page="data.searchParams.pageNum"
          v-model:limit="data.searchParams.pageSize"
          @pagination="searchList"
        />
      </div>
    </el-page-header>
    <template v-else>
      <div class="word-form">
        <el-form :model="data.params" label-width="0" :inline="true" class="search-form">
          <el-row :gutter="16">
            <el-col :xs="12" :sm="12" :md="3">
              <el-form-item prop="reportId">
                <el-select
                  v-model="data.params.reportId"
                  placeholder="期数"
                  filterable
                  @change="changeParams"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in data.dictsPeriods"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="21">
              <el-form-item prop="plate" class="bi-segmented-page">
                <el-segmented
                  v-model="data.params.plate"
                  :options="data.dictBelong"
                  @change="changePlate"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-tabs
          v-model="data.tabsActive"
          @tab-change="changeInfoType"
          class="bi-tabs-in"
          :class="device === 'mobile' ? 'mobile-word-tabs' : 'word-tabs'"
        >
          <el-tab-pane v-for="i in data.tabs" :key="i.label" :label="i.label" :name="i.value">
            <all v-if="data.tabsActive === '全部'" :list="i.list" />
            <other v-else :list="i.list" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
  </div>
</template>

<script setup>
import WordHighlighter from 'vue-word-highlighter'
import all from './all.vue'
import other from './other.vue'
import BiPagination from '@/views/components/BiPagination.vue'
import { ArrowLeftBold } from '@element-plus/icons-vue'
import {
  getPlateReportById,
  homePageMacroDataList,
  getInfoTypeById
} from '@/api/intelligence/report.js'
import { queryInfoByReportId, queryByKeyWord } from '@/api/intelligence/info.js'
const store = useStore()
const device = computed(() => store.state.biapp.device)
const router = useRouter()
const emit = defineEmits(['reset'])
const props = defineProps({
  params: {
    type: Object,
    required: false,
    default: () => ({ year: [], beginTime: '', endTime: '', keyword: '' })
  },
  isActive: {
    type: Boolean,
    required: true,
    default: false
  }
})

const data = reactive({
  dictsPeriods: [], // 期数字典
  dictBelong: [], // 期数右侧分类字典
  notAllTabs: [],
  tabs: [],
  allData: [], // 所有数据
  tabsActive: '全部',
  // 请求期数接口的参数
  params: {
    reportId: '', // 期数
    plate: '', // 期数右边的类型
    infoType: '', // 期数下边的类型
    keyword: '',
    currentStatus: '', // 当前状态
    handlerUsername: '', // 当前处理人姓名
    createUser: '', // 创建人
    updateUser: '', // 修改人
    reportType: '', // 报告类型
    beginTime: '', // 开始日期(2024-01-01)
    endTime: '' // 结束日期(2024-12-01)
  },
  showSearch: false,
  searchParams: {
    startDate: '',
    endDate: '',
    searchKey: '',
    pageNum: 1,
    pageSize: 10
  },
  searchListData: [],
  searchTotal: 0
})
const loading = reactive({
  sending: false
})

watch(
  () => props.params,
  val => {
    if (props.isActive) {
      if (val.beginTime !== '' || val.endTime !== '' || val.keyword !== '') {
        data.showSearch = true
        data.searchParams.startDate = val.beginTime
        data.searchParams.endDate = val.endTime
        data.searchParams.searchKey = val.keyword
        searchList()
      } else {
        data.showSearch = false
        data.searchParams.startDate = ''
        data.searchParams.endDate = ''
        data.searchParams.searchKey = ''
        data.searchParams.pageNum = 1
      }
    }
  },
  { deep: true }
)
watch(
  () => props.isActive,
  val => {
    if (val) {
      initDictsPeriods()
    }
  }
)

/**
 * @description 获取期数字典
 * @param params
 */
const initDictsPeriods = async () => {
  const params = data.params
  // 获取期数
  const res = await homePageMacroDataList(params).catch(e => e)
  if (res.code !== 200) return
  const list = res.rows
  data.params.reportId = ''
  data.dictsPeriods = list.map((el, index) => {
    if (index === 0) {
      // 设置期数默认值
      data.params.reportId = el.id
    }
    return { label: `第${el.releaseNum}期`, value: el.id }
  })
  await getTypes(data.params.reportId)
}

const getTypes = async reportId => {
  const response = await getPlateReportById({ reportId }).catch(e => e)
  if (response.code !== 200) return
  data.params.plate = '全部'
  const defaultRank = ['全部', '商用车', '通机', '船电', '新能源', '海外']
  const newRank = Array.from(new Set([...defaultRank, ...response.data, '其他']))
  const dictBelong = newRank.map(v => ({
    label: v,
    value: v,
    list: []
  }))
  data.dictBelong = dictBelong

  const res = await getInfoTypeById({ reportId }).catch(e => e)
  if (res.code !== 200) return
  const defaultTabs = [
    '人事',
    '行业趋势与法规政策',
    '经营动态',
    '市场活动及订单',
    '供货价格及商务政策',
    '终端返利及服务政策',
    '产品与技术',
    '产品市场表现'
  ]
  const newTabs = Array.from(new Set([...defaultTabs, ...res.data]))
  const notAllTabs = newTabs.map(v => ({
    label: v,
    value: v,
    list: []
  }))
  data.notAllTabs = notAllTabs
  data.params.infoType = '全部'
  data.tabsActive = '全部'
  data.tabs = [
    {
      label: '全部',
      value: '全部',
      list: []
    },
    ...notAllTabs
  ]
  await getList()
}

const getList = async () => {
  if (loading.sending) return
  loading.sending = true
  const response = await queryInfoByReportId({ reportId: data.params.reportId }).catch(e => e)
  if (response.code !== 200) {
    loading.sending = false
    return
  }
  const list = response.data
  data.allData = list
  await nextTick()
  loading.sending = false
  getCalculateList('全部', '全部')
}

// 改变板块
const changePlate = plate => {
  data.params.plate = plate
  const infoType = data.params.infoType
  getCalculateList(plate, infoType)
}

// 改变tabs
const changeInfoType = infoType => {
  data.params.infoType = infoType
  const plate = data.params.plate
  getCalculateList(plate, infoType)
}
const getCalculateList = async (plate, infoType) => {
  if (loading.sending) return
  loading.sending = true
  const allData = JSON.parse(JSON.stringify(data.allData))
  let list = []
  if (plate === '全部' && infoType === '全部') {
    list = allData
  } else if (plate === '全部') {
    allData.forEach(el => {
      if (el.infoType === infoType) {
        list.push(el)
      }
    })
  } else if (infoType === '全部') {
    allData.forEach(el => {
      if (el.plate === plate) {
        list.push(el)
      }
    })
  } else {
    allData.forEach(el => {
      if (el.infoType === infoType && el.plate === plate) {
        list.push(el)
      }
    })
  }

  if (infoType === '全部') {
    // 处理手风琴效果
    const notAllTabs = JSON.parse(JSON.stringify(data.notAllTabs))
    notAllTabs.forEach(element => {
      list.forEach(el => {
        if (el.infoType === element.value) {
          element.list.push(el)
        }
      })
    })
    list = notAllTabs
  }

  // 找tabs下标
  const index = data.tabs.findIndex(el => el.value === infoType)
  data.tabs[index].list = list
  await nextTick()
  loading.sending = false
}
// 改变期数
const changeParams = async () => {
  await getTypes(data.params.reportId)
}

const searchList = async () => {
  if (loading.sending) return
  loading.sending = true
  const response = await queryByKeyWord(data.searchParams).catch(e => e)
  if (response.code !== 200) {
    loading.sending = false
    return
  }
  const list = response.rows
  data.searchListData = list
  data.searchTotal = response.total
  await nextTick()
  loading.sending = false
}
function toggleList(ev) {
  const query = {
    searchKey: data.searchParams.searchKey,
    ...toRaw(ev)
  }
  router.push({ path: '/home/<USER>', query })
}
const resetSearch = () => {
  emit('reset', true)
}
initDictsPeriods()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.word {
  width: 100%;
  // padding: $bi-layout-margin;
  // height: calc($bi-main-height - 40px);
  // el-tabs 重写
  .word-form {
    margin-top: 20px;
    background: #fff;
    padding: 0 20px 20px 20px;
    border-radius: 8px;
    // height: calc($bi-main-height - 42px - 76px);
    .el-tabs__content {
      overflow: auto;
    }
  }
  :deep(.el-tabs.word-tabs) {
    background: #fff;
    // padding: 15px  20px;
    border-radius: 8px;
    height: calc($bi-main-height - 42px - 76px - 90px);
    .el-tabs__content {
      overflow: auto;
    }
  }
  :deep(.el-tabs.mobile-word-tabs) {
    background: #fff;
    // padding: 15px  20px;
    border-radius: 8px;
    height: calc($bi-main-height - 42px - 76px - 250px);
    .el-tabs__content {
      overflow: auto;
    }
  }
}
.table-box {
  :deep(.cell) {
    cursor: pointer;
    font-size: 16px;
  }
}
.search-box {
  height: calc($bi-main-height - 140px);
  :deep(.cell) {
    cursor: pointer;
    font-size: 16px;
  }
}
:deep(.el-table__row) {
  .el-table__cell:last-child {
    .cell {
      text-align: right;
    }
  }
}
.search-form {
  background: transparent;
  padding: 20px $bi-layout-margin 4px 0;
  margin: 0;
}
</style>
