<template>
  <el-tabs v-model="data.active" v-loading="loading.sending" class="bi-loading bi-tabs-in">
    <el-tab-pane v-for="i in data.tabs" :label="i.label" :name="i.value">
      <el-table
        :data="i.list"
        class="table-box"
        height="calc(100% - 50px)"
        :show-header="false"
        @row-click="toggleList"
        style="width: 100%"
      >
        <el-table-column>
          <template #default="{ row }">
            <div class="ellipsis">
              <WordHighlighter
                :query="data.params.searchKey"
                :highlightStyle="{ 'font-weight': 'bolder', background: 'transparent' }"
                >{{ row.title }}</WordHighlighter
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column width="60" align="right">
          <template #default="{ row }">
            <div class="views">
              <el-icon :size="12" color="#115E93" class="icon"> <View /> </el-icon>
              <div class="num">{{ row.clickNum }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="time" width="120" show-overflow-tooltip align="right" />
      </el-table>
      <BiPagination
        :total="data.tabs[currentIndex].total"
        v-model:page="data.params.pageNum"
        v-model:limit="data.params.pageSize"
        @pagination="initData"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { View } from '@element-plus/icons-vue'
import BiPagination from '@/views/components/BiPagination.vue'
import WordHighlighter from 'vue-word-highlighter'

import { queryHotNews } from '@/api/intelligence/tag.js'
import { nextTick } from 'vue'

const router = useRouter()

const tabsData = [
  {
    label: '宏观经济',
    value: '宏观经济',
    list: [],
    total: 0
  },
  {
    label: '经营动态',
    value: '经营动态',
    list: [],
    total: 0
  },
  {
    label: '高管动态',
    value: '高管动态',
    list: [],
    total: 0
  },
  {
    label: '人事变动',
    value: '人事变动',
    list: [],
    total: 0
  },
  {
    label: '市场动态',
    value: '市场动态',
    list: [],
    total: 0
  },
  {
    label: '政策法规',
    value: '政策法规',
    list: [],
    total: 0
  },
  {
    label: '行业趋势',
    value: '行业趋势',
    list: [],
    total: 0
  },
  {
    label: '产品与技术',
    value: '产品与技术',
    list: [],
    total: 0
  },
  {
    label: '战略合作',
    value: '战略合作',
    list: [],
    total: 0
  }
]

const props = defineProps({
  params: {
    type: Object,
    required: false,
    default: () => ({ year: [], beginTime: '', endTime: '', keyword: '' })
  },
  isActive: {
    type: Boolean,
    required: true,
    default: false
  }
})

const data = reactive({
  params: {
    hotTagName: '宏观经济',
    startDate: '',
    endDate: '',
    searchKey: '',
    pageNum: 1,
    pageSize: 15
  },
  active: '宏观经济',
  tabs: tabsData
})
const loading = reactive({
  sending: false
})

const currentIndex = computed(() => data.tabs.findIndex(v => v.value === data.active))
watch(
  () => props.params,
  val => {
    if (props.isActive) {
      data.params.startDate = val.beginTime
      data.params.endDate = val.endTime
      data.params.searchKey = val.keyword
      data.params.pageNum = 1
      initData()
    }
  },
  { deep: true }
)
watch(
  () => props.isActive,
  async val => {
    if (val) {
      await nextTick()
      initData()
    }
  },
  { immediate: true }
)

watch(
  () => data.active,
  val => {
    data.params.hotTagName = val
    data.params.pageNum = 1
    initData()
  },
  { deep: true }
)

function toggleList(ev) {
  router.push({
    path: '/home/<USER>',
    query: { searchKey: data.params.searchKey, newsInfoId: ev.newsId, newsTagId: ev.tagId }
  })
}

const initData = async () => {
  if (loading.sending) return
  loading.sending = true
  const params = data.params
  const res = await queryHotNews(params).catch(e => e)
  if (res.code !== 200) return (loading.sending = false)
  data.tabs[currentIndex.value].list = [...res.rows]
  data.tabs[currentIndex.value].total = res.total
  await nextTick()
  loading.sending = false
}
</script>

<style lang="scss" scoped>
// el-tabs 重写
:deep(.el-tabs) {
  height: 100%;
  .el-tab-pane {
    height: 100%;
  }
}
:deep(.el-tabs__header) {
  margin: 0;
}
:deep(.el-tabs--border-card) {
  & > .el-tabs__content {
    padding: 0 !important;
  }
}
.table-box {
  padding-top: 12px;
  :deep(.cell) {
    cursor: pointer;
  }
}
:deep(.el-table__row) {
  .el-table__cell:last-child {
    .cell {
      text-align: right;
    }
  }
}
.views {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .icon {
    margin-right: 4px;
  }
  .num {
    color: #051c2c;
  }
}
</style>
