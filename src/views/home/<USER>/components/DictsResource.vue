<template>
  <template v-for="(i, index) in prop.props" :key="i">
    <!-- 2-装机数 无品系 -->
    <el-col v-if="index === 3 && prop.propsBreed.show && prop.form.dataSource !== '2'" :span="prop?.span" :xs="prop.xs"
      :sm="prop.sm" :md="prop.md">
      <el-form-item :prop="prop.propsBreed.key">
        <el-select v-model="prop.form[prop.propsBreed.key]" :placeholder="prop.propsBreed.name"
          :clearable="!prop.propsBreed.clearable" :disabled="prop?.propsBreed?.disabled" filterable
          no-data-text="请先选择细分市场一" style="width: 100%">
          <el-option v-for="item in dicts.dictsBreed" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col v-if="!prop.props[index].hide" :span="prop?.span" :xs="prop.xs" :sm="prop.sm" :md="prop.md">
      <el-form-item :prop="prop.props[index].key">
        <el-select v-model="prop.form[prop.props[index].key]" :placeholder="prop.props[index].name"
          :clearable="i.key !== 'dataSource' && !prop.props[index].clearable" filterable
          :disabled="prop.props[index].disabled" @change="toggleLevel($event, index)" style="width: 100%">
          <el-option v-for="item in dicts[`level${index}`]" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-col>
  </template>
  <el-col v-if="prop.propsWeightMidLight.show" :span="prop?.span" :xs="prop.xs" :sm="prop.sm" :md="prop.md">
    <el-form-item :prop="prop.propsWeightMidLight.key">
      <el-select v-model="prop.form[prop.propsWeightMidLight.key]" :placeholder="prop.propsWeightMidLight.name"
        :clearable="!prop.propsWeightMidLight.clearable" :disabled="prop?.propsWeightMidLight?.disabled" filterable
        no-data-text="请先选择细分市场一" style="width: 100%">
        <el-option v-for="item in dicts.dictsWeightMidLight" :key="item.value" :label="item.label"
          :value="item.value" />
      </el-select>
    </el-form-item>
  </el-col>
  <el-col v-if="prop.propsManuFacturer.show && dicts.dictsManuFacturer.length > 0" :span="prop?.span" :xs="prop.xs"
    :sm="prop.sm" :md="prop.md">

    <el-form-item :prop="prop.propsManuFacturer.key">
      <el-select v-model="prop.form[prop.propsManuFacturer.key]" :placeholder="prop.propsManuFacturer.name"
        :clearable="!prop.propsManuFacturer.clearable" filterable style="width: 100%">
        <el-option v-for="item in dicts.dictsManuFacturer" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
  </el-col>
  <el-col v-if="prop.propsEngineFactory.show && dicts.dictsEngineFactory.length > 0" :span="prop?.span" :xs="prop.xs"
    :sm="prop.sm" :md="prop.md">
    <el-form-item :prop="prop.propsEngineFactory.key">
      <el-select v-model="prop.form[prop.propsEngineFactory.key]" :placeholder="prop.propsEngineFactory.name"
        :clearable="!prop.propsEngineFactory.clearable" filterable style="width: 100%">
        <el-option v-for="item in dicts.dictsEngineFactory" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
  </el-col>
  <!-- 数据扩展字段 -->
  <el-col
    v-if="prop.propsDataType.show && prop.form.dataSource === '1'"
    :xs="prop.xs"
    :sm="prop.sm"
    :md="prop.form[prop.propsDataType.key] && prop.form[prop.propsDataType.key].length > 2 ? 5 : prop.md"
  >
    <el-form-item :prop="prop.propsDataType.key">
      <el-select
        v-model="prop.form[prop.propsDataType.key]"
        :placeholder="prop.propsDataType.name"
        clearable
        style="width: 100%"
        @change="changeDataType"
      >
        <el-option
          v-for="item in dicts.dictsDataType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </el-col>
  <el-col v-if="prop.propsFuelType.show && dicts.dictsFuelType.length > 0" :span="prop?.span" :xs="prop.xs"
    :sm="prop.sm" :md="prop.md">
    <el-form-item :prop="prop.propsFuelType.key">
      <el-select v-model="prop.form[prop.propsFuelType.key]" :placeholder="prop.propsFuelType.name"
        :clearable="!prop.propsFuelType.clearable" filterable style="width: 100%">
        <el-option v-for="item in dicts.dictsFuelType" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
  </el-col>
  
</template>

<script setup>
import { dictDataType } from '@/utils/common/dicts.js'

// 级联选择
const emit = defineEmits(['dictsManuFacturer'])
const prop = defineProps({
  dicts: {
    // 数据字典
    type: Array,
    required: true,
    default: () => []
  },
  props: {
    // 表单key值默认首页新能源市场key
    type: Array,
    required: false,
    default: () => [
      {
        name: '数据来源',
        key: 'dataSource',
        hide: false
      },
      {
        name: '板块',
        key: 'segment',
        hide: false
      },
      {
        name: '细分市场一',
        key: 'subMarket1',
        hide: false
      },
      {
        name: '细分市场二',
        key: 'subMarket2',
        hide: false
      }
    ]
  },
  // 表单key发动机厂参数控制
  propsEngineFactory: {
    type: Object,
    required: false,
    default: () => ({ name: '发动机厂', key: 'engineFactory', show: true })
  },
  // 表单key主机厂参数控制
  propsManuFacturer: {
    type: Object,
    required: false,
    default: () => ({ name: '主机厂', key: 'manuFacturer', show: true })
  },
  // 表单key燃料参数控制
  propsFuelType: {
    type: Object,
    required: false,
    default: () => ({ name: '燃料', key: 'fuelType', show: true, type: 'B' }) // type 燃料类型（A-新能源 B-汽油 C-竞争环境-市场环境-商用车专用）
  },
  // 表单key品系参数控制
  propsBreed: {
    type: Object,
    required: false,
    default: () => ({ name: '品系', key: 'breed', show: false, disabled: false })
  },
  // 表单key重中轻参数控制
  propsWeightMidLight: {
    type: Object,
    required: false,
    default: () => ({ name: '重中轻', key: 'vehicleType', show: false, disabled: false })
  },
  // 表单key数据扩展参数控制
  propsDataType: {
    type: Object,
    required: false,
    default: () => ({ name: '数据扩展', key: 'dataType', show: false })
  },
  form: {
    // 搜索表单form
    type: Object,
    required: true,
    default: () => ({})
  },
  span: {
    type: Number,
    required: false,
    default: 4
  },
  xs: {
    type: Number,
    required: false,
    default: 8
  },
  sm: {
    type: Number,
    required: false,
    default: 8
  },
  md: {
    type: Number,
    required: false,
    default: 4
  }
})
const dicts = reactive({
  level1: [],
  level2: [],
  level3: [],
  level4: [],
  dictsEngineFactory: [], // 发动机厂
  dictsManuFacturer: [], // 主机厂
  dictsFuelType: [], // 燃料类型
  dictsBreed: [], // 品系
  dictsWeightMidLight: [], // 重中轻
  dictsDataType: [], // 数据扩展
  select1: [],
  select2: [],
  select3: [],
  select4: []
})

watch(
  () => prop.dicts,
  () => {
    initData()
  },
  {
    deep: true
  }
)

watch(
  () => prop.form.dataSource,
  (current, prev) => {
    if (current !== prev) setDefaultData()
  },
  {
    deep: true
  }
)
watch(
  () => prop.form.subMarket1,
  (current, prev) => {
    if (current !== prev) setDefaultData()
  }
)

/**
 * 初始化每级联动存储数据的空间
 */
function initDictsLevelData() {
  const linkageLevel = prop.props.length // 几级联动
  for (let i = 0; i < linkageLevel; i++) {
    if (i === 0) {
      dicts[`level${i}`] = prop.dicts
    } else {
      dicts[`level${i}`] = []
    }
  }
  // 初始化数据扩展字典
  dicts.dictsDataType = dictDataType
}

function toggleLevel(event, level, isSetDefaultData) {
  const linkageLevel = prop.props.length // 几级联动
  if (event === undefined) {
    // 清空默认值
    if (prop.props[level].key === 'dataSource') {
      dicts.dictsFuelType = []
      dicts.dictsEngineFactory = [] // 清空默认值
      if (JSON.stringify(dicts.dictsManuFacturer) !== '[]') {
        emit('dictsManuFacturer', [])
      }
      dicts.dictsManuFacturer = [] // 清空默认值
      dicts.dictsBreed = [] // 清空默认值
      dicts.dictsWeightMidLight = [] // 清空默认值

      prop.form[prop.propsFuelType.key] = isSetDefaultData ? prop.form[prop.propsFuelType.key] : ''
      prop.form[prop.propsEngineFactory.key] = isSetDefaultData
        ? prop.form[prop.propsEngineFactory.key]
        : ''
      prop.form[prop.propsManuFacturer.key] = isSetDefaultData
        ? prop.form[prop.propsManuFacturer.key]
        : ''
      prop.form[prop.propsBreed.key] = isSetDefaultData ? prop.form[prop.propsBreed.key] : ''
      prop.form[prop.propsWeightMidLight.key] = isSetDefaultData
        ? prop.form[prop.propsWeightMidLight.key]
        : ''
    }
    if (prop.props[level].key === 'segment') {
      dicts.dictsEngineFactory = [] // 清空默认值
      if (JSON.stringify(dicts.dictsManuFacturer) !== '[]') {
        emit('dictsManuFacturer', [])
      }
      dicts.dictsManuFacturer = [] // 清空默认值
      dicts.dictsBreed = [] // 清空默认值
      dicts.dictsWeightMidLight = [] // 清空默认值

      prop.form[prop.propsEngineFactory.key] = isSetDefaultData
        ? prop.form[prop.propsEngineFactory.key]
        : ''
      prop.form[prop.propsManuFacturer.key] = isSetDefaultData
        ? prop.form[prop.propsManuFacturer.key]
        : ''
      prop.form[prop.propsBreed.key] = isSetDefaultData ? prop.form[prop.propsBreed.key] : ''
      prop.form[prop.propsWeightMidLight.key] = isSetDefaultData
        ? prop.form[prop.propsWeightMidLight.key]
        : ''
    }
    if (prop.props[level].key === 'subMarket1' || prop.props[level].key === 'subMarket') {
      dicts.dictsBreed = [] // 清空默认值
      dicts.dictsWeightMidLight = [] // 清空默认值
      prop.form[prop.propsBreed.key] = isSetDefaultData ? prop.form[prop.propsBreed.key] : ''
      prop.form[prop.propsWeightMidLight.key] = isSetDefaultData
        ? prop.form[prop.propsWeightMidLight.key]
        : ''
    }

    prop.form[prop.props[level].key] = ''
    let flag = level + 1
    while (flag < linkageLevel) {
      dicts[`level${flag}`] = []
      prop.form[prop.props[flag].key] = ''
      flag++
    }
    return
  }
  let flag = level + 1
  while (flag < linkageLevel) {
    prop.form[prop.props[flag].key] = ''
    flag++
  }
  const select = dicts[`level${level}`]

  const index = select.findIndex(i => i.value === event)
  dicts[`level${level + 1}`] = select[index]?.children ?? []

  // 设置 燃料/发动机厂/主机厂
  if (prop.props[level].key === 'dataSource') {
    dicts.dictsFuelType = select[index][`fuelType${prop.propsFuelType.type}`] ?? []
    dicts.dictsEngineFactory = [] // 清空默认值
    if (JSON.stringify(dicts.dictsManuFacturer) !== '[]') {
      emit('dictsManuFacturer', [])
    }
    dicts.dictsManuFacturer = [] // 清空默认值
    dicts.dictsBreed = [] // 清空默认值
    dicts.dictsWeightMidLight = [] // 清空默认值

    prop.form[prop.propsFuelType.key] = isSetDefaultData ? prop.form[prop.propsFuelType.key] : ''
    prop.form[prop.propsEngineFactory.key] = isSetDefaultData
      ? prop.form[prop.propsEngineFactory.key]
      : ''
    prop.form[prop.propsManuFacturer.key] = isSetDefaultData
      ? prop.form[prop.propsManuFacturer.key]
      : ''
    prop.form[prop.propsBreed.key] = isSetDefaultData ? prop.form[prop.propsBreed.key] : ''
    prop.form[prop.propsWeightMidLight.key] = isSetDefaultData
      ? prop.form[prop.propsWeightMidLight.key]
      : ''
  }
  if (prop.props[level].key === 'segment') {
    dicts.dictsEngineFactory = select[index]?.engineFactory ?? []
    dicts.dictsManuFacturer = select[index]?.manuFacturer ?? []
    emit('dictsManuFacturer', toRaw(dicts.dictsManuFacturer))
    dicts.dictsBreed = [] // 清空默认值
    dicts.dictsWeightMidLight = [] // 清空默认值

    prop.form[prop.propsEngineFactory.key] = isSetDefaultData
      ? prop.form[prop.propsEngineFactory.key]
      : ''
    prop.form[prop.propsManuFacturer.key] = isSetDefaultData
      ? prop.form[prop.propsManuFacturer.key]
      : ''
    prop.form[prop.propsBreed.key] = isSetDefaultData ? prop.form[prop.propsBreed.key] : ''
    prop.form[prop.propsWeightMidLight.key] = isSetDefaultData
      ? prop.form[prop.propsWeightMidLight.key]
      : ''
  }
  // subMarket
  if (prop.props[level].key === 'subMarket1' || prop.props[level].key === 'subMarket') {
    // 当数据源为货运新增数 过滤皮卡和3.5吨以下轻卡及其他
    if (prop.form.dataSource == 6) {
      dicts.dictsBreed = select[index]?.breed ? select[index]?.breed.filter(x => {
        return !x.value.includes('皮卡') && !x.value.includes('3.5吨以下轻卡及其他')

      }) : []
    }else{
      dicts.dictsBreed = select[index]?.breed ?? []
    }


    // 如果是上线数就去除微客微卡
    if (prop.form.dataSource == 1 || prop.form.dataSource == 6) {
      dicts.dictsWeightMidLight = select[index]?.weightMidLight.filter(x => {
        return x.value !== '微卡'
      })
    } else {
      dicts.dictsWeightMidLight = select[index]?.weightMidLight ?? []
    }
    prop.form[prop.propsBreed.key] = isSetDefaultData ? prop.form[prop.propsBreed.key] : ''
    prop.form[prop.propsWeightMidLight.key] = isSetDefaultData
      ? prop.form[prop.propsWeightMidLight.key]
      : ''
  }
}

const setDefaultData = () => {
  const form = JSON.parse(JSON.stringify(toRaw(prop.form)))
  if (prop.props && prop.props[0] && prop.props[0].key && form) {
    toggleLevel(form[prop.props[0].key], 0, true)
  }
  if (prop.props && prop.props[1] && prop.props[1].key && form) {
    prop.form[prop.props[1].key] = form[prop.props[1].key]
    toggleLevel(form[prop.props[1].key], 1, true)
  }
  if (prop.props && prop.props[2] && prop.props[2].key && form) {
    prop.form[prop.props[2].key] = form[prop.props[2].key]
    toggleLevel(form[prop.props[2].key], 2, true)
  }
  if (prop.props && prop.props[3] && prop.props[3].key && form) {
    prop.form[prop.props[3].key] = form[prop.props[3].key]
    toggleLevel(form[prop.props[3].key], 3, true)
  }
}
function changeDataType(value) {
  if (value === '汽油，微客，微改， 微卡') {
    dicts.dictsFuelType = [
      { 'value': '柴油', 'label': '柴油' },
      { 'value': '气体', 'label': '气体' },
      { 'value': '气油', 'label': '气油' },
      { 'value': '其他', 'label': '其他' }
    ]
  } else {
    dicts.dictsFuelType = [
      { 'value': '柴油', 'label': '柴油' },
      { 'value': '气体', 'label': '气体' },
      { 'value': '其他', 'label': '其他' }
    ]
  }
}
// 初始化
const initData = () => {
  initDictsLevelData()
  setDefaultData()
}
</script>

<style lang="scss" scoped></style>
