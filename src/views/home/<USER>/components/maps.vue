<template>
  <div class="ratio-width" style="padding-bottom: 58%">
    <div ref="target" class="ratio-width__wrap" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, watch } from 'vue'
import echartsResize from '@/utils/hooks/echartsResize.js'
import china from '@/utils/common/chinajian.json'
import hainanother from '@/utils/common/provincejian/460300.json'

import decode from '@/utils/common/provincejian/adcodes.json'
import provinceFull2Jian from '@/utils/common/map/provinceFull2Jian.json'
import provinceJian2Full from '@/utils/common/map/provinceJian2Full.json'

import cityFull2Jian from '@/utils/common/map/cityFull2Jian.json'

import { numFormat } from '../../../components/echarts/config'
import { getGeoJson } from '../../../../utils/common/provincejian/getGeoJson'

const emit = defineEmits(['select'])

const props = defineProps({
  seriesData: {
    type: Array,
    required: false,
    default: () => []
  },
  queryType: {
    type: String,
    required: false
  }
})
const data = reactive({
  currentSelect: '', // 当前选中的省,
  needConvert: false
})
watch(
  () => {
    props.queryType
  },
  () => {
    data.currentSelect = ''
    emit('select', {currentSelect: '', drillLevel: 1})
    echarts.registerMap('china', china)
    renderEcharts()
  },
  { deep: true }
)
watch(
  () => {
    data.currentSelect
    props.seriesData
  },
  () => {
    // 发动机不下钻，整车才下钻
    // if (props.queryType == 1) {
      if (data.currentSelect) {
        data.currentSelect = provinceFull2Jian[data.currentSelect] || data.currentSelect
        getGeoJson(data.currentSelect).then(map => {
          if (map?.features) {
            echarts.registerMap(data.currentSelect, map)
            if (data.currentSelect.indexOf('海南') == 0) {
              echarts.registerMap('hainanother', hainanother)
            }
            renderEcharts(data.currentSelect)
          } else {
            // echarts.registerMap('china', china)
            // renderEcharts()
          }
        })
      } else {
        echarts?.registerMap('china', china)
        renderEcharts()
      }
    // }
    // 从市地图直接回切到发动机，需要重新加载中国地图和数据
    if (!data.currentSelect) {
      echarts.registerMap('china', china)
      renderEcharts()
    }
  },
  { deep: true }
)

// 初始化实例
let myChart = null
const target = ref(null)
onMounted(() => {
  echarts.registerMap('china', china)
  myChart = echarts.init(target.value)
  renderEcharts()
  // 监听 select 事件
  myChart.on('click', function (params) {
    data.currentSelect = params.name === data.currentSelect ? '' : params.name
    let drillLevel = 1; // 默认为中国地图层级

    console.log('地图点击事件:', params.name, '当前选中:', data.currentSelect)

    if (data.currentSelect) {
      if (decode[data.currentSelect]) {
        // 选中了一个有效的省份，进入省级地图
        drillLevel = 2

        if (data.needConvert) {
          console.log("转换省份名称:", data.currentSelect, "->", provinceJian2Full[data.currentSelect])
          data.currentSelect = provinceJian2Full[data.currentSelect]
        }
      } else {
        console.log('在decode中找不到该区域:', data.currentSelect)
        // 在decode中找不到该区域，可能是市级或其他，保持当前层级
        // 这里可以根据具体需求调整逻辑
      }
    } else {
      console.log('返回中国地图，层级设为1')
      emit('select', {currentSelect:data.currentSelect,drillLevel:1 })
      return
    }

    console.log('发送事件:', {currentSelect: data.currentSelect, drillLevel: drillLevel})
    emit('select', {currentSelect:data.currentSelect,drillLevel:drillLevel })
  })
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)
})

// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = mapName => {
  let visualMapMax = 3000
  var seriesData = JSON.parse(JSON.stringify(props.seriesData))

  for (var i in seriesData) {
    visualMapMax = Math.max(visualMapMax, seriesData[i].value)
    if (!mapName || mapName == 'china') {
      if (provinceFull2Jian[seriesData[i].name]) {
        data.needConvert = true
      } else {
        data.needConvert = false
      }
      seriesData[i].name = provinceFull2Jian[seriesData[i].name] || seriesData[i].name
    } else {
      seriesData[i].name = cityFull2Jian[seriesData[i].name] || seriesData[i].name
    }
  }
  visualMapMax = parseInt((visualMapMax + 100) / 100) * 100

  if (!mapName || mapName == 'china') {
    seriesData.push({ name: '南海诸岛', value: 0, silent: true, label: { show: false } })
    seriesData.push({ name: '其他地区1', value: 0, silent: true, label: { show: false } })
    seriesData.push({ name: '其他地区2', value: 0, silent: true, label: { show: false } })
  }

  myChart.clear()

  var geos = [
    {
      map: mapName || 'china',
      z: 0,
      top: '11%',
      silent: true,
      layoutSize: '100%', //保持地图宽高比
      itemStyle: {
        borderColor: '#66edff',
        borderWidth: 1,
        shadowBlur: 20,
        shadowColor: '#4d99ff',
        areaColor: '#1752ad',
        shadowOffsetX: 0,
        shadowOffsetY: 8
      },
      regions: [
        {
          name: '南海诸岛',
          silent: true,
          itemStyle: {
            areaColor: '#fff',
            borderWidth: 0,
            opacity: 0.5
          }
        },
        {
          name: '其他地区1',
          silent: true,
          itemStyle: {
            areaColor: '#fff',
            borderWidth: 0,
            opacity: 0
          }
        },
        {
          name: '其他地区2',
          silent: true,
          itemStyle: {
            areaColor: '#fff',
            borderWidth: 0,
            opacity: 0
          }
        }
      ]
    }
  ]

  var series = [
    {
      type: 'map',
      map: mapName || 'china',
      z: 10,
      layoutSize: '100%', //保持地图宽高比
      label: {
        show: true, // 显示省份名称或数量
        formatter: function (params) {
          return (
            params.name + ':' + (params.value && params.value > 0 ? numFormat(params.value, 0) : 0)
          )
        },
        fontSize: 8,
        color: '#333'
      },
      itemStyle: {
        show: true,
        borderColor: '#9ABAC9', // 省份边界线颜色
        borderWidth: 1 // 省份边界线宽度
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold',
          color: '#fff'
        },
        itemStyle: {
          areaColor: '#00A9F4',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowBlur: 20,
          borderWidth: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      select: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold',
          color: '#fff'
        },
        itemStyle: {
          areaColor: '#00A9F4',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowBlur: 20,
          borderWidth: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: seriesData //props.seriesData
    }
  ]

  // 针对海南的特殊处理
  if (mapName && mapName.indexOf('海南') == 0) {
    geos[0].layoutCenter = ['40%', '50%']
    geos[0].layoutSize = '60%'

    series[0].layoutCenter = ['40%', '50%']
    series[0].layoutSize = '60%'

    series.push({
      type: 'map',
      map: 'hainanother',
      z: 10,
      layoutCenter: ['80%', '50%'],
      layoutSize: '60%', //保持地图宽高比
      label: {
        show: true, // 显示省份名称或数量
        formatter: function (params) {
          return (
            params.name + ':' + (params.value && params.value > 0 ? numFormat(params.value, 0) : 0)
          )
        },
        fontSize: 10,
        color: '#333'
      },
      itemStyle: {
        show: true,
        borderColor: '#9ABAC9', // 省份边界线颜色
        borderWidth: 1 // 省份边界线宽度
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold',
          color: '#fff'
        },
        itemStyle: {
          areaColor: '#00A9F4',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowBlur: 20,
          borderWidth: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      select: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold',
          color: '#fff'
        },
        itemStyle: {
          areaColor: '#00A9F4',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowBlur: 20,
          borderWidth: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: seriesData //props.seriesData
    })
  }

  // 针对西藏的特殊处理
  if (mapName && mapName.indexOf('西藏') == 0) {
    geos[0].top = '17%'
  }
  const option = {
    tooltip: {
      trigger: 'item', // 触发类型，'item' 表示鼠标悬浮到图形上时触发
      // formatter: '{b}: {c}' // 提示框内容的格式，{b} 是名称，{c} 是值
      formatter: params => {
        let str = `${params.name}: ${numFormat(params.value || 0, 0)}<br />`
        return str
      }
    },
    // dataRange
    visualMap: {
      show: true,
      min: 0,
      max: visualMapMax,
      text: ['高', '低'],
      realtime: true,
      calculable: true,
      color: ['#4FA1E9', '#D7F9FE']
    },
    // backgroundColor: '#A8D2F4', // 设置背景色为白色

    backgroundColor: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: '#D6EBFA' // 0% 处的颜色
        },
        {
          offset: 1,
          color: '#FFFFFF' // 100% 处的颜色
        }
      ],
      global: false // 缺省为 false
    },
    geo: geos,
    series: series
  }
  myChart.setOption(option)
}

const selectArea = ev => {
  data.currentSelect = ev
  myChart.dispatchAction({
    type: 'select',
    name: ev
  })
}
defineExpose({
  selectArea
})
</script>
