<template>
  <!-- <div ref="refCharts" class="echarts" :style="{ width: '1100px', height: '700px' }" /> -->
  
  <div :style="{ width: '100%', height: '700px' }">
    <div ref="refCharts" class="echarts " style="width: 100%; height: 100%;" />

  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, reactive } from 'vue'
import echartsResize from '@/utils/hooks/echartsResize.js'

// 初始化实例
let myChart = null
const refCharts = ref(null)
onMounted(() => {
  myChart = echarts.init(refCharts.value)
  renderEcharts()
  const { resizeHandler } = echartsResize(myChart)
  window.addEventListener('resize', resizeHandler)
})

var charts = {
  nodes: [
    {
      name: '业务部门',
      value: [100, 100],
      label: {
        backgroundColor: '#115E93',
        color: '#fff'
      }
    },
    {
      name: '填写需求申请表',
      value: [100, 230]
    },
    {
      name: '部门领导同意',
      value: [100, 360]
    },
    {
      name: '竞争情报系统',
      value: [300, 100]
    },
    {
      name: '企划部信息主管',
      value: [500, 100]
    },
    {
      name: '情报系统已有',
      value: [700, 100]
    },
    {
      name: '情报系统无',
      value: [700, 200]
    },
    {
      name: '评估或请示，推送给需求人',
      value: [742, 300],
      label: {
        width: 240
      }
    },
    {
      name: '寻源或组织分析，收集分析素材',
      value: [762, 400],
      label: {
        width: 280
      }
    },
    {
      name: '入竞争情报库',
      value: [700, 500],
      label: {
        borderColor: '#839099', // 边框颜色
        color: '#839099'
      }
    },
    {
      name: '汇总分析形成简报/报告推送给器求方',
      value: [784, 600],
      label: {
        width: 320
      }
    },
    {
      name: '需求方反馈评价',
      value: [1010, 100]
    }
  ],
  linesData: [
    // 连线
    {
      name: '',
      coords: [
        [100, 130],
        [100, 200]
      ]
    },
    {
      name: '',
      coords: [
        [100, 260],
        [100, 330]
      ]
    },
    {
      name: '',
      coords: [
        [100, 390],
        [100, 430]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [100, 430],
        [300, 430]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [300, 430],
        [300, 130]
      ]
    },
    {
      name: '',
      coords: [
        [380, 100],
        [420, 100]
      ]
    },
    {
      name: '',
      coords: [
        [580, 100],
        [620, 100]
      ]
    },
    {
      name: '',
      coords: [
        [620, 120],
        [600, 120]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [600, 120],
        [600, 300]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [600, 300],
        [620, 300]
      ]
    },
    {
      name: '',
      coords: [
        [780, 200],
        [950, 200]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [950, 200],
        [950, 400]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [950, 400],
        [904, 400]
      ]
    },
    {
      name: '',
      coords: [
        [700, 400],
        [700, 470]
      ]
    },
    {
      name: '',
      coords: [
        [700, 530],
        [700, 570]
      ]
    },
    {
      name: '',
      coords: [
        [940, 600],
        [1020, 600]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [1020, 600],
        [1020, 130]
      ]
    },
    {
      name: '',
      coords: [
        [1000, 130],
        [1000, 500]
      ],
      symbol: 'none'
    },
    {
      name: '',
      coords: [
        [1000, 500],
        [780, 500]
      ]
    }
  ]
}
var option = {
  xAxis: {
    min: 0,
    max: 1100,
    show: false,
    type: 'value'
  },
  yAxis: {
    min: 0,
    max: 700,
    show: false,
    type: 'value',
    inverse: true
  },
  grid: {
    left: 0,
    right: 0,
    bottom: 0,
    top: 0
  },
  tooltip: {
    show: false,
    axisPointer: {
      type: 'shadow'
    },
    borderColor: 'white',
    backgroundColor: 'white',
    borderWidth: 1,
    padding: 0,
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  series: [
    {
      type: 'graph',
      coordinateSystem: 'cartesian2d',
      symbol: 'rect',
      // symbolSize: [80, 40],
      // edgeSymbol: ['', 'arrow'],
      // edgeSymbolSize: [1, 10],
      // lineStyle: {
      //   normal: {
      //     width: 0,
      //     color: "#CDD9E1",
      //   },
      // },
      // itemStyle: {
      //   color: "rgb(194, 194, 194)",
      // },
      // symbolOffset: [10, 0],
      // force: {
      //   edgeLength: 10, //连线的长度
      //   repulsion: 50, //子节点之间的间距
      // },
      label: {
        show: true,
        width: 160,
        height: 60,
        borderColor: '#115E93', // 边框颜色
        borderWidth: 1, // 边框宽度
        borderRadius: 6,
        backgroundColor: '#fff',
        color: '#115E93',
        fontSize: 18
      },
      data: charts.nodes
    },

    {
      type: 'lines',
      polyline: false,
      coordinateSystem: 'cartesian2d',
      lineStyle: {
        // type: "dashed",
        opacity: 1,
        width: 2,
        color: '#CDD9E1'
      },
      symbol: ['', 'arrow'],
      symbolSize: 10,
      label: {
        show: true,
        position: 'middle',
        fontSize: 16,
        color: '#08979C'
        // formatter: function (args) {
        //   let val = args.data.name;
        //   var strs = val.split("");
        //   var str = "";
        //   if (args.data.linkView) {
        //     for (var i = 0, s; (s = strs[i++]); ) {
        //       str += s;
        //       if (!(i % 15)) str += "\n";
        //     }
        //     return str;
        //   }
        // },
      },
      // lineStyle: {

      //     color: '#65B7E3',

      //     width: 2

      // },
      data: charts.linesData
    }
  ]
}
// 构建options,配置对象
/** @type EChartsOption */
const renderEcharts = () => {
  const options = option
  // 通过实例.setOption(options)
  myChart.setOption(options)
}
</script>

<style lang="scss" scoped>
.echarts {
  margin: 0 auto;
}
</style>
