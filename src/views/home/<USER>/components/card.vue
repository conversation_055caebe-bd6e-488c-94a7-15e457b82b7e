<template>
  <div class="vibe-card">
    <img :src="icons[index]" class="vibe-card__icon" />
    <div class="vibe-card__top" :style="{ color: color }">
      <div class="vibe-card__top--num">{{ number }}</div>
      <div v-if="mark" class="vibe-card__top--icon">{{ mark }}</div>
    </div>
    <div class="vibe-card__bottom">{{ title }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  number: {
    type: [String, Number],
    required: true
  },
  mark: {
    type: String,
    required: false
  },
  color: {
    type: String,
    required: false
  },
  index: {
    type: [String, Number]
  }
})
const icons = reactive([
  new URL('@/assets/images/icon/chart.png', import.meta.url).href,
  new URL('@/assets/images/icon/bag.png', import.meta.url).href,
  new URL('@/assets/images/icon/arrow.png', import.meta.url).href,
  new URL('@/assets/images/icon/profile.png', import.meta.url).href,
  new URL('@/assets/images/icon/wallet.png', import.meta.url).href,
  new URL('@/assets/images/icon/buy.png', import.meta.url).href,
  new URL('@/assets/images/icon/location.png', import.meta.url).href
])
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.vibe-card {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 106px;
  padding: $bi-layout-margin;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  background: linear-gradient(287deg, rgba(205, 228, 250, 0.8) 2%, #fafbfc 97%);
  border: 1px solid #92cdfc;
  backdrop-filter: blur(13.6px);
  &__icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 60px;
  }
  &__top {
    flex: 1;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: left;
    color: var(--el-color-primary);
    &--num {
      font-size: 36px;
    }
    &--icon {
      height: 6px;
      font-size: 18px;
    }
  }
  &__bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    color: #051c2c;
    font-size: 14px;
  }
}
</style>
