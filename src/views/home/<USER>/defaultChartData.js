export const defaultDataBMonth = [
  { name: '1月', value: '' },
  { name: '2月', value: '' },
  { name: '3月', value: '' },
  { name: '4月', value: '' },
  { name: '5月', value: '' },
  { name: '6月', value: '' },
  { name: '7月', value: '' },
  { name: '8月', value: '' },
  { name: '9月', value: '' },
  { name: '10月', value: '' },
  { name: '11月', value: '' },
  { name: '12月', value: '' }
]

export const defaultDataBQuarter = [
  { name: '第一季度', value: '' },
  { name: '第二季度', value: '' },
  { name: '第三季度', value: '' },
  { name: '第四季度', value: '' }
]

export const fillXAxisList = (list, defaultList) => {
  const response = JSON.parse(JSON.stringify(list))
  if (response.length === 0) {
    return [{ name: '', data: defaultList }]
  }
  response.forEach(element => {
    const ElementList = element.data
    defaultList.forEach((el, ind) => {
      if (el.name !== ElementList[ind].name) {
        ElementList.splice(ind, 0, { ...defaultList[ind] })
      }
    })
  })
  return response
}