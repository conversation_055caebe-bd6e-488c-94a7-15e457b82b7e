<template>
  <div class="wx-login-container">
    <el-card class="login-card">
      <div class="loading-content">
        <el-icon class="loading-icon" :size="50">
          <Loading />
        </el-icon>
        <div class="loading-text">正在通过企业微信登录...</div>
        <div class="tip-text">请稍候，正在验证您的身份信息</div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Loading } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { getQueryObject } from '@/utils'
import { ElMessage } from 'element-plus'
import store from '@/store/index'

const router = useRouter()
const code = ref('')
const state = ref('')
const loading = ref(true)


onMounted(async () => {
  const query = getQueryObject(window.location.href)
  code.value = query.code
  state.value = query.state
  if (!code.value) {
    ElMessage.error('获取企业微信授权码失败')
    loading.value = false
    setTimeout(() => {
      router.push('/login') // 跳转到普通登录页
    }, 2000)
    return
  }

  try {
    console.log('企业微信授权码:', code.value)
    const res: any = await store
    .dispatch('getSSonWxToken', { code:code.value ,state:state.value.split('#')[0]})
    if (res.code === 200) {
      // 跳转到首页
      router.push('/')
    } else {
      // ElMessage.error('登录失败，请重试')
      router.push('/login')
    }
  } catch (error) {
    ElMessage.error('登录请求失败')
    router.push('/login')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.wx-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.login-card {
  width: 400px;
  text-align: center;
  padding: 30px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-icon {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.tip-text {
  font-size: 14px;
  color: #999;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
