// const VITE_APP_BASE_API = '/dev-api'
// export default [
//   {
//     url: `${VITE_APP_BASE_API}/intelligence/hostCustomer/commVehicle/salesTrend`,
//     method: 'post',
//     response: {
//       'msg': '操作成功',
//       'code': 200,
//       'data': {
//         'salesLineList': [
//           {
//             'propChange': '20.0',
//             'total': '1241.0',
//             'month': '1',
//             'year': '2024'
//           },
//           {
//             'propChange': '10.0',
//             'total': '1980.0',
//             'month': '2',
//             'year': '2024'
//           },
//           {
//             'propChange': '30.0',
//             'total': '3968.0',
//             'month': '3',
//             'year': '2024'
//           }
//         ],
//         'salesTrendList': [
//           {
//             'propChange': 0.0,
//             'total': 1241.0,
//             'month': '1',
//             'year': '2024',
//             'prop': 8.94,
//             'breed': '专用车',
//             'sales': 111.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1241.0,
//             'month': '1',
//             'year': '2024',
//             'prop': 34.81,
//             'breed': '牵引车',
//             'sales': 432.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1241.0,
//             'month': '1',
//             'year': '2024',
//             'prop': 9.75,
//             'breed': '自卸车',
//             'sales': 121.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1241.0,
//             'month': '1',
//             'year': '2024',
//             'prop': 46.49,
//             'breed': '载货车',
//             'sales': 577.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1980.0,
//             'month': '2',
//             'year': '2024',
//             'prop': 8.13,
//             'breed': '专用车',
//             'sales': 161.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1980.0,
//             'month': '2',
//             'year': '2024',
//             'prop': 39.7,
//             'breed': '牵引车',
//             'sales': 786.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1980.0,
//             'month': '2',
//             'year': '2024',
//             'prop': 10.15,
//             'breed': '自卸车',
//             'sales': 201.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 1980.0,
//             'month': '2',
//             'year': '2024',
//             'prop': 42.02,
//             'breed': '载货车',
//             'sales': 832.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 3968.0,
//             'month': '3',
//             'year': '2024',
//             'prop': 6.83,
//             'breed': '专用车',
//             'sales': 271.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 3968.0,
//             'month': '3',
//             'year': '2024',
//             'prop': 42.67,
//             'breed': '牵引车',
//             'sales': 1693.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 3968.0,
//             'month': '3',
//             'year': '2024',
//             'prop': 11.11,
//             'breed': '自卸车',
//             'sales': 441.0
//           },
//           {
//             'propChange': 0.0,
//             'total': 3968.0,
//             'month': '3',
//             'year': '2024',
//             'prop': 39.39,
//             'breed': '载货车',
//             'sales': 1563.0
//           }
//         ],
//         'salesYearList': [
//           { total: 17537, year: '2026', prop: 46.59, breed: '牵引车', sales: 8170 },
//           { total: 17537, year: '2026', prop: 10.34, breed: '自卸车', sales: 1813 },
//           { total: 17537, year: '2026', prop: 5.5, breed: '专用车', sales: 964 },
//           { total: 17537, year: '2026', prop: 37.57, breed: '载货车', sales: 6589 },
//           { total: 17537, year: '2026', prop: 0.01, breed: '其他', sales: 1 },
//           { total: 17537, year: '2025', prop: 46.59, breed: '牵引车', sales: 8170 },
//           { total: 17537, year: '2025', prop: 10.34, breed: '自卸车', sales: 1813 },
//           { total: 17537, year: '2025', prop: 5.5, breed: '专用车', sales: 964 },
//           { total: 17537, year: '2025', prop: 37.57, breed: '载货车', sales: 6589 },
//           { total: 17537, year: '2025', prop: 0.01, breed: '其他', sales: 1 },
//           { total: 17537, year: '2024', prop: 46.59, breed: '牵引车', sales: 8170 },
//           { total: 17537, year: '2024', prop: 10.34, breed: '自卸车', sales: 1813 },
//           { total: 17537, year: '2024', prop: 5.5, breed: '专用车', sales: 964 },
//           { total: 17537, year: '2024', prop: 37.57, breed: '载货车', sales: 6589 },
//           { total: 17537, year: '2024', prop: 0.01, breed: '其他', sales: 1 },
//           { total: 17537, year: '2023', prop: 46.59, breed: '牵引车', sales: 8170 },
//           { total: 17537, year: '2023', prop: 10.34, breed: '自卸车', sales: 1813 },
//           { total: 17537, year: '2023', prop: 5.5, breed: '专用车', sales: 964 },
//           { total: 17537, year: '2023', prop: 37.57, breed: '载货车', sales: 6589 },
//           { total: 17537, year: '2023', prop: 0.01, breed: '其他', sales: 1 },
//           { total: 17537, year: '2022', prop: 46.59, breed: '牵引车', sales: 8170 },
//           { total: 17537, year: '2022', prop: 10.34, breed: '自卸车', sales: 1813 },
//           { total: 17537, year: '2022', prop: 5.5, breed: '专用车', sales: 964 },
//           { total: 17537, year: '2022', prop: 37.57, breed: '载货车', sales: 6589 },
//           { total: 17537, year: '2022', prop: 0.01, breed: '其他', sales: 1 }
//         ]
//       }
//     }
//   },
//   {
//     url: `${VITE_APP_BASE_API}/intelligence/hostCustomer/commVehicle/fuelTrend`,
//     method: 'post',
//     response: {
//       'msg': '操作成功',
//       'code': 200,
//       'data': {
//         'fuelTrendList': [
//           {
//             'total': 1241.0,
//             'month': '1',
//             'fuelType': '柴油',
//             'year': '2024',
//             'prop': 82.35,
//             'sales': 1022.0
//           },
//           {
//             'total': 1241.0,
//             'month': '1',
//             'fuelType': '气体',
//             'year': '2024',
//             'prop': 15.95,
//             'sales': 198.0
//           },
//           {
//             'total': 1241.0,
//             'month': '1',
//             'fuelType': '其他',
//             'year': '2024',
//             'prop': 1.69,
//             'sales': 21.0
//           },
//           {
//             'total': 739.0,
//             'month': '2',
//             'fuelType': '柴油',
//             'year': '2024',
//             'prop': 66.17,
//             'sales': 489.0
//           },
//           {
//             'total': 739.0,
//             'month': '2',
//             'fuelType': '气体',
//             'year': '2024',
//             'prop': 30.72,
//             'sales': 227.0
//           },
//           {
//             'total': 739.0,
//             'month': '2',
//             'fuelType': '其他',
//             'year': '2024',
//             'prop': 3.11,
//             'sales': 23.0
//           },
//           {
//             'total': 1988.0,
//             'month': '3',
//             'fuelType': '柴油',
//             'year': '2024',
//             'prop': 65.39,
//             'sales': 1300.0
//           },
//           {
//             'total': 1988.0,
//             'month': '3',
//             'fuelType': '气体',
//             'year': '2024',
//             'prop': 30.99,
//             'sales': 616.0
//           },
//           {
//             'total': 1988.0,
//             'month': '3',
//             'fuelType': '其他',
//             'year': '2024',
//             'prop': 3.62,
//             'sales': 72.0
//           },
//           {
//             'total': 2285.0,
//             'month': '4',
//             'fuelType': '气体',
//             'year': '2024',
//             'prop': 27.09,
//             'sales': 619.0
//           },
//           {
//             'total': 2285.0,
//             'month': '4',
//             'fuelType': '柴油',
//             'year': '2024',
//             'prop': 71.86,
//             'sales': 1642.0
//           },
//           {
//             'total': 2285.0,
//             'month': '4',
//             'fuelType': '其他',
//             'year': '2024',
//             'prop': 1.05,
//             'sales': 24.0
//           }
//         ],
//         'fuelYearList': [
//           {
//             'total': 17537.0,
//             'fuelType': '气体',
//             'year': '2024',
//             'prop': 23.21,
//             'sales': 4071.0
//           },
//           {
//             'total': 17537.0,
//             'fuelType': '柴油',
//             'year': '2024',
//             'prop': 69.03,
//             'sales': 12105.0
//           },
//           {
//             'total': 17537.0,
//             'fuelType': '其他',
//             'year': '2024',
//             'prop': 7.76,
//             'sales': 1361.0
//           }
//         ]
//       }
//     }
//   },
//   {
//     url: `${VITE_APP_BASE_API}/intelligence/hostCustomer/commVehicle/matchTrend`,
//     method: 'post',
//     response: {
//       'msg': '操作成功',
//       'code': 200,
//       'data': {
//         'matchTrendList': [
//           {
//             'total': 3968.0,
//             'month': '1',
//             'top': 1,
//             'year': '2024',
//             'engine': '玉柴',
//             'prop': 61.72,
//             'sales': 2449.0
//           },
//           {
//             'total': 3968.0,
//             'month': '1',
//             'top': 2,
//             'year': '2024',
//             'engine': '潍柴',
//             'prop': 23.31,
//             'sales': 925.0
//           },
//           {
//             'total': 3968.0,
//             'month': '1',
//             'top': 3,
//             'year': '2024',
//             'engine': '康明斯',
//             'prop': 12.05,
//             'sales': 478.0
//           },
//           {
//             'total': 3968.0,
//             'month': '1',
//             'top': 6,
//             'year': '2024',
//             'engine': '其他',
//             'prop': 2.92,
//             'sales': 116.0
//           },
//           {
//             'total': 6156.0,
//             'month': '2',
//             'top': 1,
//             'year': '2024',
//             'engine': '玉柴',
//             'prop': 55.3,
//             'sales': 3404.0
//           },
//           {
//             'total': 6156.0,
//             'month': '2',
//             'top': 2,
//             'year': '2024',
//             'engine': '潍柴',
//             'prop': 21.59,
//             'sales': 1329.0
//           },
//           {
//             'total': 6156.0,
//             'month': '2',
//             'top': 3,
//             'year': '2024',
//             'engine': '康明斯',
//             'prop': 17.46,
//             'sales': 1075.0
//           },
//           {
//             'total': 6156.0,
//             'month': '2',
//             'top': 6,
//             'year': '2024',
//             'engine': '其他',
//             'prop': 5.65,
//             'sales': 348.0
//           }
//         ],
//         'matchLineList': [
//           {
//             'month': '1',
//             'year': '2024',
//             'engine': '玉柴',
//             'prop': '61.72',
//             'sales': '2449.0'
//           },
//           {
//             'month': '1',
//             'year': '2024',
//             'engine': '潍柴',
//             'prop': '23.31',
//             'sales': '925.0'
//           },
//           {
//             'month': '1',
//             'year': '2024',
//             'engine': '康明斯',
//             'prop': '12.05',
//             'sales': '478.0'
//           },
//           {
//             'month': '1',
//             'year': '2024',
//             'engine': '其他',
//             'prop': '2.92',
//             'sales': '116.0'
//           },
//           {
//             'month': '2',
//             'year': '2024',
//             'engine': '玉柴',
//             'prop': '55.3',
//             'sales': '3404.0'
//           },
//           {
//             'month': '2',
//             'year': '2024',
//             'engine': '潍柴',
//             'prop': '21.59',
//             'sales': '1329.0'
//           },
//           {
//             'month': '2',
//             'year': '2024',
//             'engine': '康明斯',
//             'prop': '17.46',
//             'sales': '1075.0'
//           },
//           {
//             'month': '2',
//             'year': '2024',
//             'engine': '其他',
//             'prop': '5.65',
//             'sales': '348.0'
//           }
//         ],
//         'matchYearList': [
//           {
//             'total': 17537.0,
//             'top': 1,
//             'year': '2024',
//             'engine': '玉柴',
//             'prop': 55.69,
//             'sales': 9766.0
//           },
//           {
//             'total': 17537.0,
//             'top': 2,
//             'year': '2024',
//             'engine': '潍柴',
//             'prop': 19.29,
//             'sales': 3383.0
//           },
//           {
//             'total': 17537.0,
//             'top': 3,
//             'year': '2024',
//             'engine': '康明斯',
//             'prop': 17.26,
//             'sales': 3027.0
//           },
//           {
//             'total': 17537.0,
//             'top': 6,
//             'year': '2024',
//             'engine': '其他',
//             'prop': 7.76,
//             'sales': 1361.0
//           }
//         ]
//       }
//     }
//   },
//   {
//     url: `${VITE_APP_BASE_API}/intelligence/hostCustomer/commVehicle/areaSales`,
//     method: 'post',
//     response: {
//       'msg': '操作成功',
//       'code': 200,
//       'data': {
//         'areaLineList': [
//           {
//             'yc_sales': 5.0,
//             'province': '宁夏',
//             'year': '2024',
//             'prop': 38.46,
//             'sales': 13.0
//           },
//           {
//             'yc_sales': 94.0,
//             'province': '浙江',
//             'year': '2024',
//             'prop': 34.31,
//             'sales': 274.0
//           },
//           {
//             'yc_sales': 137.0,
//             'province': '湖南',
//             'year': '2024',
//             'prop': 79.19,
//             'sales': 173.0
//           },
//           {
//             'yc_sales': 176.0,
//             'province': '四川',
//             'year': '2024',
//             'prop': 83.41,
//             'sales': 211.0
//           },
//           {
//             'yc_sales': 90.0,
//             'province': '江苏',
//             'year': '2024',
//             'prop': 24.59,
//             'sales': 366.0
//           },
//           {
//             'yc_sales': 279.0,
//             'province': '河北',
//             'year': '2024',
//             'prop': 34.49,
//             'sales': 809.0
//           },
//           {
//             'yc_sales': 803.0,
//             'province': '广西',
//             'year': '2024',
//             'prop': 84.0,
//             'sales': 956.0
//           },
//           {
//             'yc_sales': 154.0,
//             'province': '江西',
//             'year': '2024',
//             'prop': 73.68,
//             'sales': 209.0
//           },
//           {
//             'yc_sales': 658.0,
//             'province': '广东',
//             'year': '2024',
//             'prop': 66.4,
//             'sales': 991.0
//           },
//           {
//             'yc_sales': 61.0,
//             'province': '福建',
//             'year': '2024',
//             'prop': 68.54,
//             'sales': 89.0
//           },
//           {
//             'yc_sales': 162.0,
//             'province': '贵州',
//             'year': '2024',
//             'prop': 79.41,
//             'sales': 204.0
//           },
//           {
//             'yc_sales': 64.0,
//             'province': '山西',
//             'year': '2024',
//             'prop': 28.32,
//             'sales': 226.0
//           },
//           {
//             'yc_sales': 96.0,
//             'province': '湖北',
//             'year': '2024',
//             'prop': 72.73,
//             'sales': 132.0
//           },
//           {
//             'yc_sales': 151.0,
//             'province': '山东',
//             'year': '2024',
//             'prop': 31.26,
//             'sales': 483.0
//           },
//           {
//             'yc_sales': 91.0,
//             'province': '河南',
//             'year': '2024',
//             'prop': 36.55,
//             'sales': 249.0
//           },
//           {
//             'yc_sales': 6.0,
//             'province': '',
//             'year': '2024',
//             'prop': 28.57,
//             'sales': 21.0
//           },
//           {
//             'yc_sales': 18.0,
//             'province': '陕西',
//             'year': '2024',
//             'prop': 62.07,
//             'sales': 29.0
//           },
//           {
//             'yc_sales': 51.0,
//             'province': '天津',
//             'year': '2024',
//             'prop': 62.2,
//             'sales': 82.0
//           },
//           {
//             'yc_sales': 25.0,
//             'province': '上海',
//             'year': '2024',
//             'prop': 18.25,
//             'sales': 137.0
//           },
//           {
//             'yc_sales': 163.0,
//             'province': '云南',
//             'year': '2024',
//             'prop': 67.08,
//             'sales': 243.0
//           },
//           {
//             'yc_sales': 35.0,
//             'province': '海南',
//             'year': '2024',
//             'prop': 89.74,
//             'sales': 39.0
//           },
//           {
//             'yc_sales': 10.0,
//             'province': '北京',
//             'year': '2024',
//             'prop': 47.62,
//             'sales': 21.0
//           },
//           {
//             'yc_sales': 9.0,
//             'province': '内蒙',
//             'year': '2024',
//             'prop': 16.67,
//             'sales': 54.0
//           },
//           {
//             'yc_sales': 18.0,
//             'province': '安徽',
//             'year': '2024',
//             'prop': 31.58,
//             'sales': 57.0
//           },
//           {
//             'yc_sales': 10.0,
//             'province': '辽宁',
//             'year': '2024',
//             'prop': 35.71,
//             'sales': 28.0
//           },
//           {
//             'yc_sales': 4.0,
//             'province': '青海',
//             'year': '2024',
//             'prop': 100.0,
//             'sales': 4.0
//           },
//           {
//             'yc_sales': 10.0,
//             'province': '重庆',
//             'year': '2024',
//             'prop': 76.92,
//             'sales': 13.0
//           },
//           {
//             'yc_sales': 4.0,
//             'province': '甘肃',
//             'year': '2024',
//             'prop': 80.0,
//             'sales': 5.0
//           },
//           {
//             'yc_sales': 18.0,
//             'province': '新疆',
//             'year': '2024',
//             'prop': 52.94,
//             'sales': 34.0
//           },
//           {
//             'yc_sales': 1.0,
//             'province': '西藏',
//             'year': '2024',
//             'prop': 100.0,
//             'sales': 1.0
//           },
//           {
//             'yc_sales': 1.0,
//             'province': '黑龙江',
//             'year': '2024',
//             'prop': 33.33,
//             'sales': 3.0
//           }
//         ],
//         'areaTrendList': [
//           {
//             'total': 991.0,
//             'province': '广东',
//             'top': 1,
//             'year': '2024',
//             'sales': 991.0
//           },
//           {
//             'total': 991.0,
//             'province': '广东',
//             'top': 1,
//             'year': '2023',
//             'sales': 122
//           },
//           {
//             'total': 956.0,
//             'province': '广西',
//             'top': 2,
//             'year': '2024',
//             'sales': 956.0
//           },
//           {
//             'total': 956.0,
//             'province': '广西',
//             'top': 2,
//             'year': '2023',
//             'sales': 556.0
//           },
//           {
//             'total': 809.0,
//             'province': '河北',
//             'top': 3,
//             'year': '2024',
//             'sales': 809.0
//           },
//           {
//             'total': 483.0,
//             'province': '山东',
//             'top': 4,
//             'year': '2024',
//             'sales': 483.0
//           },
//           {
//             'total': 366.0,
//             'province': '江苏',
//             'top': 5,
//             'year': '2024',
//             'sales': 366.0
//           },
//           {
//             'total': 274.0,
//             'province': '浙江',
//             'top': 6,
//             'year': '2024',
//             'sales': 274.0
//           },
//           {
//             'total': 249.0,
//             'province': '河南',
//             'top': 7,
//             'year': '2024',
//             'sales': 249.0
//           },
//           {
//             'total': 243.0,
//             'province': '云南',
//             'top': 8,
//             'year': '2024',
//             'sales': 243.0
//           },
//           {
//             'total': 226.0,
//             'province': '山西',
//             'top': 9,
//             'year': '2024',
//             'sales': 226.0
//           },
//           {
//             'total': 211.0,
//             'province': '四川',
//             'top': 10,
//             'year': '2024',
//             'sales': 211.0
//           },
//           {
//             'total': 209.0,
//             'province': '江西',
//             'top': 11,
//             'year': '2024',
//             'sales': 209.0
//           },
//           {
//             'total': 204.0,
//             'province': '贵州',
//             'top': 12,
//             'year': '2024',
//             'sales': 204.0
//           },
//           {
//             'total': 173.0,
//             'province': '湖南',
//             'top': 13,
//             'year': '2024',
//             'sales': 173.0
//           },
//           {
//             'total': 137.0,
//             'province': '上海',
//             'top': 14,
//             'year': '2024',
//             'sales': 137.0
//           },
//           {
//             'total': 132.0,
//             'province': '湖北',
//             'top': 15,
//             'year': '2024',
//             'sales': 132.0
//           },
//           {
//             'total': 89.0,
//             'province': '福建',
//             'top': 16,
//             'year': '2024',
//             'sales': 89.0
//           },
//           {
//             'total': 82.0,
//             'province': '天津',
//             'top': 17,
//             'year': '2024',
//             'sales': 82.0
//           },
//           {
//             'total': 57.0,
//             'province': '安徽',
//             'top': 18,
//             'year': '2024',
//             'sales': 57.0
//           },
//           {
//             'total': 54.0,
//             'province': '内蒙',
//             'top': 19,
//             'year': '2024',
//             'sales': 54.0
//           },
//           {
//             'total': 39.0,
//             'province': '海南',
//             'top': 20,
//             'year': '2024',
//             'sales': 39.0
//           },
//           {
//             'total': 34.0,
//             'province': '新疆',
//             'top': 21,
//             'year': '2024',
//             'sales': 34.0
//           },
//           {
//             'total': 29.0,
//             'province': '陕西',
//             'top': 22,
//             'year': '2024',
//             'sales': 29.0
//           },
//           {
//             'total': 28.0,
//             'province': '辽宁',
//             'top': 23,
//             'year': '2024',
//             'sales': 28.0
//           },
//           {
//             'total': 21.0,
//             'province': '',
//             'top': 24,
//             'year': '2024',
//             'sales': 21.0
//           },
//           {
//             'total': 21.0,
//             'province': '北京',
//             'top': 24,
//             'year': '2024',
//             'sales': 21.0
//           },
//           {
//             'total': 13.0,
//             'province': '宁夏',
//             'top': 26,
//             'year': '2024',
//             'sales': 13.0
//           },
//           {
//             'total': 13.0,
//             'province': '重庆',
//             'top': 26,
//             'year': '2024',
//             'sales': 13.0
//           },
//           {
//             'total': 5.0,
//             'province': '甘肃',
//             'top': 28,
//             'year': '2024',
//             'sales': 5.0
//           },
//           {
//             'total': 4.0,
//             'province': '青海',
//             'top': 29,
//             'year': '2024',
//             'sales': 4.0
//           },
//           {
//             'total': 3.0,
//             'province': '黑龙江',
//             'top': 30,
//             'year': '2024',
//             'sales': 3.0
//           },
//           {
//             'total': 1.0,
//             'province': '西藏',
//             'top': 31,
//             'year': '2024',
//             'sales': 1.0
//           }
//         ]
//       }
//     }
//   }
//   // 其他mock规则...
// ]
