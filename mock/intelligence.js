// 宏观环境数据
const PREFIX = '/dev-api/intelligence'
export default [
  {
    url: `${PREFIX}/macroData/homePageMacroDataList`,
    method: 'POST',
    response: {
      msg: '操作成功',
      code: 200,
      data: {
        discount: {
          //折线图类型数据
          LNG与柴油价格比走势: [
            {
              name: 'LNG与柴油价格比走势',
              macroType: 'LNG/0#柴油',
              data: []
            }
          ],
          物流类指数月度走势: [
            {
              name: '物流类指数月度走势',
              macroType: '物流业景气指数',
              data: [
                {
                  number: '1',
                  yearMonths: "24'10"
                },
                {
                  number: '2',
                  yearMonths: "24'11"
                }
              ]
            }
          ],
          固定资产分行业投资累计增速: [
            {
              name: '固定资产分行业投资累计增速',
              macroType: '制造业固定资产投资',
              data: [
                {
                  number: '2',
                  yearMonths: "24'04"
                },
                {
                  number: '3.6',
                  yearMonths: "24'05"
                },
                {
                  number: '1.5',
                  yearMonths: "24'06"
                },
                {
                  number: '2.7',
                  yearMonths: "24'07"
                }
              ]
            },
            {
              name: '固定资产分行业投资累计增速',
              macroType: '基础设施投资',
              data: [
                {
                  number: '18',
                  yearMonths: "24'04"
                },
                {
                  number: '1',
                  yearMonths: "24'05"
                },
                {
                  number: '7',
                  yearMonths: "24'07"
                }
              ]
            },
            {
              name: '固定资产分行业投资累计增速',
              macroType: '房地产开发投资',
              data: []
            },
            {
              name: '固定资产分行业投资累计增速',
              macroType: '采矿业',
              data: []
            }
          ],
          '公路货运量月度走势(亿吨)': [
            {
              name: '公路货运量月度走势(亿吨)',
              macroType: '物流运价指数',
              data: []
            },
            {
              name: '公路货运量月度走势(亿吨)',
              macroType: '公路货运量（亿吨）',
              data: []
            }
          ],
          进出口市场累计增速: [
            {
              name: '进出口市场累计增速',
              macroType: '进口',
              data: []
            },
            {
              name: '进出口市场累计增速',
              macroType: '出口',
              data: []
            }
          ],
          消费市场累计增速: [
            {
              name: '消费市场累计增速',
              macroType: '消费品零售总额',
              data: []
            },
            {
              name: '消费市场累计增速',
              macroType: '商品零售总额',
              data: []
            }
          ]
        },
        card: [
          //指标卡图类型数据
          {
            number: '12',
            yearMonths: "24'3",
            macroType: '24年前3季度GDP累计增速'
          },
          {
            number: '14',
            yearMonths: "24'08",
            macroType: '24年前08月居民消费价格累计增速'
          }
        ]
      }
    }
  },
  {
    url: `${PREFIX}/homePage/permeability`,
    method: 'POST',
    response: ({ query }) => {
      const response = {
        msg: '操作成功',
        code: 200,
        data: {}
      }
      if (query.pointerType === '1') {
        // 月度
        response.data = {
          'trendList': [
            {
              'total': 102881.0,
              'month': '1',
              'year': '2024',
              'trend': '22.8%',
              'electricity': 23428.0,
              'unelectricity': 79453.0
            },
            {
              'total': 69822.0,
              'month': '2',
              'year': '2024',
              'trend': '21.6%',
              'electricity': 15088.0,
              'unelectricity': 54734.0
            }
          ],
          'fuelList': [
            {
              'fuelcell': 78909.0,
              'month': '1',
              'year': '2024',
              'mixture': 544.0,
              'electricity': 23428.0
            },
            {
              'fuelcell': 54331.0,
              'month': '2',
              'year': '2024',
              'mixture': 403.0,
              'electricity': 15088.0
            }
          ],
          'trendYearList': [
            {
              'total': 172703.0,
              'year': '2024',
              'year_trend': '22.3%',
              'electricity': 38516.0,
              'unelectricity': 134187.0
            }
          ],
          'fuelYearList': [
            {
              'fuelcell': 133240.0,
              'year': '2024',
              'mixture': 947.0,
              'electricity': 38516.0
            }
          ]
        }
      }
      response.data = {
        'trendList': [
          {
            'total': 102881.0,
            'month': '1',
            'year': '2024',
            'trend': '22.8%',
            'electricity': 23428.0,
            'unelectricity': 79453.0
          },
          {
            'total': 69822.0,
            'month': '2',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          },
          {
            'total': 69822.0,
            'month': '3',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          },
          {
            'total': 69822.0,
            'month': '4',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          },
          {
            'total': 69822.0,
            'month': '5',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          },
          {
            'total': 69822.0,
            'month': '6',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          },
          {
            'total': 69822.0,
            'month': '7',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          },
          {
            'total': 69822.0,
            'month': '8',
            'year': '2024',
            'trend': '21.6%',
            'electricity': 15088.0,
            'unelectricity': 54734.0
          }
        ],
        'fuelList': [
          {
            'fuelcell': 78909.0,
            'month': '1',
            'year': '2024',
            'mixture': 544.0,
            'electricity': 23428.0
          },
          {
            'fuelcell': 78909.0,
            'month': '2',
            'year': '2024',
            'mixture': 544.0,
            'electricity': 23428.0
          },
          {
            'fuelcell': 78909.0,
            'month': '3',
            'year': '2024',
            'mixture': 544.0,
            'electricity': 23428.0
          },
          {
            'fuelcell': 54331.0,
            'month': '4',
            'year': '2024',
            'mixture': 403.0,
            'electricity': 15088.0
          }
        ],
        'trendYearList': [
          {
            'total': 172703.0,
            'year': '2021',
            'year_trend': '22.3%',
            'electricity': 38516.0,
            'unelectricity': 134187.0
          },
          {
            'total': 172703.0,
            'year': '2022',
            'year_trend': '22.3%',
            'electricity': 38516.0,
            'unelectricity': 134187.0
          },
          {
            'total': 172703.0,
            'year': '2023',
            'year_trend': '22.3%',
            'electricity': 38516.0,
            'unelectricity': 134187.0
          },
          {
            'total': 172703.0,
            'year': '2024',
            'year_trend': '22.3%',
            'electricity': 38516.0,
            'unelectricity': 134187.0
          }
        ],
        'fuelYearList': [
          {
            'fuelcell': 133240.0,
            'year': '2021',
            'mixture': 947.0,
            'electricity': 38516.0
          },
          {
            'fuelcell': 133240.0,
            'year': '2022',
            'mixture': 947.0,
            'electricity': 38516.0
          },
          {
            'fuelcell': 133240.0,
            'year': '2023',
            'mixture': 947.0,
            'electricity': 38516.0
          },
          {
            'fuelcell': 133240.0,
            'year': '2024',
            'mixture': 947.0,
            'electricity': 38516.0
          }
        ]
      }
      return response
    }
  },
  {
    url: `${PREFIX}/homePage/oem`,
    method: 'POST',
    response: ({ query }) => {
      const response = {
        msg: '操作成功',
        code: 200,
        data: {}
      }
      response.data = {
        'patternYearList': [
          {
            'proportion': '16',
            'top': 1,
            'year': '2024',
            'manuFacturer': '北汽福田'
          },
          {
            'proportion': '2.1',
            'top': 10,
            'year': '2024',
            'manuFacturer': '吉利四川'
          }
        ],
        'patternMap': {
          '1': {
            'name': '1',
            'engineList': [
              {
                'month': '1',
                'proportion': '15.6',
                'top': 1,
                'year': '2024',
                'manuFacturer': '北汽福田'
              },
              {
                'month': '1',
                'proportion': '2.2',
                'top': 10,
                'year': '2024',
                'manuFacturer': '上汽大通'
              }
            ]
          }
        },
        'powerPatPropMap': {
          '1': {
            'engineList': [
              {
                'month': '1',
                'engineFactory': '全柴',
                'top': 1,
                'year': '2024',
                'percentage': '18.2',
                'manuSales': 16052.0,
                'manuFacturer': '北汽福田',
                'engineSales': 2924.0
              }
            ],
            'total': '2244.0',
            'name': '上汽大通'
          }
        },
        'fiveYearMap': {
          '2024': {
            'engineList': [
              {
                'proportion': '16',
                'top': 1,
                'year': '2024',
                'manuFacturer': '北汽福田'
              },
              {
                'proportion': '2.1',
                'top': 10,
                'year': '2024',
                'manuFacturer': '吉利四川'
              }
            ],
            'name': '2024'
          }
        }
      }
      return response
    }
  },
  
  {
    url: `${PREFIX}/test`,
    method: 'POST',
    response: ({ query }) => {
      const response = {
        msg: '操作成功',
        code: 200,
        data: {}
      } 
      return response
    }
  }
  // 其他mock规则...
]
