const VITE_APP_BASE_API = "/dev-api";
export default [
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_yes_no`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:23",
              "updateBy": null,
              "updateTime": null,
              "remark": "系统默认是",
              "dictCode": 12,
              "dictSort": 1,
              "dictLabel": "是",
              "dictValue": "Y",
              "dictType": "sys_yes_no",
              "cssClass": "",
              "listClass": "primary",
              "isDefault": "Y",
              "status": "0",
              "default": true
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:23",
              "updateBy": null,
              "updateTime": null,
              "remark": "系统默认否",
              "dictCode": 13,
              "dictSort": 2,
              "dictLabel": "否",
              "dictValue": "N",
              "dictType": "sys_yes_no",
              "cssClass": "",
              "listClass": "danger",
              "isDefault": "N",
              "status": "0",
              "default": false
          }
      ]
  },
  },
  {
    url: `${VITE_APP_BASE_API}/system/config/list`,
    method: "get",
    response: {
      "total": 6,
      "rows": [
          {
              "createBy": "",
              "createTime": "2024-06-30 11:28:30",
              "updateBy": "",
              "updateTime": null,
              "remark": "蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow",
              "configId": 1,
              "configName": "主框架页-默认皮肤样式名称",
              "configKey": "sys.index.skinName",
              "configValue": "skin-blue",
              "configType": "Y"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:30",
              "updateBy": "",
              "updateTime": null,
              "remark": "初始化密码 123456",
              "configId": 2,
              "configName": "用户管理-账号初始密码",
              "configKey": "sys.user.initPassword",
              "configValue": "123456",
              "configType": "Y"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:30",
              "updateBy": "",
              "updateTime": null,
              "remark": "深色主题theme-dark，浅色主题theme-light",
              "configId": 3,
              "configName": "主框架页-侧边栏主题",
              "configKey": "sys.index.sideTheme",
              "configValue": "theme-dark",
              "configType": "Y"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:30",
              "updateBy": "",
              "updateTime": null,
              "remark": "是否开启验证码功能（true开启，false关闭）",
              "configId": 4,
              "configName": "账号自助-验证码开关",
              "configKey": "sys.account.captchaEnabled",
              "configValue": "true",
              "configType": "Y"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:31",
              "updateBy": "",
              "updateTime": null,
              "remark": "是否开启注册用户功能（true开启，false关闭）",
              "configId": 5,
              "configName": "账号自助-是否开启用户注册功能",
              "configKey": "sys.account.registerUser",
              "configValue": "false",
              "configType": "Y"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:31",
              "updateBy": "",
              "updateTime": null,
              "remark": "设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）",
              "configId": 6,
              "configName": "用户登录-黑名单列表",
              "configKey": "sys.login.blackIPList",
              "configValue": "",
              "configType": "Y"
          }
      ],
      "code": 200,
      "msg": "查询成功"
    },
  }
  // 其他mock规则...
];

