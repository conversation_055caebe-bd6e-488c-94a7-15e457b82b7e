const VITE_APP_BASE_API = "/dev-api";
export default [
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_oper_type`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:24",
              "updateBy": null,
              "updateTime": null,
              "remark": "新增操作",
              "dictCode": 19,
              "dictSort": 1,
              "dictLabel": "新增",
              "dictValue": "1",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "info",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:25",
              "updateBy": null,
              "updateTime": null,
              "remark": "修改操作",
              "dictCode": 20,
              "dictSort": 2,
              "dictLabel": "修改",
              "dictValue": "2",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "info",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:25",
              "updateBy": null,
              "updateTime": null,
              "remark": "删除操作",
              "dictCode": 21,
              "dictSort": 3,
              "dictLabel": "删除",
              "dictValue": "3",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "danger",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:25",
              "updateBy": null,
              "updateTime": null,
              "remark": "授权操作",
              "dictCode": 22,
              "dictSort": 4,
              "dictLabel": "授权",
              "dictValue": "4",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "primary",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:25",
              "updateBy": null,
              "updateTime": null,
              "remark": "导出操作",
              "dictCode": 23,
              "dictSort": 5,
              "dictLabel": "导出",
              "dictValue": "5",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "warning",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:26",
              "updateBy": null,
              "updateTime": null,
              "remark": "导入操作",
              "dictCode": 24,
              "dictSort": 6,
              "dictLabel": "导入",
              "dictValue": "6",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "warning",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:26",
              "updateBy": null,
              "updateTime": null,
              "remark": "强退操作",
              "dictCode": 25,
              "dictSort": 7,
              "dictLabel": "强退",
              "dictValue": "7",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "danger",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:26",
              "updateBy": null,
              "updateTime": null,
              "remark": "生成操作",
              "dictCode": 26,
              "dictSort": 8,
              "dictLabel": "生成代码",
              "dictValue": "8",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "warning",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:26",
              "updateBy": null,
              "updateTime": null,
              "remark": "清空操作",
              "dictCode": 27,
              "dictSort": 9,
              "dictLabel": "清空数据",
              "dictValue": "9",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "danger",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:24",
              "updateBy": null,
              "updateTime": null,
              "remark": "其他操作",
              "dictCode": 18,
              "dictSort": 99,
              "dictLabel": "其他",
              "dictValue": "0",
              "dictType": "sys_oper_type",
              "cssClass": "",
              "listClass": "info",
              "isDefault": "N",
              "status": "0",
              "default": false
          }
        ]
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_common_status`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:27",
              "updateBy": null,
              "updateTime": null,
              "remark": "正常状态",
              "dictCode": 28,
              "dictSort": 1,
              "dictLabel": "成功",
              "dictValue": "0",
              "dictType": "sys_common_status",
              "cssClass": "",
              "listClass": "primary",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:27",
              "updateBy": null,
              "updateTime": null,
              "remark": "停用状态",
              "dictCode": 29,
              "dictSort": 2,
              "dictLabel": "失败",
              "dictValue": "1",
              "dictType": "sys_common_status",
              "cssClass": "",
              "listClass": "danger",
              "isDefault": "N",
              "status": "0",
              "default": false
          }
      ]
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/operlog/list`,
    method: "get",
    response: {
      "total": 11547,
      "rows": [
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11646,
              "title": "代码生成",
              "businessType": 8,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.batchGenCode()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/batchGenCode",
              "operIp": "************",
              "operLocation": "重庆市 重庆市",
              "operParam": "{\"tables\":\"sys_user\"}",
              "jsonResult": null,
              "status": 1,
              "errorMsg": "",
              "operTime": "2024-12-20 11:35:11",
              "costTime": 1
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11645,
              "title": "代码生成",
              "businessType": 3,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.remove()",
              "requestMethod": "DELETE",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/2838,2839,2840",
              "operIp": "*************",
              "operLocation": "广东省 深圳市",
              "operParam": "{}",
              "jsonResult": "{\"msg\":\"操作成功\",\"code\":200}",
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:25:03",
              "costTime": 6
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11644,
              "title": "代码生成",
              "businessType": 2,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.synchDb()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/synchDb/sys_oper_log",
              "operIp": "*************",
              "operLocation": "重庆市 重庆市",
              "operParam": "{}",
              "jsonResult": "{\"msg\":\"操作成功\",\"code\":200}",
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:22:31",
              "costTime": 22
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11643,
              "title": "代码生成",
              "businessType": 8,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.batchGenCode()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/batchGenCode",
              "operIp": "***********",
              "operLocation": "山东省 济南市",
              "operParam": "{\"tables\":\"sys_user\"}",
              "jsonResult": null,
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:15:03",
              "costTime": 22
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11642,
              "title": "代码生成",
              "businessType": 6,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.importTableSave()",
              "requestMethod": "POST",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/importTable",
              "operIp": "*************",
              "operLocation": "广东省 广州市",
              "operParam": "{\"tables\":\"sys_oper_log\"}",
              "jsonResult": "{\"msg\":\"操作成功\",\"code\":200}",
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:13:15",
              "costTime": 24
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11641,
              "title": "代码生成",
              "businessType": 2,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.synchDb()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/synchDb/sys_user",
              "operIp": "**************",
              "operLocation": "广东省 广州市",
              "operParam": "{}",
              "jsonResult": "{\"msg\":\"操作成功\",\"code\":200}",
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:04:53",
              "costTime": 24
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11640,
              "title": "代码生成",
              "businessType": 8,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.batchGenCode()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/batchGenCode",
              "operIp": "**************",
              "operLocation": "山东省 东营市",
              "operParam": "{\"tables\":\"sys_user\"}",
              "jsonResult": null,
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:03:29",
              "costTime": 22
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11639,
              "title": "代码生成",
              "businessType": 6,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.importTableSave()",
              "requestMethod": "POST",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/importTable",
              "operIp": "**************",
              "operLocation": "山东省 东营市",
              "operParam": "{\"tables\":\"sys_role_dept\"}",
              "jsonResult": "{\"msg\":\"操作成功\",\"code\":200}",
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:03:24",
              "costTime": 12
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11638,
              "title": "代码生成",
              "businessType": 8,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.batchGenCode()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/batchGenCode",
              "operIp": "**************",
              "operLocation": "广东省 广州市",
              "operParam": "{\"tables\":\"sys_user\"}",
              "jsonResult": null,
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:02:12",
              "costTime": 23
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "operId": 11637,
              "title": "代码生成",
              "businessType": 8,
              "businessTypes": null,
              "method": "com.ruoyi.project.tool.gen.controller.GenController.batchGenCode()",
              "requestMethod": "GET",
              "operatorType": 1,
              "operName": "admin",
              "deptName": "研发部门",
              "operUrl": "/tool/gen/batchGenCode",
              "operIp": "**************",
              "operLocation": "广东省 东莞市",
              "operParam": "{\"tables\":\"sys_user\"}",
              "jsonResult": null,
              "status": 0,
              "errorMsg": null,
              "operTime": "2024-12-20 11:01:29",
              "costTime": 24
          }
      ],
      "code": 200,
      "msg": "查询成功"
   },
  },
  {
    url: `${VITE_APP_BASE_API}/system/logininfor/list`,
    method: "get",
    response: {
      "total": 516614,
      "rows": [
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516713,
              "userName": "admin",
              "status": "0",
              "ipaddr": "***************",
              "loginLocation": "安徽省 宣城市",
              "browser": "Chrome 8",
              "os": "Windows 10",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:45:39"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516712,
              "userName": "admin",
              "status": "1",
              "ipaddr": "***************",
              "loginLocation": "安徽省 宣城市",
              "browser": "Chrome 8",
              "os": "Windows 10",
              "msg": "验证码错误",
              "loginTime": "2024-12-20 11:45:36"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516711,
              "userName": "admin",
              "status": "1",
              "ipaddr": "***************",
              "loginLocation": "安徽省 宣城市",
              "browser": "Chrome 8",
              "os": "Windows 10",
              "msg": "验证码已失效",
              "loginTime": "2024-12-20 11:45:28"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516710,
              "userName": "admin",
              "status": "0",
              "ipaddr": "**************",
              "loginLocation": "云南省 玉溪市",
              "browser": "Chrome 11",
              "os": "Mac OS X",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:45:25"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516709,
              "userName": "admin",
              "status": "0",
              "ipaddr": "**************",
              "loginLocation": "广东省 汕头市",
              "browser": "Chrome 12",
              "os": "Windows 10",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:45:21"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516708,
              "userName": "admin",
              "status": "0",
              "ipaddr": "**************",
              "loginLocation": "山东省 济南市",
              "browser": "Chrome 12",
              "os": "Windows 10",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:45:13"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516707,
              "userName": "admin",
              "status": "0",
              "ipaddr": "************",
              "loginLocation": "陕西省 西安市",
              "browser": "Chrome Mobile",
              "os": "Android 1.x",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:44:51"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516706,
              "userName": "admin",
              "status": "0",
              "ipaddr": "*************",
              "loginLocation": "吉林省 长春市",
              "browser": "Chrome Mobile",
              "os": "Android 1.x",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:44:09"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516705,
              "userName": "admin",
              "status": "0",
              "ipaddr": "*************",
              "loginLocation": "江苏省 苏州市",
              "browser": "Chrome 13",
              "os": "Windows 10",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:44:06"
          },
          {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "infoId": 516704,
              "userName": "admin",
              "status": "0",
              "ipaddr": "*************",
              "loginLocation": "北京市 北京市",
              "browser": "Chrome Mobile",
              "os": "Android 1.x",
              "msg": "登录成功",
              "loginTime": "2024-12-20 11:42:36"
          }
      ],
      "code": 200,
      "msg": "查询成功"
    },
  },
  // 其他mock规则...
];

