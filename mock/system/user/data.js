const VITE_APP_BASE_API = "/dev-api";
export default [
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_user_sex`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:20",
              "updateBy": null,
              "updateTime": null,
              "remark": "性别男",
              "dictCode": 1,
              "dictSort": 1,
              "dictLabel": "男",
              "dictValue": "0",
              "dictType": "sys_user_sex",
              "cssClass": "",
              "listClass": "",
              "isDefault": "Y",
              "status": "0",
              "default": true
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:20",
              "updateBy": null,
              "updateTime": null,
              "remark": "性别女",
              "dictCode": 2,
              "dictSort": 2,
              "dictLabel": "女",
              "dictValue": "1",
              "dictType": "sys_user_sex",
              "cssClass": "",
              "listClass": "",
              "isDefault": "N",
              "status": "0",
              "default": false
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:20",
              "updateBy": null,
              "updateTime": null,
              "remark": "性别未知",
              "dictCode": 3,
              "dictSort": 3,
              "dictLabel": "未知",
              "dictValue": "2",
              "dictType": "sys_user_sex",
              "cssClass": "",
              "listClass": "",
              "isDefault": "N",
              "status": "0",
              "default": false
          }
      ]
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/dept/treeselect`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "id": 100,
              "label": "若依科技",
              "children": [
                  {
                      "id": 101,
                      "label": "深圳总公司",
                      "children": [
                          {
                              "id": 103,
                              "label": "研发部门"
                          },
                          {
                              "id": 104,
                              "label": "市场部门"
                          },
                          {
                              "id": 105,
                              "label": "测试部门"
                          },
                          {
                              "id": 106,
                              "label": "财务部门"
                          },
                          {
                              "id": 107,
                              "label": "运维部门"
                          }
                      ]
                  },
                  {
                      "id": 102,
                      "label": "长沙分公司",
                      "children": [
                          {
                              "id": 108,
                              "label": "市场部门"
                          },
                          {
                              "id": 109,
                              "label": "财务部门"
                          }
                      ]
                  }
              ]
          }
      ]
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/user/list`,
    method: "get",
    response: {
      "total": 2,
      "rows": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:11",
              "updateBy": null,
              "updateTime": null,
              "remark": "管理员",
              "userId": 1,
              "deptId": 103,
              "userName": "admin",
              "nickName": "若依",
              "email": "<EMAIL>",
              "phonenumber": "15888888888",
              "sex": "1",
              "avatar": "",
              "password": null,
              "status": "0",
              "delFlag": "0",
              "loginIp": "***************",
              "loginDate": "2024-12-20T10:06:24.000+08:00",
              "dept": {
                  "createBy": null,
                  "createTime": null,
                  "updateBy": null,
                  "updateTime": null,
                  "remark": null,
                  "deptId": 103,
                  "parentId": null,
                  "ancestors": null,
                  "deptName": "研发部门",
                  "orderNum": null,
                  "leader": "若依",
                  "phone": null,
                  "email": null,
                  "status": null,
                  "delFlag": null,
                  "parentName": null,
                  "children": []
              },
              "roles": [],
              "roleIds": null,
              "postIds": null,
              "roleId": null,
              "admin": true
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:11",
              "updateBy": null,
              "updateTime": null,
              "remark": "测试员",
              "userId": 2,
              "deptId": 105,
              "userName": "ry",
              "nickName": "若依",
              "email": "<EMAIL>",
              "phonenumber": "15666666666",
              "sex": "1",
              "avatar": "",
              "password": null,
              "status": "0",
              "delFlag": "0",
              "loginIp": "**************",
              "loginDate": "2024-12-19T23:06:51.000+08:00",
              "dept": {
                  "createBy": null,
                  "createTime": null,
                  "updateBy": null,
                  "updateTime": null,
                  "remark": null,
                  "deptId": 105,
                  "parentId": null,
                  "ancestors": null,
                  "deptName": "测试部门",
                  "orderNum": null,
                  "leader": "若依",
                  "phone": null,
                  "email": null,
                  "status": null,
                  "delFlag": null,
                  "parentName": null,
                  "children": []
              },
              "roles": [],
              "roleIds": null,
              "postIds": null,
              "roleId": null,
              "admin": false
          }
      ],
      "code": 200,
      "msg": "查询成功"
    },
  },
  // 其他mock规则...
];

