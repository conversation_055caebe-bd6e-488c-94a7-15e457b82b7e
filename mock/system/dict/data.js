const VITE_APP_BASE_API = "/dev-api";
export default [
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/list`,
    method: "get",
    response: {
      code: 200,
      msg: null,
      data: {},
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/sys_show_hide`,
    method: "get",
    response: {
      total: 2,
      rows: [
        {
          createBy: "admin",
          createTime: "2024-11-04 15:33:58",
          updateBy: null,
          updateTime: null,
          remark: "显示菜单",
          dictCode: 4,
          dictSort: 1,
          dictLabel: "显示",
          dictValue: "0",
          dictType: "sys_show_hide",
          cssClass: "",
          listClass: "primary",
          isDefault: "Y",
          status: "0",
          default: true,
        },
        {
          createBy: "admin",
          createTime: "2024-11-04 15:33:58",
          updateBy: null,
          updateTime: null,
          remark: "隐藏菜单",
          dictCode: 5,
          dictSort: 2,
          dictLabel: "隐藏",
          dictValue: "1",
          dictType: "sys_show_hide",
          cssClass: "",
          listClass: "danger",
          isDefault: "N",
          status: "0",
          default: false,
        },
      ],
      code: 200,
      msg: "查询成功",
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_normal_disable`,
    method: "get",
    response: {
    "total": 2,
    "rows": [
        {
            "createBy": "admin",
            "createTime": "2024-11-04 15:33:58",
            "updateBy": null,
            "updateTime": null,
            "remark": "正常状态",
            "dictCode": 6,
            "dictSort": 1,
            "dictLabel": "正常",
            "dictValue": "0",
            "dictType": "sys_normal_disable",
            "cssClass": "",
            "listClass": "primary",
            "isDefault": "Y",
            "status": "0",
            "default": true
        },
        {
            "createBy": "admin",
            "createTime": "2024-11-04 15:33:58",
            "updateBy": null,
            "updateTime": null,
            "remark": "停用状态",
            "dictCode": 7,
            "dictSort": 2,
            "dictLabel": "停用",
            "dictValue": "1",
            "dictType": "sys_normal_disable",
            "cssClass": "",
            "listClass": "danger",
            "isDefault": "N",
            "status": "0",
            "default": false
        }
    ],
    "code": 200,
    "msg": "查询成功"
},
  },
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/cascade`,
    method: "get",
    response: {
    "data": {
      'dataSourceOptions':[
        {
          value: '0',
          label: '上险数',
          bankuaiOptions:[
            {
              value: '0',
              label: '商用车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '客车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '轻卡',
                    },{
                      value: '1',
                      label: '皮卡',
                    },{
                      value: '2',
                      label: '公路',
                    },{
                      value: '3',
                      label: '公交',
                    },{
                      value: '4',
                      label: '校车',
                    }
                  ]
                },{
                  value: '1',
                  label: '卡车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '牵引车',
                    },{
                      value: '1',
                      label: '中重载货',
                    },{
                      value: '2',
                      label: '中重自卸',
                    },{
                      value: '3',
                      label: '中重专用',
                    },{
                      value: '4',
                      label: '轻卡',
                    },{
                      value: '5',
                      label: '皮卡',
                    }
                  ]
                }
              ]
            }
          ]
        },{
          value: '1',
          label: '货运新增',
          bankuaiOptions:[
            {
              value: '0',
              label: '商用车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '客车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '轻卡',
                    },{
                      value: '1',
                      label: '皮卡',
                    },{
                      value: '2',
                      label: '公路',
                    },{
                      value: '3',
                      label: '公交',
                    },{
                      value: '4',
                      label: '校车',
                    }
                  ]
                },{
                  value: '1',
                  label: '卡车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '牵引车',
                    },{
                      value: '1',
                      label: '中重载货',
                    },{
                      value: '2',
                      label: '中重自卸',
                    },{
                      value: '3',
                      label: '中重专用',
                    },{
                      value: '4',
                      label: '轻卡',
                    },{
                      value: '5',
                      label: '皮卡',
                    }
                  ]
                }
              ]
            }
          ]
        },{
          value: '2',
          label: '装机数',
          bankuaiOptions:[
            {
              value: '0',
              label: '通机',
              shichangOptions1:[
                {
                  value: '0',
                  label: '工程机械',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '高空平台',
                    },{
                      value: '1',
                      label: '矿用车',
                    },{
                      value: '2',
                      label: '挖掘机',
                    },{
                      value: '3',
                      label: '装载机',
                    },{
                      value: '4',
                      label: '工程机（其他）',
                    }
                  ]
                },{
                  value: '1',
                  label: '工业动力',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '叉车',
                    },{
                      value: '1',
                      label: '空压机',
                    },{
                      value: '2',
                      label: '内燃叉车',
                    },{
                      value: '3',
                      label: '钻机',
                    }
                  ]
                },{
                  value: '2',
                  label: '农业机械',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '花生机',
                    },{
                      value: '1',
                      label: '农机（其他）',
                    },{
                      value: '2',
                      label: '水稻机',
                    },{
                      value: '3',
                      label: '拖拉机',
                    },{
                      value: '4',
                      label: '小麦机',
                    },{
                      value: '5',
                      label: '玉米机',
                    }
                  ]
                }
              ]
            }
          ]
        },{
          value: '3',
          label: '海关数',
          bankuaiOptions:[
            {
              value: '0',
              label: '卡车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '乘用车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '乘用车',
                    },{
                      value: '1',
                      label: '皮卡',
                    },{
                      value: '2',
                      label: '轻卡',
                    }
                  ]
                },{
                  value: '1',
                  label: '纯电卡车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '纯电卡车',
                    }
                  ]
                },{
                  value: '2',
                  label: '皮卡',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '乘用车',
                    },{
                      value: '1',
                      label: '皮卡',
                    }
                  ]
                },{
                  value: '3',
                  label: '轻卡',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '轻卡',
                    }
                  ]
                },{
                  value: '4',
                  label: '中卡',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '中卡',
                    }
                  ]
                },{
                  value: '5',
                  label: '重卡',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '重卡',
                    }
                  ]
                }
              ]
            },
            {
              value: '1',
              label: '客车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '乘用车',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '乘用车',
                    }
                  ]
                },{
                  value: '1',
                  label: '大客',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '大客',
                    }
                  ]
                },{
                  value: '2',
                  label: '轻客',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '轻客',
                    }
                  ]
                },{
                  value: '3',
                  label: '中客',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '中客',
                    }
                  ]
                }
              ]
            },{
              value: '2',
              label: '船电',
              shichangOptions1:[
                {
                  value: '0',
                  label: '船机',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '船机',
                    }
                  ]
                },{
                  value: '1',
                  label: '发电单机',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '发电单机',
                    }
                  ]
                },{
                  value: '2',
                  label: '发电机组',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '发电机组',
                    }
                  ]
                }
              ]
            },{
              value: '3',
              label: '通机',
              shichangOptions1:[
                {
                  value: '0',
                  label: '工业机械',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '挖掘机',
                    },{
                      value: '1',
                      label: '装载机',
                    },{
                      value: '2',
                      label: '筑路机机平地机',
                    }
                  ]
                },{
                  value: '1',
                  label: '工业动力',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '叉车',
                    }
                  ]
                },{
                  value: '2',
                  label: '农业机械',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '收割机',
                    },{
                      value: '1',
                      label: '拖拉机',
                    }
                  ]
                }
              ]
            }
          ]
        },{
          value: '4',
          label: '船电数',
          bankuaiOptions:[
            {
              value: '0',
              label: '船电',
              shichangOptions1:[
                {
                  value: '0',
                  label: '船机',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '玉柴',
                    },{
                      value: '1',
                      label: '潍柴',
                    },{
                      value: '2',
                      label: '上柴',
                    },{
                      value: '3',
                      label: '东康',
                    },{
                      value: '4',
                      label: '重康',
                    }
                  ]
                },{
                  value: '1',
                  label: '单机',
                  shichangOptions2:[
                    {
                      value: '0',
                      label: '玉柴',
                    },{
                      value: '1',
                      label: '潍柴',
                    },{
                      value: '2',
                      label: '上柴',
                    },{
                      value: '3',
                      label: '东康',
                    },{
                      value: '4',
                      label: '重康',
                    },{
                      value: '5',
                      label: 'MTU',
                    }
                  ]
                }
              ]
            }
          ]
        },{
          value: '5',
          label: '中内协',
          bankuaiOptions:[
            {
              value: '0',
              label: '商用车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '商用车',
                  shichangOptions2:[]
                }
              ]
            },{
              value: '1',
              label: '通机',
              shichangOptions1:[
                {
                  value: '0',
                  label: '工程机',
                  shichangOptions2:[]
                }
              ]
            },{
              value: '2',
              label: '船电',
              shichangOptions1:[
                {
                  value: '0',
                  label: '农机',
                  shichangOptions2:[]
                },{
                  value: '1',
                  label: '通机（其他）',
                  shichangOptions2:[]
                },{
                  value: '2',
                  label: '船机',
                  shichangOptions2:[]
                },{
                  value: '3',
                  label: '发电',
                  shichangOptions2:[]
                }
              ]
            },{
              value: '3',
              label: '乘用车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '乘用车',
                  shichangOptions2:[]
                }
              ]
            }
          ]
        },{
          value: '6',
          label: '友商数',
          bankuaiOptions:[
            {
              value: '0',
              label: '商用车',
              shichangOptions1:[
                {
                  value: '0',
                  label: '卡车',
                  shichangOptions2:[]
                },
                {
                  value: '1',
                  label: '客车',
                  shichangOptions2:[]
                }
              ]
            },{
              value: '1',
              label: '通机',
              shichangOptions1:[
                {
                  value: '0',
                  label: '工程机',
                  shichangOptions2:[]
                },{
                  value: '1',
                  label: '农机',
                  shichangOptions2:[]
                }
              ]
            },{
              value: '2',
              label: '船电',
              shichangOptions1:[
                {
                  value: '0',
                  label: '船机',
                  shichangOptions2:[]
                },{
                  value: '1',
                  label: '发电',
                  shichangOptions2:[]
                }
              ]
            },{
              value: '3',
              label: '基础机',
              shichangOptions1:[
                {
                  value: '0',
                  label: '基础机',
                  shichangOptions2:[]
                }
              ]
            }
          ]
        }
      ]
    },
    "code": 200,
    "msg": "查询成功"
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/dict/type/list`,
    method: "get",
    response: {
      "total": 10,
      "rows": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:15",
              "updateBy": null,
              "updateTime": null,
              "remark": "用户性别列表",
              "dictId": 1,
              "dictName": "用户性别",
              "dictType": "sys_user_sex",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:15",
              "updateBy": null,
              "updateTime": null,
              "remark": "菜单状态列表",
              "dictId": 2,
              "dictName": "菜单状态",
              "dictType": "sys_show_hide",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:15",
              "updateBy": null,
              "updateTime": null,
              "remark": "系统开关列表",
              "dictId": 3,
              "dictName": "系统开关",
              "dictType": "sys_normal_disable",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:16",
              "updateBy": null,
              "updateTime": null,
              "remark": "任务状态列表",
              "dictId": 4,
              "dictName": "任务状态",
              "dictType": "sys_job_status",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:16",
              "updateBy": null,
              "updateTime": null,
              "remark": "任务分组列表",
              "dictId": 5,
              "dictName": "任务分组",
              "dictType": "sys_job_group",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:16",
              "updateBy": null,
              "updateTime": null,
              "remark": "系统是否列表",
              "dictId": 6,
              "dictName": "系统是否",
              "dictType": "sys_yes_no",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:16",
              "updateBy": null,
              "updateTime": null,
              "remark": "通知类型列表",
              "dictId": 7,
              "dictName": "通知类型",
              "dictType": "sys_notice_type",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:16",
              "updateBy": null,
              "updateTime": null,
              "remark": "通知状态列表",
              "dictId": 8,
              "dictName": "通知状态",
              "dictType": "sys_notice_status",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:17",
              "updateBy": null,
              "updateTime": null,
              "remark": "操作类型列表",
              "dictId": 9,
              "dictName": "操作类型",
              "dictType": "sys_oper_type",
              "status": "0"
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:17",
              "updateBy": null,
              "updateTime": null,
              "remark": "登录状态列表",
              "dictId": 10,
              "dictName": "系统状态",
              "dictType": "sys_common_status",
              "status": "0"
          }
      ],
      "code": 200,
      "msg": "查询成功"
    },
  },
  // 其他mock规则...
];

// 查询字典数据详细
// export function getData(dictCode) {
//   return request({
//     url: '/system/dict/data/' + dictCode,
//     method: 'get'
//   })
// }

// 根据字典类型查询字典数据信息
// export function getDicts(dictType) {
//   return request({
//     url: '/system/dict/data/type/' + dictType,
//     method: 'get'
//   })
// }

// // 新增字典数据
// export function addData(data) {
//   return request({
//     url: '/system/dict/data',
//     method: 'post',
//     data: data
//   })
// }

// // 修改字典数据
// export function updateData(data) {
//   return request({
//     url: '/system/dict/data',
//     method: 'put',
//     data: data
//   })
// }

// // 删除字典数据
// export function delData(dictCode) {
//   return request({
//     url: '/system/dict/data/' + dictCode,
//     method: 'delete'
//   })
// }
