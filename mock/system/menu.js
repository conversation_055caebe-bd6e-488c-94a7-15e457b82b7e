const VITE_APP_BASE_API = '/dev-api'
export default [
  { // 查询菜单列表
    url: `${VITE_APP_BASE_API}/system/menu/list`,
    method: 'get',
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:18",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1,
              "menuName": "系统管理",
              "parentName": null,
              "parentId": 0,
              "orderNum": 1,
              "path": "system",
              "component": null,
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "M",
              "visible": "0",
              "status": "0",
              "perms": "",
              "icon": "system",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:19",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 2,
              "menuName": "系统监控",
              "parentName": null,
              "parentId": 0,
              "orderNum": 2,
              "path": "monitor",
              "component": null,
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "M",
              "visible": "0",
              "status": "0",
              "perms": "",
              "icon": "monitor",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:19",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 3,
              "menuName": "系统工具",
              "parentName": null,
              "parentId": 0,
              "orderNum": 3,
              "path": "tool",
              "component": null,
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "M",
              "visible": "0",
              "status": "0",
              "perms": "",
              "icon": "tool",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:19",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 4,
              "menuName": "若依官网",
              "parentName": null,
              "parentId": 0,
              "orderNum": 4,
              "path": "http://ruoyi.vip",
              "component": null,
              "query": "",
              "routeName": "",
              "isFrame": "0",
              "isCache": "0",
              "menuType": "M",
              "visible": "0",
              "status": "0",
              "perms": "",
              "icon": "guide",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:19",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 100,
              "menuName": "用户管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 1,
              "path": "user",
              "component": "system/user/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:user:list",
              "icon": "user",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:20",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 101,
              "menuName": "角色管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 2,
              "path": "role",
              "component": "system/role/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:role:list",
              "icon": "peoples",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:20",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 102,
              "menuName": "菜单管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 3,
              "path": "menu",
              "component": "system/menu/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:menu:list",
              "icon": "tree-table",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:20",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 103,
              "menuName": "部门管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 4,
              "path": "dept",
              "component": "system/dept/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:dept:list",
              "icon": "tree",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:20",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 104,
              "menuName": "岗位管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 5,
              "path": "post",
              "component": "system/post/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:post:list",
              "icon": "post",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:21",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 105,
              "menuName": "字典管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 6,
              "path": "dict",
              "component": "system/dict/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:dict:list",
              "icon": "dict",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:21",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 106,
              "menuName": "参数设置",
              "parentName": null,
              "parentId": 1,
              "orderNum": 7,
              "path": "config",
              "component": "system/config/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:config:list",
              "icon": "edit",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:21",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 107,
              "menuName": "通知公告",
              "parentName": null,
              "parentId": 1,
              "orderNum": 8,
              "path": "notice",
              "component": "system/notice/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "system:notice:list",
              "icon": "message",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:21",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 108,
              "menuName": "日志管理",
              "parentName": null,
              "parentId": 1,
              "orderNum": 9,
              "path": "log",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "M",
              "visible": "0",
              "status": "0",
              "perms": "",
              "icon": "log",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:22",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 109,
              "menuName": "在线用户",
              "parentName": null,
              "parentId": 2,
              "orderNum": 1,
              "path": "online",
              "component": "monitor/online/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:online:list",
              "icon": "online",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:22",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 110,
              "menuName": "定时任务",
              "parentName": null,
              "parentId": 2,
              "orderNum": 2,
              "path": "job",
              "component": "monitor/job/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:list",
              "icon": "job",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:22",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 111,
              "menuName": "数据监控",
              "parentName": null,
              "parentId": 2,
              "orderNum": 3,
              "path": "druid",
              "component": "monitor/druid/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:druid:list",
              "icon": "druid",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:22",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 112,
              "menuName": "服务监控",
              "parentName": null,
              "parentId": 2,
              "orderNum": 4,
              "path": "server",
              "component": "monitor/server/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:server:list",
              "icon": "server",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:23",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 113,
              "menuName": "缓存监控",
              "parentName": null,
              "parentId": 2,
              "orderNum": 5,
              "path": "cache",
              "component": "monitor/cache/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:cache:list",
              "icon": "redis",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:23",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 114,
              "menuName": "缓存列表",
              "parentName": null,
              "parentId": 2,
              "orderNum": 6,
              "path": "cacheList",
              "component": "monitor/cache/list",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:cache:list",
              "icon": "redis-list",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:23",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 115,
              "menuName": "表单构建",
              "parentName": null,
              "parentId": 3,
              "orderNum": 1,
              "path": "build",
              "component": "tool/build/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "tool:build:list",
              "icon": "build",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:23",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 116,
              "menuName": "代码生成",
              "parentName": null,
              "parentId": 3,
              "orderNum": 2,
              "path": "gen",
              "component": "tool/gen/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:list",
              "icon": "code",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:23",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 117,
              "menuName": "系统接口",
              "parentName": null,
              "parentId": 3,
              "orderNum": 3,
              "path": "swagger",
              "component": "tool/swagger/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "tool:swagger:list",
              "icon": "swagger",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:24",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1000,
              "menuName": "用户查询",
              "parentName": null,
              "parentId": 100,
              "orderNum": 1,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:24",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1001,
              "menuName": "用户新增",
              "parentName": null,
              "parentId": 100,
              "orderNum": 2,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:25",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1002,
              "menuName": "用户修改",
              "parentName": null,
              "parentId": 100,
              "orderNum": 3,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:25",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1003,
              "menuName": "用户删除",
              "parentName": null,
              "parentId": 100,
              "orderNum": 4,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:25",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1004,
              "menuName": "用户导出",
              "parentName": null,
              "parentId": 100,
              "orderNum": 5,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:25",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1005,
              "menuName": "用户导入",
              "parentName": null,
              "parentId": 100,
              "orderNum": 6,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:import",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:26",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1006,
              "menuName": "重置密码",
              "parentName": null,
              "parentId": 100,
              "orderNum": 7,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:user:resetPwd",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:26",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1007,
              "menuName": "角色查询",
              "parentName": null,
              "parentId": 101,
              "orderNum": 1,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:role:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:26",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1008,
              "menuName": "角色新增",
              "parentName": null,
              "parentId": 101,
              "orderNum": 2,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:role:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:26",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1009,
              "menuName": "角色修改",
              "parentName": null,
              "parentId": 101,
              "orderNum": 3,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:role:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:27",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1010,
              "menuName": "角色删除",
              "parentName": null,
              "parentId": 101,
              "orderNum": 4,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:role:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:27",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1011,
              "menuName": "角色导出",
              "parentName": null,
              "parentId": 101,
              "orderNum": 5,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:role:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:27",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1012,
              "menuName": "菜单查询",
              "parentName": null,
              "parentId": 102,
              "orderNum": 1,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:menu:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:27",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1013,
              "menuName": "菜单新增",
              "parentName": null,
              "parentId": 102,
              "orderNum": 2,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:menu:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:28",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1014,
              "menuName": "菜单修改",
              "parentName": null,
              "parentId": 102,
              "orderNum": 3,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:menu:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:28",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1015,
              "menuName": "菜单删除",
              "parentName": null,
              "parentId": 102,
              "orderNum": 4,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:menu:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:28",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1016,
              "menuName": "部门查询",
              "parentName": null,
              "parentId": 103,
              "orderNum": 1,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dept:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:28",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1017,
              "menuName": "部门新增",
              "parentName": null,
              "parentId": 103,
              "orderNum": 2,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dept:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:29",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1018,
              "menuName": "部门修改",
              "parentName": null,
              "parentId": 103,
              "orderNum": 3,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dept:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:29",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1019,
              "menuName": "部门删除",
              "parentName": null,
              "parentId": 103,
              "orderNum": 4,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dept:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:29",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1020,
              "menuName": "岗位查询",
              "parentName": null,
              "parentId": 104,
              "orderNum": 1,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:post:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:29",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1021,
              "menuName": "岗位新增",
              "parentName": null,
              "parentId": 104,
              "orderNum": 2,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:post:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:30",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1022,
              "menuName": "岗位修改",
              "parentName": null,
              "parentId": 104,
              "orderNum": 3,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:post:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:30",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1023,
              "menuName": "岗位删除",
              "parentName": null,
              "parentId": 104,
              "orderNum": 4,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:post:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:30",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1024,
              "menuName": "岗位导出",
              "parentName": null,
              "parentId": 104,
              "orderNum": 5,
              "path": "",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:post:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:30",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1025,
              "menuName": "字典查询",
              "parentName": null,
              "parentId": 105,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dict:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:31",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1026,
              "menuName": "字典新增",
              "parentName": null,
              "parentId": 105,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dict:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:31",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1027,
              "menuName": "字典修改",
              "parentName": null,
              "parentId": 105,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dict:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:31",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1028,
              "menuName": "字典删除",
              "parentName": null,
              "parentId": 105,
              "orderNum": 4,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dict:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:31",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1029,
              "menuName": "字典导出",
              "parentName": null,
              "parentId": 105,
              "orderNum": 5,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:dict:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:32",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1030,
              "menuName": "参数查询",
              "parentName": null,
              "parentId": 106,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:config:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:32",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1031,
              "menuName": "参数新增",
              "parentName": null,
              "parentId": 106,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:config:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:32",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1032,
              "menuName": "参数修改",
              "parentName": null,
              "parentId": 106,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:config:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:32",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1033,
              "menuName": "参数删除",
              "parentName": null,
              "parentId": 106,
              "orderNum": 4,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:config:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:33",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1034,
              "menuName": "参数导出",
              "parentName": null,
              "parentId": 106,
              "orderNum": 5,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:config:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:33",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1035,
              "menuName": "公告查询",
              "parentName": null,
              "parentId": 107,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:notice:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:33",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1036,
              "menuName": "公告新增",
              "parentName": null,
              "parentId": 107,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:notice:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:33",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1037,
              "menuName": "公告修改",
              "parentName": null,
              "parentId": 107,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:notice:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:34",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1038,
              "menuName": "公告删除",
              "parentName": null,
              "parentId": 107,
              "orderNum": 4,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "system:notice:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:24",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 500,
              "menuName": "操作日志",
              "parentName": null,
              "parentId": 108,
              "orderNum": 1,
              "path": "operlog",
              "component": "monitor/operlog/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:operlog:list",
              "icon": "form",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:24",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 501,
              "menuName": "登录日志",
              "parentName": null,
              "parentId": 108,
              "orderNum": 2,
              "path": "logininfor",
              "component": "monitor/logininfor/index",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "C",
              "visible": "0",
              "status": "0",
              "perms": "monitor:logininfor:list",
              "icon": "logininfor",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:36",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1046,
              "menuName": "在线查询",
              "parentName": null,
              "parentId": 109,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:online:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:36",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1047,
              "menuName": "批量强退",
              "parentName": null,
              "parentId": 109,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:online:batchLogout",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:36",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1048,
              "menuName": "单条强退",
              "parentName": null,
              "parentId": 109,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:online:forceLogout",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:36",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1049,
              "menuName": "任务查询",
              "parentName": null,
              "parentId": 110,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:36",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1050,
              "menuName": "任务新增",
              "parentName": null,
              "parentId": 110,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:add",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:37",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1051,
              "menuName": "任务修改",
              "parentName": null,
              "parentId": 110,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:37",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1052,
              "menuName": "任务删除",
              "parentName": null,
              "parentId": 110,
              "orderNum": 4,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:37",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1053,
              "menuName": "状态修改",
              "parentName": null,
              "parentId": 110,
              "orderNum": 5,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:changeStatus",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:37",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1054,
              "menuName": "任务导出",
              "parentName": null,
              "parentId": 110,
              "orderNum": 6,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:job:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:38",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1055,
              "menuName": "生成查询",
              "parentName": null,
              "parentId": 116,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:38",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1056,
              "menuName": "生成修改",
              "parentName": null,
              "parentId": 116,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:edit",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:38",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1057,
              "menuName": "生成删除",
              "parentName": null,
              "parentId": 116,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:38",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1058,
              "menuName": "导入代码",
              "parentName": null,
              "parentId": 116,
              "orderNum": 4,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:import",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:39",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1059,
              "menuName": "预览代码",
              "parentName": null,
              "parentId": 116,
              "orderNum": 5,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:preview",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:39",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1060,
              "menuName": "生成代码",
              "parentName": null,
              "parentId": 116,
              "orderNum": 6,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "tool:gen:code",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:34",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1039,
              "menuName": "操作查询",
              "parentName": null,
              "parentId": 500,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:operlog:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:34",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1040,
              "menuName": "操作删除",
              "parentName": null,
              "parentId": 500,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:operlog:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:34",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1041,
              "menuName": "日志导出",
              "parentName": null,
              "parentId": 500,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:operlog:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:35",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1042,
              "menuName": "登录查询",
              "parentName": null,
              "parentId": 501,
              "orderNum": 1,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:logininfor:query",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:35",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1043,
              "menuName": "登录删除",
              "parentName": null,
              "parentId": 501,
              "orderNum": 2,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:logininfor:remove",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:35",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1044,
              "menuName": "日志导出",
              "parentName": null,
              "parentId": 501,
              "orderNum": 3,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:logininfor:export",
              "icon": "#",
              "children": []
          },
          {
              "createBy": null,
              "createTime": "2024-06-30 11:27:35",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "menuId": 1045,
              "menuName": "账户解锁",
              "parentName": null,
              "parentId": 501,
              "orderNum": 4,
              "path": "#",
              "component": "",
              "query": "",
              "routeName": "",
              "isFrame": "1",
              "isCache": "0",
              "menuType": "F",
              "visible": "0",
              "status": "0",
              "perms": "monitor:logininfor:unlock",
              "icon": "#",
              "children": []
          }
      ]
  },
  },
  // 其他mock规则...
]; 



// // 查询菜单详细
// export function getMenu(menuId) {
//   return request({
//     url: '/system/menu/' + menuId,
//     method: 'get'
//   })
// }

// // 查询菜单下拉树结构
// export function treeselect() {
//   return request({
//     url: '/system/menu/treeselect',
//     method: 'get'
//   })
// }

// // 根据角色ID查询菜单下拉树结构
// export function roleMenuTreeselect(roleId) {
//   return request({
//     url: '/system/menu/roleMenuTreeselect/' + roleId,
//     method: 'get'
//   })
// }

// // 新增菜单
// export function addMenu(data) {
//   return request({
//     url: '/system/menu',
//     method: 'post',
//     data: data
//   })
// }

// // 修改菜单
// export function updateMenu(data) {
//   return request({
//     url: '/system/menu',
//     method: 'put',
//     data: data
//   })
// }

// // 删除菜单
// export function delMenu(menuId) {
//   return request({
//     url: '/system/menu/' + menuId,
//     method: 'delete'
//   })
// }