const VITE_APP_BASE_API = "/dev-api";
export default [
  {
    url: `${VITE_APP_BASE_API}/system/dept/list`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:07",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 100,
              "parentId": 0,
              "ancestors": "0",
              "deptName": "若依科技",
              "orderNum": 0,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:07",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 101,
              "parentId": 100,
              "ancestors": "0,100",
              "deptName": "深圳总公司",
              "orderNum": 1,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:07",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 102,
              "parentId": 100,
              "ancestors": "0,100",
              "deptName": "长沙分公司",
              "orderNum": 2,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:08",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 103,
              "parentId": 101,
              "ancestors": "0,100,101",
              "deptName": "研发部门",
              "orderNum": 1,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:08",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 104,
              "parentId": 101,
              "ancestors": "0,100,101",
              "deptName": "市场部门",
              "orderNum": 2,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:08",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 105,
              "parentId": 101,
              "ancestors": "0,100,101",
              "deptName": "测试部门",
              "orderNum": 3,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:08",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 106,
              "parentId": 101,
              "ancestors": "0,100,101",
              "deptName": "财务部门",
              "orderNum": 4,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:09",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 107,
              "parentId": 101,
              "ancestors": "0,100,101",
              "deptName": "运维部门",
              "orderNum": 5,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:09",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 108,
              "parentId": 102,
              "ancestors": "0,100,102",
              "deptName": "市场部门",
              "orderNum": 1,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:27:09",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "deptId": 109,
              "parentId": 102,
              "ancestors": "0,100,102",
              "deptName": "财务部门",
              "orderNum": 2,
              "leader": "若依",
              "phone": "15888888888",
              "email": "<EMAIL>",
              "status": "0",
              "delFlag": "0",
              "parentName": null,
              "children": []
          }
      ]
  },
  },
  
  // 其他mock规则...
];

