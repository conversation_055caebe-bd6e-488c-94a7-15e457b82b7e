const VITE_APP_BASE_API = "/dev-api";
export default [
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_notice_status`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:24",
              "updateBy": null,
              "updateTime": null,
              "remark": "正常状态",
              "dictCode": 16,
              "dictSort": 1,
              "dictLabel": "正常",
              "dictValue": "0",
              "dictType": "sys_notice_status",
              "cssClass": "",
              "listClass": "primary",
              "isDefault": "Y",
              "status": "0",
              "default": true
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:24",
              "updateBy": null,
              "updateTime": null,
              "remark": "关闭状态",
              "dictCode": 17,
              "dictSort": 2,
              "dictLabel": "关闭",
              "dictValue": "1",
              "dictType": "sys_notice_status",
              "cssClass": "",
              "listClass": "danger",
              "isDefault": "N",
              "status": "0",
              "default": false
          }
      ]
    },
  },
  {
    url: `${VITE_APP_BASE_API}/system/dict/data/type/sys_notice_type`,
    method: "get",
    response: {
      "msg": "操作成功",
      "code": 200,
      "data": [
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:23",
              "updateBy": null,
              "updateTime": null,
              "remark": "通知",
              "dictCode": 14,
              "dictSort": 1,
              "dictLabel": "通知",
              "dictValue": "1",
              "dictType": "sys_notice_type",
              "cssClass": "",
              "listClass": "warning",
              "isDefault": "Y",
              "status": "0",
              "default": true
          },
          {
              "createBy": "admin",
              "createTime": "2024-06-30 11:28:23",
              "updateBy": null,
              "updateTime": null,
              "remark": "公告",
              "dictCode": 15,
              "dictSort": 2,
              "dictLabel": "公告",
              "dictValue": "2",
              "dictType": "sys_notice_type",
              "cssClass": "",
              "listClass": "success",
              "isDefault": "N",
              "status": "0",
              "default": false
          }
        ]
      },
    },
    {
      url: `${VITE_APP_BASE_API}/system/notice/list`,
      method: "get",
      response: {
        "total": 2,
        "rows": [
            {
                "createBy": "admin",
                "createTime": "2024-06-30 11:28:39",
                "updateBy": "",
                "updateTime": null,
                "remark": "管理员",
                "noticeId": 1,
                "noticeTitle": "温馨提醒：2018-07-01 若依新版本发布啦",
                "noticeType": "2",
                "noticeContent": "新版本内容",
                "status": "0"
            },
            {
                "createBy": "admin",
                "createTime": "2024-06-30 11:28:40",
                "updateBy": "",
                "updateTime": null,
                "remark": "管理员",
                "noticeId": 2,
                "noticeTitle": "维护通知：2018-07-01 若依系统凌晨维护",
                "noticeType": "1",
                "noticeContent": "维护内容",
                "status": "0"
            }
        ],
        "code": 200,
        "msg": "查询成功"
    },
      },
  // 其他mock规则...
];

