const VITE_APP_BASE_API = '/dev-api'
export default [
  
  // 竞争对手-玉柴-新闻列表-查询
  {
    url: `/dev-api/rival/yuchai/news/list`,
    method: 'get',
    response: {
      "total": 2,
      "rows": [
          {
              "text": '财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
              "date": '2024-12-13',
              "content":'昨天闭幕的中央经济工作会议指出，明年要实施更加积极的财政政策、适度宽松的货币政策。这些政策背后释放出怎样的信号？财政政策将更加积极策将适度宽松在财政政策方面，中央经济工作会议要求，明年要实施更加积极的财政政策，提高财政赤字率，增加发行超长期特别国债，增加地方政府专项债券发行，使用优化财政支出结构，兜牢基层“三保”底线。专家表示，这些要求明确了财政政策“更加积极有为”的落地路径。',
              "id": '0',
          },{
            "text": '财政货币政策调整有何深意？“两新”政策有何利好？专家解读2',
            "date": '2024-12-14',
            "content":'昨天闭幕的中央经济工作会议指出，明年要实施更加积极的财政政策、适度宽松的货币政策。这些政策背后释放出怎样的信号？财政政策将更加积极策将适度宽松在财政政策方面，中央经济工作会议要求，明年要实施更加积极的财政政策，提高财政赤字率，增加发行超长期特别国债，增加地方政府专项债券发行，使用优化财政支出结构，兜牢基层“三保”底线。专家表示，这些要求明确了财政政策“更加积极有为”的落地路径。',
            "id": '1',
          },
          {
            "text": '财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
            "date": '2024-12-13',
            "content":'昨天闭幕的中央经济工作会议指出，明年要实施更加积极的财政政策、适度宽松的货币政策。这些政策背后释放出怎样的信号？财政政策将更加积极策将适度宽松在财政政策方面，中央经济工作会议要求，明年要实施更加积极的财政政策，提高财政赤字率，增加发行超长期特别国债，增加地方政府专项债券发行，使用优化财政支出结构，兜牢基层“三保”底线。专家表示，这些要求明确了财政政策“更加积极有为”的落地路径。',
            "id": '0',
        },{
          "text": '财政货币政策调整有何深意？“两新”政策有何利好？专家解读2',
          "date": '2024-12-14',
          "content":'昨天闭幕的中央经济工作会议指出，明年要实施更加积极的财政政策、适度宽松的货币政策。这些政策背后释放出怎样的信号？财政政策将更加积极策将适度宽松在财政政策方面，中央经济工作会议要求，明年要实施更加积极的财政政策，提高财政赤字率，增加发行超长期特别国债，增加地方政府专项债券发行，使用优化财政支出结构，兜牢基层“三保”底线。专家表示，这些要求明确了财政政策“更加积极有为”的落地路径。',
          "id": '1',
        },{
          "text": '财政货币政策调整有何深意？“两新”政策有何利好？专家解读1',
          "date": '2024-12-13',
          "content":'昨天闭幕的中央经济工作会议指出，明年要实施更加积极的财政政策、适度宽松的货币政策。这些政策背后释放出怎样的信号？财政政策将更加积极策将适度宽松在财政政策方面，中央经济工作会议要求，明年要实施更加积极的财政政策，提高财政赤字率，增加发行超长期特别国债，增加地方政府专项债券发行，使用优化财政支出结构，兜牢基层“三保”底线。专家表示，这些要求明确了财政政策“更加积极有为”的落地路径。',
          "id": '0',
      },{
        "text": '财政货币政策调整有何深意？“两新”政策有何利好？专家解读2',
        "date": '2024-12-14',
        "content":'昨天闭幕的中央经济工作会议指出，明年要实施更加积极的财政政策、适度宽松的货币政策。这些政策背后释放出怎样的信号？财政政策将更加积极策将适度宽松在财政政策方面，中央经济工作会议要求，明年要实施更加积极的财政政策，提高财政赤字率，增加发行超长期特别国债，增加地方政府专项债券发行，使用优化财政支出结构，兜牢基层“三保”底线。专家表示，这些要求明确了财政政策“更加积极有为”的落地路径。',
        "id": '1',
      }
      ],
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-玉柴-报告区-查询
  {
    url: `/dev-api/rival/yuchai/report/list`,
    method: 'get',
    response: {
      "total": 2,
      "rows": [
          {
              "text": '玉柴1',
              "pdfPath": 'shfjhsg2y55.pdf',
              id:'0'
          },{
            "text": '玉柴2',
            "pdfPath": 'shfjhsg2y35.pdf',
            "id": '1',
          }
      ],
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-玉柴-图表-查询
  {
    url: `/dev-api/rival/yuchai/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '玉柴TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [4987, 17109, 12566, 4827, 6656, 11384, 13088, 59610, 15232, 29645],
            itemStyle:{
              color:'#44546a'
            },
            label: {
              show: true, // 显示值
              position: 'right', // 值的位置，可以是 'top', 'left', 'right', 'bottom', 'inside', 或者 'insideLeft'
              // 可以通过 formatter 自定义显示格式
            }
          },
          {
            name: '玉柴销量',
            type: 'bar',
            data: [2800, 8000, 4876, 4009, 4563, 5432, 61.3, 18.5, 79.0, 61.4],
            itemStyle:{
              color:'#2e75b6'
            },
            label: {
              show: true, // 显示值
              position: 'right', // 值的位置，可以是 'top', 'left', 'right', 'bottom', 'inside', 或者 'insideLeft'
              // 可以通过 formatter 自定义显示格式
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-潍柴-图表-查询
  {
    url: `/dev-api/rival/weichai/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '潍柴TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '潍柴销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-康明斯-图表-查询
  {
    url: `/dev-api/rival/kangmingsi/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '康明斯TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '康明斯销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-云内-图表-查询
  {
    url: `/dev-api/rival/yunnei/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '云内TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '云内销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-全柴-图表-查询
  {
    url: `/dev-api/rival/quanchai/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '全柴TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '全柴销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-新柴-图表-查询
  {
    url: `/dev-api/rival/xinchai/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '新柴TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '新柴销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-久保田-图表-查询
  {
    url: `/dev-api/rival/jiubaotian/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '久保田TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '久保田销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-洋马-图表-查询
  {
    url: `/dev-api/rival/yangma/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '洋马TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '洋马销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  },
  // 竞争对手-铂金斯-图表-查询
  {
    url: `/dev-api/rival/bojinsi/echart`,
    method: 'get',
    response: {
      "data": {
        title: {
          text: '铂金斯TOP10客户及份额情况',
          left:'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          right: 50,
          selectedMode: false,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['中联重科', '郑州日产', '江淮重卡', '东风随专', '湖北大运', '陕汽商用车', '东风新疆', '东风股份', '东风华神', '东风柳汽']
        },
        series: [
          {
            name: '厂家销量',
            type: 'bar',
            data: [18203, 23489, 29034, 104970, 131744, 630230, 29034, 104970, 131744, 630230],
            itemStyle:{
              color:'#44546a'
            }
          },
          {
            name: '铂金斯销量',
            type: 'bar',
            data: [19325, 23438, 31000, 121594, 134141, 681807, 31000, 121594, 134141, 681807],
            itemStyle:{
              color:'#2e75b6'
            },
            barGap:'-40%',/*重叠40%*/
          }
        ]
      },
      "code": 200,
      "msg": "查询成功"
    }
  }
];