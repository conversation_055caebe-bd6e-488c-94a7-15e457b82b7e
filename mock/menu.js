const VITE_APP_BASE_API = "/dev-api";
// // 当设置 true 的时候该路由不会在侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
// hidden: true // (默认 false)

// //当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
// redirect: 'noRedirect'

// // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
// // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
// // 若你想不管路由下面的 children 声明的个数都显示你的根路由
// // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
// alwaysShow: true

// name: 'router-name' // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
// query: '{"id": 1, "name": "ry"}'     // 访问路由的默认传递参数
// roles: ['admin', 'common']           // 访问路由的角色权限
// permissions: ['a:a:a', 'b:b:b']      // 访问路由的菜单权限
 
// meta: {
//   title: 'title' // 设置该路由在侧边栏和面包屑中展示的名字
//   icon: 'svg-name' // 设置该路由的图标，支持 svg-class，也支持 el-icon-x element-ui 的 icon
//   noCache: true // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
//   breadcrumb: false //  如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)
//   affix: true // 如果设置为true，它则会固定在tags-view中(默认 false)

//   // 当路由设置了该属性，则会高亮相对应的侧边栏。
//   // 这在某些场景非常有用，比如：一个文章的列表页路由为：/article/list
//   // 点击文章进入文章详情页，这时候路由为/article/1，但你想在侧边栏高亮文章列表的路由，就可以进行如下设置
//   activeMenu: '/article/list'
// }
export default [
	{
    url: `${VITE_APP_BASE_API}/system/menu/getBiRouters`,
    method: 'get',
    response: {
     msg: "操作成功",
      code: 200,
			data: [
				{
          name: "Home",
          path: "/home",
          hidden: false,
          redirect: "/home/<USER>",
          component: "BiLayout", 
          meta: {
            title: "首页",
            icon: "system",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "homeNews",
              path: "news",
              hidden: false,
              component: "home/news/index",
              meta: {
                title: "热点新闻",
                icon: "bi-home-news",
                noCache: true,
                link: null,
              },
						},{
              name: "homeNewsList", // 首页一句话信息列表
              path: "newslist",
              hidden: true,
              component: "home/newslist/index",
              meta: {
                title: "信息快递",
                icon: "",
                noCache: true,
                link: null,
                activeMenu: '/home/<USER>'
              },
						},{
              name: "homeNewsInfo", // 首页信息详情
              path: "newsinfo",
              hidden: true,
              component: "home/newsinfo/index",
              meta: {
                title: "信息详情",
                icon: "",
                noCache: true,
                link: null,
                activeMenu: '/home/<USER>'
              },
						},{
              name: "HomeVibe",
              path: "vibe",
              hidden: false,
              component: "home/vibe/index",
              meta: {
                title: "宏观环境",
                icon: "bi-home-macro",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeEngine",
              path: "engine",
              hidden: false,
              component: "home/engine/index",
              meta: {
                title: "内燃机排名",
                icon: "bi-home-engine",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeInland",
              path: "inland",
              hidden: false,
              component: "home/inland/index",
              meta: {
                title: "国内市场",
                icon: "bi-home-inland",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeEnergy",
              path: "energy",
              hidden: false,
              component: "home/energy/index",
              meta: {
                title: "新能源市场",
                icon: "bi-home-energy",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeOem",
              path: "oem",
              hidden: false,
              component: "home/oem/index",
              meta: {
                title: "OEM排名",
                icon: "bi-home-oem",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeExit",
              path: "exit",
              hidden: false,
              component: "home/exit/index",
              meta: {
                title: "出口分布",
                icon: "bi-home-exit",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeMarketing",
              path: "marketing",
              hidden: false,
              component: "home/marketing/index",
              meta: {
                title: "营销线索",
                icon: "bi-home-marketing",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeScan",
              path: "scan",
              hidden: false,
              component: "home/scan/index",
              meta: {
                title: "异动扫描",
                icon: "bi-home-scan",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeSubscription",
              path: "subscription",
              hidden: false,
              component: "home/subscription/index",
              meta: {
                title: "我的订阅",
                icon: "bi-home-subscription",
                noCache: true,
                link: null,
              },
            },{
              name: "HomeMessage",
              path: "message",
              hidden: false,
              component: "home/message/index",
              meta: {
                title: "信息需求",
                icon: "bi-home-message",
                noCache: true,
                link: null,
              },
            },
          ],
				},{
          name: "Ambience",
          path: "/ambience",
          hidden: false,
          redirect: '/ambience/macro/economy',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "竞争环境",
            icon: "system",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "AmbienceMacro",
              path: "macro",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "宏观环境",
                icon: "bi-ambience-macro",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "AmbienceMacroEconomy",
                  path: "economy",
                  hidden: false,
                  component: "ambience/macro/economy/index",
                  meta: {
                    title: "经济环境",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "AmbienceMacroPolicy",
                  path: "policy",
                  hidden: false,
                  component: "ambience/macro/policy/index",
                  meta: {
                    title: "政策环境",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "AmbienceMacroEstate",
                  path: "estate",
                  hidden: false,
                  component: "ambience/macro/estate/index",
                  meta: {
                    title: "产业环境",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "AmbienceMacroInfo",
                  path: "info",
                  hidden: true,
                  component: "ambience/macro/info/index",
                  meta: {
                    title: "详情",
                    icon: "",
                    noCache: false,
                    link: null,
                    activeMenu: '/ambience/macro/estate'
                  },
                },
              ],
						},{
              name: "AmbienceMarket",
              path: "market",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "市场环境",
                icon: "bi-ambience-market",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "CommercialVehicle",
                  path: "vehicle",
                  hidden: false,
                  component: "ambience/market/vehicle/index",
                  meta: {
                    title: "商用车",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "GeneralMachine",
                  path: "machine",
                  hidden: false,
                  component: "ambience/market/machine/index",
                  meta: {
                    title: "通机",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "ShipElectricity",
                  path: "electricity",
                  hidden: false,
                  component: "ambience/market/electricity/index",
                  meta: {
                    title: "船电",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "NewEnergy",
                  path: "energy",
                  hidden: false,
                  component: "ambience/market/energy/index",
                  meta: {
                    title: "新能源",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "SegmentedPower",
                  path: "power",
                  hidden: false,
                  component: "ambience/market/power/index",
                  meta: {
                    title: "细分动力",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
              ],
						}, 
          ],
				},{
          name: "Rival",
          path: "/rival",
          hidden: false,
          redirect: '/rival/shangyongche/yuchai',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "竞争对手",
            icon: "bi-home-marketing",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "SYCHome",
              path: "shangyongche",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "商用车",
                icon: "bi-home-macro",
                noCache: true,
                link: null,
              },
              children:[
                {
                  name: "RivalHomeYuchaiSyc",
                  path: "yuchai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "玉柴",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeWeichaiSyc",
                  path: "weichai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "潍柴",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeKMS",
                  path: "kangmingsi",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "康明斯",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeYunNei",
                  path: "yunnei",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "云内",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
              ]
            },
            {
              name: "TJHome",
              path: "tongji",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "通机",
                icon: "job",
                noCache: true,
                link: null,
              },
              children:[
                {
                  name: "RivalHomeYuchaiTj",
                  path: "yuchai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "玉柴",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeWeichaiTj",
                  path: "weichai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "潍柴",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
              ]
            }
            ,
            {
              name: "ShipHome",
              path: "chuandian",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "船电",
                icon: "job",
                noCache: true,
                link: null,
              },
              children:[
                {
                  name: "RivalHomeYuchaiShiphc",
                  path: "yuchai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "潍柴",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeWeichaiShipkms",
                  path: "weichai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "康明斯",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
              ]
            },
            {
              name: "newpowerHome",
              path: "newPower",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "新能源",
                icon: "job",
                noCache: true,
                link: null,
              },
              children:[
                {
                  name: "RivalHomeYuchainewpowerwc",
                  path: "yuchai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "潍柴",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeWeichainewpowerksm",
                  path: "weichai",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "康明斯",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "RivalHomeWeichainewpowermn",
                  path: "yunnei",
                  hidden: false,
                  component: "rival/home/<USER>/index",
                  meta: {
                    title: "云内",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
              ]
            }
            
            
          ]
          // children: [
          //   {
          //     name: "RivalHome",
          //     path: "home",
          //     hidden: false,
          //     component: "ParentView", 
          //     redirect: 'noRedirect',
          //     alwaysShow: true,
          //     meta: {
          //       title: "竞争对手",
          //       icon: "bi-home-macro",
          //       noCache: true,
          //       link: null,
          //     },
              
              // children: [
              //   {
              //     name: "RivalHomeYuchai",
              //     path: "yuchai",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "玉柴",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeWeichai",
              //     path: "weichai",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "潍柴",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeKMS",
              //     path: "kangmingsi",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "康明斯",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeYunNei",
              //     path: "yunnei",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "云内",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeQuanchai",
              //     path: "quanchai",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "全柴",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeXinchai",
              //     path: "xinchai",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "新柴",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeJiubaotian",
              //     path: "jiubaotian",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "久保田",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeYangma",
              //     path: "yangma",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "洋马",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   },
              //   {
              //     name: "RivalHomeBojinsi",
              //     path: "bojinsi",
              //     hidden: false,
              //     component: "rival/home/<USER>/index",
              //     meta: {
              //       title: "铂金斯",
              //       icon: "",
              //       noCache: false,
              //       link: null,
              //     },
              //   }
              // ],
					// 	} 
          // ],
				},{
          name: "Machine",
          path: "/machine",
          hidden: false,
          redirect: '/machine/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "主机厂客户",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "MachineIntel",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "客户情报",
                icon: "bi-home-macro",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "MachineIntelCar",
                  path: "car",
                  hidden: false,
                  component: "machine/intel/car/index",
                  meta: {
                    title: "商用车",
                    icon: "",
                    noCache: false,
                    link: null,
                    activeMenu:''
                  },
                },
              
                // {
                //   name: "MachineIntelCar9",
                //   path: "morenews",
                //   hidden: false,
                //   component: "machine/intel/car/morenews",
                //   meta: {
                //     title: "详情",
                //     icon: "",
                //     noCache: false,
                //     link: null,
                //     activeMenu: 'machine/intel/car/morenews'
                //   },
                // },
                
                
                
               
                {
                  name: "MachineIntelMechanical",
                  path: "mechanical",
                  hidden: false,
                  component: "machine/intel/mechanical/index",
                  meta: {
                    title: "工程机械",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "MachineIntelAgro",
                  path: "agro",
                  hidden: false,
                  component: "machine/intel/agro/index",
                  meta: {
                    title: "农业装备",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "MachineIntelShip",
                  path: "ship",
                  hidden: false,
                  component: "machine/intel/ship/index",
                  meta: {
                    title: "船舶",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "MachineIntelPower",
                  path: "power",
                  hidden: false,
                  component: "machine/intel/power/index",
                  meta: {
                    title: "发电动力",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "MachineIntelEnrgy",
                  path: "energy",
                  hidden: false,
                  component: "machine/intel/energy/index",
                  meta: {
                    title: "新能源",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
              ],
						} 
          ],
				},
        
        {
          name: "Enduser", // 开发订正自己的路由
          path: "/enduser",
          hidden: false,
          redirect: '/enduser/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "终端客户",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "EnduserIntel",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "客户情报",
                icon: "bi-home-macro",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "EnduserIntelCar",
                  path: "car",
                  hidden: false,
                  component: "enduser/intel/car/index",
                  meta: {
                    title: "商用车",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "EnduserIntelMechanical",
                  path: "mechanical",
                  hidden: false,
                  component: "enduser/intel/mechanical/index",
                  meta: {
                    title: "工程机械",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "EnduserIntelAgro",
                  path: "agro",
                  hidden: false,
                  component: "enduser/intel/agro/index",
                  meta: {
                    title: "农业装备",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "EnduserIntelShip",
                  path: "ship",
                  hidden: false,
                  component: "enduser/intel/ship/index",
                  meta: {
                    title: "船舶",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "EnduserIntelPower",
                  path: "power",
                  hidden: false,
                  component: "enduser/intel/power/index",
                  meta: {
                    title: "发电动力",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "EnduserIntelEnergy",
                  path: "energy",
                  hidden: false,
                  component: "enduser/intel/energy/index",
                  meta: {
                    title: "新能源",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                
                
              ],
						} 
          ],
				},
        
        {
          name: "Abroad", // 开发订正自己的路由
          path: "/abroad",
          hidden: false,
          redirect: '/abroad/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "海外客户",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "AbroadIntel2",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "客户情报",
                icon: "bi-home-macro",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "AbroadIntelCar2",
                  path: "car",
                  hidden: false,
                  component: "abroad/intel/car/index",
                  meta: {
                    title: "商用车",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "AbroadIntelmechanical",
                  path: "mechanical",
                  hidden: false,
                  component: "abroad/intel/mechanical/index",
                  meta: {
                    title: "工程机械",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "AbroadIntelAgro2",
                  path: "agro",
                  hidden: false,
                  component: "abroad/intel/agro/index",
                  meta: {
                    title: "农业装备",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "AbroadIntelShip",
                  path: "ship",
                  hidden: false,
                  component: "abroad/intel/ship/index",
                  meta: {
                    title: "船舶",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "AbroadIntelPower",
                  path: "power",
                  hidden: false,
                  component: "abroad/intel/power/index",
                  meta: {
                    title: "发电动力",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "AbroadIntelEnergy",
                  path: "energy",
                  hidden: false,
                  component: "abroad/intel/energy/index",
                  meta: {
                    title: "新能源",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                
              ],
						} 
          ],
				},{
          name: "Machine3", // 开发订正自己的路由
          path: "/machine",
          hidden: false,
          redirect: '/machine/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "技术情报",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "MachineIntel3",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "新能源",
                icon: "",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "MachineIntelCar3",
                  path: "car",
                  hidden: false,
                  component: "machine/intel/car/index",
                  meta: {
                    title: "商用车",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                }
              ],
						} 
          ],
				},{
          name: "Machine4", // 开发订正自己的路由
          path: "/machine",
          hidden: false,
          redirect: '/machine/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "展会情报",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "MachineIntel4",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "展会情报",
                icon: "",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "MachineIntelCar4",
                  path: "car",
                  hidden: false,
                  component: "machine/intel/car/index",
                  meta: {
                    title: "商务年会",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                }
              ],
						} 
          ],
				},{
          name: "Machine5", // 开发订正自己的路由
          path: "/machine",
          hidden: false,
          redirect: '/machine/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "情报应用",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "MachineIntel5",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "报告生成",
                icon: "",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "MachineIntelCar5",
                  path: "car",
                  hidden: false,
                  component: "machine/intel/car/index",
                  meta: {
                    title: "情报季报",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                }
              ],
						} 
          ],
				},{
          name: "Batabase", // 开发订正自己的路由
          path: "/database",
          hidden: false,
          redirect: '/database/cooperate/business',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "数据管理",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "Batabaseupload",
              path: "upload",
              hidden: false,
              component: "database/upload/index",
              alwaysShow: true,
              meta: {
                title: "报告上传",
                icon: "bi-ambience-macro",
                noCache: true,
                link: null,
              }
						} ,
            {
              name: "BatabaseCooperate",
              path: "cooperate",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "数据库管理",
                icon: "bi-ambience-macro",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "BatabaseCooperateBusiness",
                  path: "business",
                  hidden: false,
                  component: "database/cooperate/business/index",
                  meta: {
                    title: "竞争对手",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                },
                // {
                //   name: "BatabaseCooperateNotice",
                //   path: "notice",
                //   hidden: false,
                //   component: "database/cooperate/notice/index",
                //   meta: {
                //     title: "公告数据",
                //     icon: "",
                //     noCache: false,
                //     link: null,
                //   },
                // }
              ],
						} ,
            
          ],
          

				},{
          name: "System", // 开发订正自己的路由
          path: "/system",
          hidden: false,
          redirect: '/system/user',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "后台管理",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "SystemHome",
              path: "/system",
              hidden: false,
              redirect: 'noRedirect',
              component: "ParentView", 
              alwaysShow: true,
              meta: {
                title: "后台管理",
                icon: "system",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "systemUser",
                  path: "user",
                  hidden: false,
                  component: "system/user/index",
                  meta: {
                    title: "用户管理",
                    icon: "user",
                    noCache: false,
                    link: null,
                  }
                },
                {
                  name: "authUser",
                  path: "user-auth/role/:userId",
                  hidden: true,
                  component: "system/user/authRole",
                  meta: {
                    title: "角色分配",
                    icon: "",
                    noCache: true,
                    link: null,
                    activeMenu: '/system/user'
                  }
                },
                ,{
                  name: "systemRole",
                  path: "role",
                  hidden: false,
                  component: "system/role/index",
                  meta: {
                    title: "角色管理",
                    icon: "peoples",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "authUserRole",
                  path: "role-auth/user/:roleId",
                  hidden: true,
                  component: "system/role/authUser",
                  meta: {
                    title: "分配用户",
                    icon: "",
                    noCache: true,
                    link: null,
                    activeMenu: '/system/role'
                  }
                },{
                  name: "systemMenu",
                  path: "menu",
                  hidden: false,
                  component: "system/menu/index",
                  meta: {
                    title: "菜单管理",
                    icon: "tree-table",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "systemDept",
                  path: "dept",
                  hidden: false,
                  component: "system/dept/index",
                  meta: {
                    title: "部门管理",
                    icon: "tree",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "systemPost",
                  path: "post",
                  hidden: false,
                  component: "system/post/index",
                  meta: {
                    title: "岗位管理",
                    icon: "post",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "systemDict",
                  path: "dict",
                  hidden: false,
                  component: "system/dict/index",
                  meta: {
                    title: "字典管理",
                    icon: "dict",
                    noCache: false,
                    link: null,
                  },
                },
                {
                  name: "systemConfig",
                  path: "config",
                  hidden: false,
                  component: "system/config/index",
                  meta: {
                    title: "参数设置",
                    icon: "edit",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "systemNotice",
                  path: "notice",
                  hidden: false,
                  component: "system/notice/index",
                  meta: {
                    title: "通知公告",
                    icon: "message",
                    noCache: false,
                    link: null,
                  },
                },{
                  name: "systemLog",
                  path: "log",
                  hidden: false,
                  redirect: "noRedirect",
                  component: "ParentView",
                  alwaysShow: true,
                  meta: {
                    title: "日志管理",
                    icon: "log", 
                    noCache: false,
                    link: null,
                  },
                  children: [
                    {
                      name: "systemOperlog",
                      path: "operlog",
                      hidden: false,
                      component: "system/operlog/index",
                      meta: {
                        title: "操作日志",
                        icon: "form",
                        noCache: false,
                        link: null,
                      },
                    },
                    {
                      name: "systemLogininfor",
                      path: "logininfor",
                      hidden: false,
                      component: "system/logininfor/index",
                      meta: {
                        title: "登录日志",
                        icon: "logininfor",
                        noCache: false,
                        link: null,
                      },
                    },
                  ],
                },
              ],
						} 
          ],
				},{
          name: "Machine7", // 开发订正自己的路由
          path: "/machine",
          hidden: false,
          redirect: '/machine/intel/car',
          component: "BiLayout",
          alwaysShow: true,
          meta: {
            title: "我的订阅",
            icon: "",
            noCache: true,
            link: null,
          },
          children: [
            {
              name: "MachineIntel7",
              path: "intel",
              hidden: false,
              component: "ParentView", 
              redirect: 'noRedirect',
              alwaysShow: true,
              meta: {
                title: "我的订阅",
                icon: "",
                noCache: true,
                link: null,
              },
              children: [
                {
                  name: "MachineIntelCar7",
                  path: "car",
                  hidden: false,
                  component: "machine/intel/car/index",
                  meta: {
                    title: "我的订阅",
                    icon: "",
                    noCache: false,
                    link: null,
                  },
                }
              ],
						} 
          ],
				}
			]
    }
  }, 
  // 其他mock规则...
];
